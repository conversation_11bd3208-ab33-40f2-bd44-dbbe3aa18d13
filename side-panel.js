(() => {
    H2 = {};
  qw(H2, { default: () => Ld });
  var Hw = q2(G2());
  Gw(H2, q2(G2()));
  var { default: U2, ...Vw } = Hw,
    Ld = U2 !== void 0 ? U2 : Vw;
  var vee = `${ge}-new-user-guide`;
  var kee = `${ge}-new-user-guide`;
  var qee = `${ge}-new-user-guide`;
  var Jee = `${ge}-new-user-guide`;
  var yte = `${ge}-new-user-guide`;
  var Vt = `${ge}-new-user-guide`,
    Kw = `
    .${Vt}-container {
      width: 276px;
      height: 224px;
      z-index: ${Uo};
    }

    .${Vt}-close-icon {
      position: absolute;
      top: -20px;
      right: 20px;
      z-index: 1;
      cursor: pointer;
    }

    .${Vt}-bg {
      position: absolute;
      left: 0;
      right: 0;
      top: 0;
      bottom: 0;
      z-index: -1;
      height: 100%;
      width: 100%;
    }

    .${Vt}-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      height: 100%;
    }


    .${Vt}-img {
      width: 192px;
      height: 112px;
      margin-top: -20px;
    }

    .${Vt}-select-service-guide {
      width: 200px;
      height: 112px;
      padding: 10px;
      margin-top: -20px;
    }

    .${Vt}-select-service-guide-card {
      display: flex;
      flex-direction: column;
      justify-content: center;
      padding: 0 20px;
      border-radius: 12px;
      background: #FFF;
      box-shadow: 0px 0.976px 7.805px 0px rgba(0, 10, 30, 0.10);
      height: 100%;
      width: 100%;
      box-sizing: border-box;
    }

    .${Vt}-max-model {
      color: #999;
      font-size: 12px;
      line-height: 1.5;
    }

    .${Vt}-model-example {
      color: #333;
      font-size: 14px;
      line-height: 1.5;
      display: flex;
      align-items: center;
      gap: 4px;
      margin-top: 10px;
      padding-left: 4px;
    }

    .service-icon {
      width: 24px;
      height: 24px;
    }


    .${Vt}-video-subtitle-guide {
      width: 200px;
      padding: 10px;
      margin-bottom: 10px;
      margin-top: 20px;
    }


    .${Vt}-video-subtitle-guide-card {
      background: #000;
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 100%;
      padding: 10px;
      font-size: 14px;
      border-radius: 12px;
      color: #FFF;
      box-sizing: border-box;
    }
    .${Vt}-video-subtitle-guide-card div {
      display: flex;
      align-items: center;
      gap: 4px;
    }

    .${Vt}-message {
      font-size: 14px;
      line-height: 28px;
      color: #333333;
      white-space: pre-wrap;
      text-align: center;
      font-weight: 700;
      margin-bottom: 20px;
      padding: 0 30px;
    }

    .${Vt}-button {
      margin-top: 16px;
      line-height: 40px;
      height: 40px;
      padding: 0 20px;
      width: unset;
    }
`;
  var zte = `${ge}-new-user-guide-root`;
  function Fd(e) {
    let { type: t, arrowDirection: a = "right" } = e;
    Bt.set("showUserGuide", { type: t, arrowDirection: a });
  }
  async function Eu(e, t) {
    let a = t || (await ir()),
      n = await re.storage.sync.get(e);
    return { userValue: a[e], localValue: n[e] };
  }
  async function ku(e, t) {
    let a = await ir();
    zr({ ...a, [e]: t }), re.storage.sync.set({ [e]: t });
  }
  function Rd(e, t = !1) {
    if (!t) {
      try {
        if (!be(!1, !0) && aa()) {
          re.tabs.create({ url: `${e}` });
          return;
        }
        globalThis.open(`${e}`, "_blank");
      } catch {
        globalThis.open(`${e}`, "_blank");
      }
      return;
    }
    try {
      if (!be(!1, !0) && aa()) {
        re.tabs.create({
          url: `${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,
        });
        return;
      }
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,
        "_blank"
      );
    } catch {
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,
        "_blank"
      );
    }
  }
  async function Ww() {
    let { userValue: e, localValue: t } = await Eu("rewardCenterOpenTime");
    e || t || (await ku("rewardCenterOpenTime", Date.now().toString()));
  }
  function Bd({
    visible: e,
    onClose: t,
    ctx: a,
    refreshKey: n = 0,
    setSettings: r,
  }) {
    Ww();
    let [i, o] = ve(!1),
      [s, u] = ve(!1),
      [l, c] = ve({}),
      [m, d] = ve(0),
      { t: y } = vt(),
      [h, v] = ve(""),
      x = `${n}_${m}`,
      { rewardData: g, isLoading: b, error: p } = z2({ refreshKey: x, ctx: a }),
      f = () => {
        d((F) => F + 1);
      };
    je(() => {
      if (e) {
        o(!0), u(!1);
        let F = setTimeout(() => {
          u(!0);
        }, 10);
        return d((L) => L + 1), () => clearTimeout(F);
      } else {
        u(!1);
        let F = setTimeout(() => {
          o(!1);
        }, 400);
        return () => clearTimeout(F);
      }
    }, [e]);
    let C = (F) => {
        F.target === F.currentTarget && t();
      },
      A = () => {
        e || o(!1);
      };
    function S(F) {
      let L = Be();
      return (
        {
          translate_line_1: `${jo}reward-task/?showUserGuide=true&guideType=selection-translate`,
          translate_web_1: `${jo}reward-task/?showUserGuide=true&guideType=float-ball`,
          translate_web_2: `${jo}reward-task/?showUserGuide=true&guideType=translate-service`,
          translate_pdf_1: `${L.TRANSLATE_FILE_URL}`,
          translate_pdf_2: `${L.PDF_PRO_URL}`,
          translate_video_1: `${Ju}`,
          translate_video_2: `${Ju}`,
        }[F] || ""
      );
    }
    let T = async (F) => {
        if (
          (W.debug("Task start:", F.taskConfig.taskKey),
          be(!1, !0) && F.taskConfig.taskKey === "translate_line_1")
        )
          return;
        let L = await e4(),
          O = S(F.taskConfig.taskKey);
        if (!L) {
          Rd(`${_o}&return_url=${encodeURIComponent(O)}`, !1);
          return;
        }
        if (F.enabled && !F.completed) {
          let E = F.taskConfig.taskKey;
          ea({
            key: "reward_center_start_task",
            ctx: a,
            params: { trigger: F.taskConfig.taskKey },
          }),
            E === "translate_line_1" &&
              (a.config.generalRule.selectionTranslation?.enable ||
                r((w) => ({
                  ...w,
                  generalRule: {
                    ...w.generalRule,
                    "selectionTranslation.add": {
                      ...w.generalRule?.["selectionTranslation.add"],
                      enable: !0,
                    },
                  },
                }))),
            E === "translate_video_1" && Fd({ type: "video-subtitle" }),
            E === "translate_video_2" && Fd({ type: "ai-subtitle" }),
            Rd(O);
          return;
        }
        if (F.completed && !F.taken) {
          if (h) return;
          v(F.taskConfig.taskKey),
            ea({
              key: "reward_center_claim",
              ctx: a,
              params: { trigger: F.taskConfig.taskKey },
            });
          try {
            await $1(F.taskConfig.taskKey), f(), t4(), v("");
          } catch (E) {
            W.error("takeReward error", E);
          } finally {
            v("");
          }
        }
      },
      k = (F, L = !1) => {
        switch (F) {
          case "ai_token":
            return L
              ? y("rewardCenter.reward.ai_token") +
                  y("rewardCenter.reward.description")
              : y("rewardCenter.reward.ai_token");
          case "pdf_token":
            return y("rewardCenter.reward.pdf_token");
          case "video_token":
            return y("rewardCenter.reward.video_token");
          default:
            return F;
        }
      },
      R = (F, L, O = !0) => {
        let E = F.toLocaleString(),
          M = O ? y("rewardCenter.reward.unit_pdf_token") : "",
          w = O ? y("rewardCenter.reward.unit_video_token") : "";
        return L === "ai_token"
          ? E
          : L === "video_token"
          ? `${E}${w}`
          : L === "pdf_token"
          ? `${E}${M}`
          : E;
      },
      j = (F) => {
        let { taskConfig: L, taken: O, completed: E, enabled: M } = F,
          w = L.rewards[0],
          D = L.groupName === "\u9AD8\u7EA7\u4EFB\u52A1",
          U = E && O,
          _ = !M && !E && !O,
          q = y("rewardCenter.task.start"),
          z = "reward-task-button",
          G = !1;
        return (
          E && !O
            ? ((q = y("rewardCenter.task.claim")), (z += " claim"))
            : E && O
            ? ((q = y("rewardCenter.task.claimed")), (z += " completed"))
            : !M && !E && !O
            ? ((q = y("rewardCenter.task.start")), (z += " disabled"))
            : D &&
              M &&
              !E &&
              !O &&
              (n4(F, g) || ((q = y("rewardCenter.task.start")), (G = !0))),
          I(
            "div",
            {
              className: `reward-task-item ${_ ? "unavailable" : ""} ${
                U ? "completed" : ""
              }`,
              children: [
                I("div", {
                  className: "reward-task-content",
                  children: [
                    I("div", {
                      className: "reward-task-title",
                      children: y(`rewardCenter.task.${L.taskKey}`),
                    }),
                    w &&
                      I("div", {
                        className: "reward-task-reward",
                        children: [
                          y("rewardCenter.reward.get"),
                          "  ",
                          I("span", {
                            className: `${
                              D ? "reward-amount-advanced" : "reward-amount"
                            }`,
                            children: R(w.rewardAmount, w.rewardType),
                          }),
                          " ",
                          k(w.rewardType, !0),
                        ],
                      }),
                  ],
                }),
                U
                  ? I(C2, {})
                  : be(!1, !0) && L.taskKey === "translate_line_1"
                  ? I(xo, {
                      text: y("rewardCenter.task.translate_line_1.warning", {
                        1: {
                          tag: "a",
                          style: "color: #EA4C89;",
                          href: Qu + "?utm_campaign=reward_center",
                          target: "_blank",
                        },
                      }),
                      position: "left",
                      tipStyle: {
                        width: "150px",
                        maxWidth: "150px",
                        whiteSpace: "normal",
                        wordBreak: "break-word",
                      },
                      children: I("button", {
                        className: `${z} disabled`,
                        disabled: !0,
                        children: q,
                      }),
                    })
                  : G
                  ? I(xo, {
                      text: y("rewardCenter.task.unclaimedWarning"),
                      position: "left",
                      tipStyle: {
                        width: "150px",
                        maxWidth: "150px",
                        whiteSpace: "normal",
                        wordBreak: "break-word",
                      },
                      children: I("button", {
                        className: z,
                        onClick: () => T(F),
                        disabled: _,
                        children: [q, h === L.taskKey ? I(V2, {}) : null],
                      }),
                    })
                  : I("button", {
                      className: z,
                      onClick: () => T(F),
                      disabled: _,
                      children: [q, h === L.taskKey ? I(V2, {}) : null],
                    }),
              ],
            },
            L.taskKey
          )
        );
      },
      B = (F) => {
        let L = F.groupName === "\u9AD8\u7EA7\u4EFB\u52A1",
          O = y(L ? "rewardCenter.task.level2" : "rewardCenter.task.level1"),
          E = F.taskItems.filter((w) => !(w.completed && w.taken)),
          M = F.taskItems.filter((w) => w.completed && w.taken);
        return I(
          "div",
          {
            className: "reward-center-card",
            children: [
              I("div", {
                className: "reward-card-title",
                children: [
                  L ? I(x2, {}) : I(v2, {}),
                  O,
                  L &&
                    I("span", {
                      className: "reward-task-subtitle",
                      children: y("rewardCenter.task.level2.description"),
                    }),
                ],
              }),
              I("div", {
                className: "reward-card-content",
                children: [
                  E.map((w) => j(w)),
                  M.length > 0 &&
                    I(St, {
                      children: [
                        I("div", {
                          className: "completed-tasks-divider clickable",
                          onClick: () =>
                            c((w) => ({
                              ...w,
                              [F.groupName]: !w[F.groupName],
                            })),
                          children: [
                            I("span", {
                              className: "completed-tasks-text",
                              children: y("rewardCenter.task.completed"),
                            }),
                            I("span", {
                              className: `completed-tasks-arrow ${
                                l[F.groupName] ? "expanded" : ""
                              }`,
                              children: I(Rn, {}),
                            }),
                          ],
                        }),
                        l[F.groupName] && M.map((w) => j(w)),
                      ],
                    }),
                ],
              }),
            ],
          },
          F.groupName
        );
      };
    return !e && !i
      ? null
      : I(St, {
          children: I("div", {
            className: `reward-center-overlay ${s ? "visible" : ""}`,
            onClick: C,
            children: I("div", {
              className: `reward-center-drawer ${s ? "visible" : ""}`,
              onTransitionEnd: A,
              children: [
                I("div", {
                  className: "reward-center-fixed-header",
                  children: [
                    I("div", {
                      className: "reward-center-header",
                      children: [
                        I("div", {
                          className: "reward-center-header-left",
                          children: [
                            I(Cu, {}),
                            I("div", {
                              className: "reward-center-title",
                              children: y("rewardCenter.title"),
                            }),
                          ],
                        }),
                        I(vo, {
                          class: "reward-center-close-icon",
                          onClick: t,
                        }),
                      ],
                    }),
                    I("div", {
                      className: "reward-center-description",
                      children: y("rewardCenter.description"),
                    }),
                  ],
                }),
                I("div", {
                  className: "reward-center-scrollable-content",
                  children: [
                    !g && b && I("div", { className: "reward-center-loading" }),
                    p &&
                      I("div", {
                        className: "reward-center-error",
                        children: p.message,
                      }),
                    !p &&
                      g &&
                      I(St, {
                        children: [
                          I("div", {
                            className:
                              "reward-center-card reward-progress-container",
                            children: [
                              I("div", {
                                className: "reward-card-title",
                                children: y("rewardCenter.progress"),
                              }),
                              I("div", {
                                className: "reward-card-content",
                                children: g?.rewardViews.map((F) => {
                                  let L =
                                    F.rewardTotalAmount > 0
                                      ? (F.completedAmount /
                                          F.rewardTotalAmount) *
                                        100
                                      : 0;
                                  return I(
                                    "div",
                                    {
                                      className: "reward-progress-item",
                                      children: I("div", {
                                        className: "reward-progress-info",
                                        children: [
                                          I("div", {
                                            className: "reward-progress-label",
                                            children: k(F.rewardType),
                                          }),
                                          I("div", {
                                            className: "reward-progress-right",
                                            children: [
                                              I("div", {
                                                className:
                                                  "reward-progress-value",
                                                children: [
                                                  R(
                                                    F.completedAmount,
                                                    F.rewardType,
                                                    !1
                                                  ),
                                                  "/",
                                                  R(
                                                    F.rewardTotalAmount,
                                                    F.rewardType,
                                                    !1
                                                  ),
                                                ],
                                              }),
                                              I("div", {
                                                className:
                                                  "reward-progress-bar",
                                                children: I("div", {
                                                  className:
                                                    "reward-progress-fill",
                                                  style: { width: `${L}%` },
                                                }),
                                              }),
                                            ],
                                          }),
                                        ],
                                      }),
                                    },
                                    F.rewardType
                                  );
                                }),
                              }),
                            ],
                          }),
                          g?.groupViews.map((F) => B(F)),
                        ],
                      }),
                    I("div", {
                      className: "reward-center-footer",
                      children: [
                        I(xu, {}),
                        I("div", {
                          className: "reward-center-footer-text",
                          onClick: () => Rd(Oo, !0),
                          children: y("rewardCenter.help"),
                        }),
                      ],
                    }),
                  ],
                }),
              ],
            }),
          }),
        });
  }
  var V2 = () => I("div", { className: "reward-task-loading" });
  var Du = null;
  function _d() {
    let {
        config: e,
        ctx: t,
        ctxRef: a,
        settingsHookValue: n,
        refreshCtx: r,
      } = O2(),
      [i, o, s, u, l] = n,
      [c, m] = ve("auto"),
      [d, y] = ve([]),
      [h, v] = ve([]),
      { expand: x, setExpand: g } = l2(),
      [b, p] = ve(!1),
      [f, C] = ve(0),
      A = () => {
        b ||
          ea({
            key: "reward_center_open",
            ctx: t,
            params: { trigger: "side_panel" },
          }),
          p(!b);
      },
      { t: S } = vt(),
      T = e && Ad(e.rtlLanguages ?? [], e.interfaceLanguage),
      [k, R] = ve([]);
    je(() => {
      async function z() {
        if (!t) return;
        let G = await B2(t),
          Q = (await o4(t, "translationService", "zh-CN", G)) || [];
        R(Q);
      }
      z();
    }, [t]);
    let j = ct(() => (t ? k.filter((z) => h.includes(z.id)) : []), [t, k, h]),
      B = ct(() => s4(k, S), [k]),
      [F, L] = ve([]),
      O = it([]);
    O.current = F;
    let E = ot(
        async (z, G, Q, J) => {
          t &&
            (m(G),
            y(Q),
            v(z),
            L([]),
            Cd({
              ctx: t,
              sourceLang: G,
              text: J,
              services: z,
              targetLangs: Q,
              callback: (le) => {
                let pe = O.current.find(
                  (Z) =>
                    Z.service === le.service && Z.targetLang === le.targetLang
                );
                pe
                  ? ((pe.loading = !1),
                    (pe.translateText = le.translateText),
                    (pe.error = le.error),
                    L([...O.current]))
                  : (O.current.push(le), L([...O.current]));
              },
            }));
        },
        [t, O]
      ),
      M = ot(
        (z, G) => {
          let Q = O.current.find((J) => J.service === z && J.targetLang === G);
          !t ||
            !Q ||
            Cd({
              ctx: t,
              sourceLang: c,
              text: Q.text,
              services: [z],
              targetLangs: [G],
              callback: (J) => {
                (Q.translateText = J.translateText),
                  (Q.loading = J.loading),
                  (Q.error = J.error),
                  L([...O.current]);
              },
            });
        },
        [t, O, c]
      ),
      w = ot(
        (z) => {
          v(h.filter((G) => G !== z));
        },
        [h]
      ),
      [D, U] = ve(!1);
    je(() => {
      document.body.style.marginRight && U(!0);
    }, []);
    let _ = ot(
      (z) => {
        let G;
        typeof z.detail == "string"
          ? (G = JSON.parse(z.detail))
          : (G = z.detail);
        let { payload: Q } = G,
          { method: J, data: le } = Q;
        switch ((W.debug("side panel received message", J, le || " "), J)) {
          case "openRewardCenter":
            b
              ? (p(!1), Pf())
              : (p(!0),
                ea({
                  key: "reward_center_open",
                  ctx: a.current,
                  params: { trigger: le?.trigger || "float_ball" },
                }));
            break;
          case "refreshRewardCenter":
            p(!0), C((pe) => pe + 1);
            break;
          case "updateContext":
            r();
            break;
        }
      },
      [b, r]
    );
    function q() {
      Du && globalThis.document.removeEventListener(pr, Du),
        (Du = _),
        globalThis.document.addEventListener(pr, Du);
    }
    return (
      je(() => {
        location.href.includes("openRewardCenter=true") &&
          (p(!0),
          setTimeout(() => {
            ea({
              key: "reward_center_open",
              ctx: a.current,
              params: { trigger: "popup" },
            });
          }, 1e3)),
          q();
      }, []),
      t
        ? I("div", {
            className: "notranslate imt-side-panel",
            dir: T ? "rtl" : "ltr",
            children: [
              D &&
                I(vo, {
                  class: "panel-close-icon",
                  onClick: () => {
                    tu(t, "panel");
                  },
                }),
              I(R2, {
                ctx: t,
                selectServices: h,
                serviceItems: k,
                groupServiceItems: B,
                resultsRef: O,
                onTranslate: E,
              }),
              I(Yw, {
                ctx: t,
                config: e,
                serviceItems: j,
                langs: d,
                results: F,
                onDeleteService: w,
                onRefresh: M,
              }),
              I("div", { style: { height: x ? 160 : 50 } }),
              I(Qw, { ctx: t, expand: x, setExpand: g, toggleRewardCenter: A }),
              I(Bd, {
                visible: b,
                onClose: A,
                ctx: t,
                refreshKey: f,
                setSettings: o,
              }),
            ],
          })
        : null
    );
  }
  function Qw({ ctx: e, expand: t, setExpand: a, toggleRewardCenter: n }) {
    let { t: r } = vt(),
      [i, o] = ve(!1),
      [s, u] = ve(!1),
      l = ot(() => {
        o(!0),
          setTimeout(() => {
            o(!1);
          }, 1200);
      }, []);
    je(() => {
      (async () => {
        let { userValue: p, localValue: f } = await Eu("rewardCenterClicked"),
          C = p || f;
        u(!!C), C || l();
      })();
    }, []),
      je(() => {
        let p = setInterval(() => {
          s || Zc(e) ? clearInterval(p) : l();
        }, 2e3);
        return () => clearTimeout(p);
      }, [l, e, s]);
    let c = ot(() => {
        o(!1), ku("rewardCenterClicked", !0), u(!0);
      }, []),
      [m, d] = wd(),
      y = e.config?.sidePanel || {},
      h = Zc(e),
      v = async () => {
        let b = await _2();
        if (b) {
          let p = b.langs[0],
            f = encodeURIComponent(b.text.trim()),
            C = `${oi}text#${b.sourceLang}/${p}/${f}`;
          ei(C, "translate_text");
        } else ei(oi + "text", "translate_text");
      },
      x = (y.i18n?.[e.config.interfaceLanguage] || y.i18n?.en)?.upgradeTitle,
      g = y.upgradeUrl;
    return I("div", {
      className: "side-footer",
      children: [
        I("div", {
          className: "footer-expand",
          style: { marginBottom: t ? 4 : -12 },
          onClick: () => a(!t),
          children: t ? I(Rn, {}) : I(A2, {}),
        }),
        I("div", {
          className: "footer-area",
          style: { display: t ? "flex" : "none" },
          children: [
            I("div", { className: "divider" }),
            I("span", {
              className: "footer-area-title",
              children: r("alsoTranslate"),
            }),
            I("div", {
              className: "footer-area-buttons-wrapper",
              children: [
                I("div", {
                  className: "footer-area-button",
                  onClick: () => ei(oi, "translate_file"),
                  children: [I(S2, {}), r("translateFile")],
                }),
                I("div", {
                  className: "footer-area-button",
                  onClick: () => ei(oi + "image", "translate_image"),
                  children: [I(T2, {}), r("translateImage")],
                }),
                I("div", {
                  className: "footer-area-button",
                  onClick: v,
                  children: [I(E2, {}), r("translateText")],
                }),
              ],
            }),
          ],
        }),
        I("div", {
          className: "side-footer-preview",
          children: [
            e.isPro
              ? I("div", { className: "empty-space" })
              : I("a", {
                  className: "upgrade",
                  target: "_blank",
                  href: g,
                  onClick: () => d(!0),
                  children: [I(w2, {}), I("span", { children: x })],
                }),
            I("div", {
              className: "action-buttons",
              children: [
                !h &&
                  I("div", {
                    className: "action-icon-wrapper",
                    onClick: () => {
                      n(), c();
                    },
                    children: [
                      I("div", {
                        className: `action-icon${i ? " bounce-animate" : ""}`,
                        children: I(Cu, {}),
                      }),
                      I("span", {
                        className: "reward-center-text",
                        children: r("rewardCenter.title"),
                      }),
                    ],
                  }),
                I("div", {
                  className: "action-icon",
                  onClick: () => ei(Oo, "help_center"),
                  children: I(xu, {}),
                }),
                I("div", {
                  className: "action-icon",
                  onClick: () => uu(!1, "", !1),
                  children: I(b2, {
                    style: { width: 18, height: 18 },
                    fillColor: "#999",
                  }),
                }),
              ],
            }),
          ],
        }),
      ],
    });
  }
  function ei(e, t) {
    try {
      if (!be(!1, !0) && aa()) {
        re.tabs.create({
          url: `${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,
        });
        return;
      }
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,
        "_blank"
      );
    } catch {
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,
        "_blank"
      );
    }
  }
  function Yw({
    ctx: e,
    config: t,
    serviceItems: a,
    langs: n,
    results: r,
    onDeleteService: i,
    onRefresh: o,
  }) {
    let { t: s } = vt(),
      [u, l] = ve(!1),
      c = (d) => {
        I2(d),
          l(!0),
          setTimeout(() => {
            l(!1);
          }, 2e3);
      },
      m = (d, y) => {
        let h = new AbortController();
        M2({ text: y, lang: d, provider: "WebSpeech", signal: h.signal });
      };
    return t
      ? I("div", {
          className: "results-container",
          children:
            r.length > 0 &&
            a
              .map((d, y) => {
                let h = n.map((v) => {
                  let x = r.find(
                      (f) => f.service === d.id && f.targetLang === v
                    ),
                    g = Ad(e.config.rtlLanguages, v),
                    b = x?.translateText,
                    p = x?.loading || (!x?.translateText && !x?.error);
                  return I("div", {
                    className: "result-container",
                    children: [
                      b &&
                        !x.error &&
                        I("pre", {
                          dir: g ? "rtl" : "ltr",
                          children: b.trim(),
                        }),
                      p &&
                        I("div", {
                          className: "skeleton-loader",
                          children: [
                            I("div", { className: "skeleton-text-line short" }),
                            I("div", {
                              className: "skeleton-text-line medium",
                            }),
                          ],
                        }),
                      I(Zw, {
                        ctx: e,
                        error: x?.error,
                        onRefresh: () => o(d.id, v),
                      }),
                      I("div", {
                        className: "result-lang",
                        children: [
                          I(xo, {
                            text: s(
                              u
                                ? "selectionTranslationCopySuccess"
                                : "selectionTranslationCopy"
                            ),
                            children: I("div", {
                              className: "icon",
                              onClick: () => c(b ?? ""),
                              children: I(y2, {}),
                            }),
                          }),
                          I("div", {
                            className: "icon",
                            onClick: () => m(v, b),
                            children: I(f2, {}),
                          }),
                          I("div", { style: { flex: 1 } }),
                          bo(v, t?.interfaceLanguage, !1, !0),
                        ],
                      }),
                    ],
                  });
                });
                return I("div", {
                  className: "service-wrapper-container",
                  children: [
                    I("div", {
                      className: "service-wrapper",
                      children: [
                        I("img", { src: d.icon, className: "icon" }),
                        I("span", { children: d.name }),
                        I("div", {
                          className: "close-icon",
                          onClick: () => i(d.id),
                          children: I(vo, {}),
                        }),
                      ],
                    }),
                    h,
                    I("div", {
                      className: "divider",
                      hidden: y === a.length - 1,
                    }),
                  ],
                });
              })
              .flat(),
        })
      : null;
  }
  function Zw({ ctx: e, error: t, onRefresh: a }) {
    let { t: n } = vt(),
      [r, i] = wd();
    if (!e || !t) return null;
    let o = t.uiConfig(e);
    return ["upgrade", "login"].includes(o.action || "")
      ? I("div", {
          className: "error-result error-info",
          children: [
            I("div", {
              className: "error-message",
              dangerouslySetInnerHTML: { __html: o.errMsg ?? "" },
            }),
            I("div", {
              className: "upgrade-button",
              onClick: () => {
                ei(da, "upgrade"), i(!0);
              },
              children: [
                I("svg", {
                  width: "20",
                  height: "21",
                  viewBox: "0 0 20 21",
                  fill: "none",
                  xmlns: "http://www.w3.org/2000/svg",
                  children: I("path", {
                    d: "M6.7482 17.3325C7.46127 17.5702 7.69894 16.9165 7.40183 16.6788C6.45109 16.0252 5.97572 14.8368 6.33226 13.7672C6.68879 12.5788 7.63953 12.1034 7.63953 10.3208C7.63953 10.3208 8.82795 11.2121 8.59027 12.5788C9.77869 11.2121 9.2439 9.42947 9.00623 8.59756C12.0367 10.2019 14.6512 13.7078 11.6208 16.6788C11.2642 16.9759 11.6208 17.5107 12.1556 17.3325C20.4151 12.6382 14.1759 5.62648 13.1063 4.79459C13.4628 5.62648 13.5223 6.93375 12.8092 7.5874C11.5613 2.89312 8.53088 1.94238 8.53088 1.94238C8.8874 4.37865 7.1642 6.9932 5.5598 9.01352C5.50039 8.00336 5.44098 7.34973 4.96559 6.4584C4.84676 8.18162 3.48006 9.60772 3.12355 11.3904C2.64818 13.7672 3.48008 15.4904 6.74826 17.3325L6.7482 17.3325Z",
                    fill: "#FFC736",
                  }),
                }),
                n("upgradeToPro"),
              ],
            }),
          ],
        })
      : I("div", {
          className: "error-result error-warning",
          children: [
            I("div", {
              className: "error-message",
              dangerouslySetInnerHTML: { __html: o.errMsg ?? "" },
            }),
            I("div", {
              className: "retry-button",
              onClick: a,
              children: I(m2, {}),
            }),
          ],
        });
  }
  var Jw = function (e, t) {
      let { method: a, data: n } = e;
      W.debug(`popup received message: ${a}`, n || " ");
      let r;
      t.active &&
        ((r = t.id),
        globalThis.document.dispatchEvent(
          new CustomEvent(pr, { detail: { tabId: r, payload: e } })
        ));
    },
    Mu;
  function K2() {
    Xw();
  }
  function Xw() {
    return (
      Mu || ((Mu = new sn("popup", !1).getConnection("main_sync", Jw)), Mu)
    );
  }
  var W2 = document.getElementById("mount");
  K2();
  W2 &&
    (async () => {
      let e = await wa();
      await Rf(),
        e.debug && W.setLevel("debug"),
        Po(I(k0, { lang: e.interfaceLanguage, children: I(_d, {}) }), W2);
    })();
})();
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
/*! Bundled license information:

lottie-web/build/player/lottie.js:
  (*!
   Transformation Matrix v2.0
   (c) Epistemex 2014-2015
   www.epistemex.com
   By Ken Fyrstenberg
   Contributions by leeoniya.
   License: MIT, header required.
   *)
*/
