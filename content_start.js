(() => {
  var bv = No(null);
  async function vv(e, t, a) {
    a = a || (await fs());
    let n = a.aiAssistants || [],
      r = !1;
    if (e == "edit" && ip(t))
      for (let o = n.length - 1; o >= 0; o--)
        n[o].id === t.id && ((n[o] = t), (r = !0));
    else if (e === "add" && ip(t)) {
      for (let o = n.length - 1; o >= 0; o--)
        n[o].id === t.id && n.splice(o, 1);
      n.push(t), (r = !0);
    } else {
      for (let o = n.length - 1; o >= 0; o--)
        n[o].id === t.id && n.splice(o, 1);
      r = !0;
    }
    a.aiAssistants = n.sort((o, s) => o.priority - s.priority);
    let i = await bn();
    i.aiAssistantIds = [...new Set(n.map((o) => o.id))];
    try {
      await r0(a), await yn(i);
    } catch {
      return !1;
    }
    return r;
  }
  async function xv(e, t) {
    (
      await Promise.allSettled(
        e.map((n) => rt({ url: `${Yi}api/plugins/${n}.json` }))
      )
    ).forEach((n) => {
      if (n.status === "fulfilled") {
        let r = n.value;
        r && vv("add", r, t);
      }
    });
  }
  async function wv(e, t, a = !0) {
    e || (e = await aa()), t || (t = await fs());
    let n = t.aiAssistants || [],
      r = (e.aiAssistantIds || []).filter((s) => !n.find((u) => u.id === s)),
      i = [];
    if (a) {
      let s = await Sv();
      i = await Ev(t, s);
    }
    let o = [...new Set([...r, ...i])].filter((s) => !s.startsWith("custom"));
    o.length !== 0 && xv(o, t);
  }
  var cu = Fa(wv, 1500);
  function ip(e) {
    return ha(On(), e.extensionVersion);
  }
  function Av(e, t) {
    return t ? !ha(e.version, t) : !1;
  }
  function Ev(e, t) {
    let a = [];
    return (
      (e.aiAssistants || []).forEach((n) => {
        Av(n, t[n.id]?.version) && a.push(n.id);
      }),
      [...new Set(a)]
    );
  }
  async function Sv() {
    return (await rt({ url: `${Yi}api/plugins/meta.json` }))?.plugins || {};
  }
  async function mu(e) {
    let { url: t, config: a, state: n } = e,
      r = new URL(t),
      i = "auto",
      {
        translationService: o,
        translationParagraphLanguagePattern: s,
        translationServices: u,
        translationTheme: c,
        translationThemePatterns: l,
        translationUrlPattern: d,
        targetLanguage: m,
        sourceLanguageUrlPattern: p,
        immediateTranslationPattern: x,
      } = a,
      A = m || "zh-CN",
      S = Mn(t, s),
      g = Mn(t, x),
      M = o,
      h = Object.keys(u);
    for (let O of h) {
      let ue = u[O];
      if (Mn(t, ue)) {
        M = O;
        break;
      }
    }
    let k = c,
      L = Object.keys(l);
    for (let O of L) {
      let ue = l[O];
      if (ue && Mn(t, ue)) {
        k = O;
        break;
      }
    }
    let E = Mn(t, d),
      v = du(t, d);
    v || (v = yt(t, pl)), v || (v = yt(t, a.blockUrls));
    let w = du(t, a.inputTranslationUrlPattern);
    w || (w = yt(t, a.inputTranslationBlockUrls));
    let y = du(t, a.generalRule?.selectionTranslation?.urlPattern || {}),
      R = yt(t, a.mutationBlockUrls),
      F = Object.keys(p),
      C = {};
    for (let O of F) {
      let ue = p[O];
      if (ue && ue.matches) for (let me of ue.matches) C[me] = O;
    }
    let j = Object.keys(C),
      Y = Go(t, j);
    Y && ((i = C[Y] ?? "auto"), C[Y] && C[Y] !== "auto" && os(C[Y]));
    let Z = r.hostname,
      ve = await or(Z),
      Se = r.pathname + r.search + r.hash,
      H = await or(Se),
      oe = `https://${ve}.com/${H}`,
      Fe = await ya(),
      he = await tp(),
      T = a.translationStartMode,
      I = Tv(Fe, a);
    (Fe.aiAssistants = Dv(I, a)), T === "dynamic" && g && (T = "immediate");
    let U = M;
    a.inputTranslationService &&
      a.inputTranslationService !== "inherit" &&
      (U = a.inputTranslationService);
    let ne = M;
    a.mouseHoverTranslationService &&
      a.mouseHoverTranslationService !== "inherit" &&
      (ne = a.mouseHoverTranslationService);
    let Q = M;
    a.subtitleTranslateService &&
      a.subtitleTranslateService !== "inherit" &&
      (Q = a.subtitleTranslateService);
    let ce = await st.get(Xe, null),
      fe = !1;
    ce && (fe = Jt(ce));
    let _ = {
      targetLanguage: A,
      config: a,
      translationService: M,
      inputTranslationService: U,
      mouseHoverTranslationService: ne,
      subtitleTranslateService: Q,
      clientImageTranslationService: Q,
      isTranslateUrl: E,
      sourceLanguage: i,
      mainFrame: document.body,
      isTranslateExcludeUrl: v,
      isMutationTranslationExcludeUrl: R,
      isInputTranslationExcludeUrl: w,
      isSelectionTranslationExcludeUrl: y,
      rule: a.generalRule,
      url: t,
      encryptedUrl: oe,
      state: n
        ? Object.assign(
            {
              translationMode: a.translationMode,
              translationArea: a.translationArea,
              translationStartMode: T,
              immediateTranslationTextCount: a.immediateTranslationTextCount,
              isAutoTranslate: !1,
              translationDebounce: 300,
              isNeedClean: !1,
              isDetectParagraphLanguage: S,
              cache: a.cache,
              translationTheme: k,
              isTranslateDirectlyOnHover: !1,
            },
            n
          )
        : {
            translationMode: a.translationMode,
            translationArea: a.translationArea,
            translationStartMode: T,
            immediateTranslationTextCount: a.immediateTranslationTextCount,
            isAutoTranslate: !1,
            translationDebounce: 300,
            isNeedClean: !1,
            isDetectParagraphLanguage: S,
            cache: a.cache,
            translationTheme: k,
            isTranslateDirectlyOnHover: !1,
          },
      localConfig: Fe,
    };
    ce &&
      ((_.user = ce), fe ? (_.isPro = !0) : (_.isPro = !1), (_.isMax = El(ce))),
      _.state.translationArea === "body" &&
        ((_.config.generalRule.excludeTags =
          _.config.generalRule.excludeTags.filter(
            (O) => !_.config.generalRule.bodyTranslateTags.includes(O)
          )),
        (_.config.generalRule.additionalExcludeSelectors =
          _.config.generalRule.additionalExcludeSelectors.filter(
            (O) => O !== ".btn"
          )));
    let N = a.translationServices[_.translationService] || {};
    N.immediateTranslationTextCount !== void 0 &&
      kv(N.immediateTranslationTextCount) &&
      N.immediateTranslationTextCount >= 0 &&
      (_.state.immediateTranslationTextCount = N.immediateTranslationTextCount),
      _.translationService === "deepl-pro" &&
        (N?.authKey?.startsWith("immersive_") ||
          (N?.provider === "pro" && _.user?.token)) &&
        N.immediateTranslationTextCountForImmersiveDeepl !== void 0 &&
        N.immediateTranslationTextCountForImmersiveDeepl >= 0 &&
        (_.state.immediateTranslationTextCount =
          N.immediateTranslationTextCountForImmersiveDeepl),
      N &&
        N.translationDebounce &&
        typeof N.translationDebounce == "number" &&
        (_.state.translationDebounce = N.translationDebounce);
    let V = he.immediateTranslationTextCount;
    a.immediateTranslationTextCount !== V &&
      (_.state.immediateTranslationTextCount = a.immediateTranslationTextCount);
    let ae = a.rules,
      $;
    globalThis.PDFViewerApplication
      ? ($ = ae.find((O) => O.pageType == "pdfReader"))
      : globalThis.immersiveTranslateEbookViewer
      ? ($ = ae.find((O) => O.pageType == "ebookReader"))
      : globalThis.immersiveTranslateEbookBuilder
      ? ($ = ae.find((O) => O.pageType == "ebookBuilder"))
      : (($ = ae.find((O) => Mn(t, O))), X.debug("match rule.id", $?.id)),
      $ &&
        $.pageType === "ebookBuilder" &&
        (_.state.translationStartMode = "immediate");
    let q = a.generalRule;
    if (
      ($ &&
        ((_.rule =
          Object.keys($).length >= Object.keys(q).length ? $ : Er(q, $)),
        (_.rule = await np(a, _.rule, [
          ...(q.advanceMergeConfig || []),
          ...($.advanceMergeConfig || []),
        ]))),
      _.state.translationArea === "body" &&
        _.rule.excludeTags &&
        (_.rule.excludeTags = _.rule.excludeTags.filter(
          (O) =>
            !_.rule.bodyTranslateTags.includes(O) &&
            !_.rule.forceTranslateTags.includes(O)
        )),
      _.rule.mainFrameSelector)
    ) {
      let O = document.querySelector(_.rule.mainFrameSelector);
      O && (_.mainFrame = O);
    }
    return _;
  }
  function Mn(e, t) {
    if (!t) return !1;
    let {
      matches: a,
      excludeMatches: n,
      selectorMatches: r,
      excludeSelectorMatches: i,
    } = t;
    return n && n.length > 0 && yt(e, n)
      ? !1
      : a && a.length > 0 && yt(e, a)
      ? !0
      : i && i.length > 0 && Zr(i)
      ? !1
      : !!(r && r.length > 0 && Zr(r));
  }
  function du(e, t) {
    if (!t) return !1;
    let { excludeMatches: a, excludeSelectorMatches: n } = t;
    return !!((a && a.length > 0 && yt(e, a)) || (n && n.length > 0 && Zr(n)));
  }
  function kv(e) {
    return typeof e == "number";
  }
  function Tv(e, t) {
    let a = [...(e.aiAssistants || [])];
    return (
      (t.rawUserConfig?.customAiAssistants || []).forEach((r) => {
        let i = a.findIndex((o) => o.id === r.id);
        i !== -1 ? (a[i] = r) : a.push(r);
      }),
      a
    );
  }
  function Dv(e, t) {
    try {
      let a = t.rawUserConfig?.aiAssistantsMatches || {};
      return e.map((n) => {
        let r = Um(n.matches || [], a[n.id]?.matches || []);
        return { ...n, matches: r };
      });
    } catch (a) {
      return X.error(a), e;
    }
  }
  var In = "imt-subtitle-inject",
    Ri = class {
      from;
      to;
      constructor(t, a) {
        (this.from = t), (this.to = a);
      }
      sendMessages(t) {
        globalThis.postMessage({
          eventType: In,
          to: this.to,
          from: this.from,
          type: t.type,
          data: t.data,
          id: t.id || new Date().getTime(),
          isAsync: !1,
        });
      }
      getRandomId() {
        return (new Date().getTime() + Math.random()) * Math.random();
      }
      sendAsyncMessages({ type: t, data: a }) {
        return new Promise((n) => {
          let r = this.getRandomId();
          globalThis.postMessage({
            eventType: In,
            to: this.to,
            from: this.from,
            type: t,
            data: a,
            id: r,
            isAsync: !0,
          });
          let i = (o) => {
            let s = o.data;
            In === s.eventType &&
              s.id === r &&
              s.to === this.from &&
              (n(s.data), globalThis.removeEventListener("message", i));
          };
          globalThis.addEventListener("message", i);
        });
      }
      handleMessageOnce(t) {
        return new Promise((a) => {
          let n = (r) => {
            let i = r.data;
            In === i.eventType &&
              i.type === t &&
              i.to === this.from &&
              (a(i.data), globalThis.removeEventListener("message", n));
          };
          globalThis.addEventListener("message", n);
        });
      }
      handleMessage(t, a) {
        let n = (r) => {
          let i = r.data;
          In === i.eventType && i.type === t && i.to === this.from && a(i);
        };
        return (
          globalThis.addEventListener("message", n),
          () => {
            globalThis.removeEventListener("message", n);
          }
        );
      }
      handleMessages(t) {
        let a = ({ data: n }) => {
          In === n.eventType && n.to === this.from && t(n);
        };
        return (
          globalThis.addEventListener("message", a),
          () => {
            globalThis.removeEventListener("message", a);
          }
        );
      }
    },
    _i = new Ri("content-script", "inject"),
    Cv = new Ri("inject", "content-script"),
    op = {
      get(e, t, a) {
        return t in e
          ? (...n) => {
              let r = e[t];
              return typeof r == "function"
                ? r.apply(e, n)
                : Reflect.get(e, t, a);
            }
          : (n) => e.sendAsyncMessages({ type: t, data: n });
      },
    },
    Nj = new Proxy(Cv, op),
    Oj = new Proxy(_i, op);
  var eL = Fa(Mv, 1e3);
  async function Mv(e, t, a) {
    try {
      if (t === null) return "noupdate";
      let n = await ya();
      if (t.updatedAt) {
        let u = new Date().getTime(),
          c = new Date(t.updatedAt).getTime();
        if (u - c < 2e3) {
          let l = Bi(t.proSyncAPIKey, t);
          return await pu(e, l), await Mt(n), "upload";
        }
      }
      let { remoteSetting: r, remoteTimestamp: i } = await lp(e);
      (n.accountLastSyncedAt = Date.now()),
        X.debug("settings", t),
        X.debug("remoteSettings", r),
        X.debug("local settings.updatedAt", Pn(t.updatedAt)),
        X.debug("remote settings.updatedAt", Pn(r.updatedAt)),
        X.debug("last synced at", Pn(n.accountLastSyncedAt)),
        Bd(t, i);
      let o = !1;
      if (
        (t.updatedAt && (!r || !r.updatedAt) && (o = !0),
        !o && t.updatedAt > r.updatedAt && (o = !0),
        X.debug("isUpload", o),
        o)
      ) {
        let u = Bi(t.proSyncAPIKey, t);
        return await pu(e, u), await Mt(n), "upload";
      }
      let s = !1;
      return (
        r.updatedAt && (!t || !t.updatedAt) && (s = !0),
        !s && t.updatedAt < r.updatedAt && (s = !0),
        s
          ? ((r.override = !0),
            su(r.proSyncAPIKey, t, r),
            await a(up(r, t)),
            await Mt(n),
            cu(void 0, n),
            "override")
          : (await Mt(n), "noupdate")
      );
    } catch (n) {
      throw (ts(n), n);
    }
  }
  var Pn = (e) =>
    e ? new Date(e).toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" }) : "";
  async function sp(e, t, a) {
    try {
      if (t === null) return "noupdate";
      let { remoteSetting: n } = await lp(e),
        r = await ya();
      (r.accountLastSyncedAt = Date.now()),
        X.debug("settings", t),
        X.debug("remoteSettings", n),
        X.debug("local settings.updatedAt", Pn(t.updatedAt)),
        X.debug("remote settings.updatedAt", Pn(n.updatedAt)),
        X.debug("last synced at", Pn(r.accountLastSyncedAt));
      let i = !1;
      if (
        (t.updatedAt &&
          (!n?.updatedAt || Object.keys(n).length <= 1) &&
          (i = !0),
        X.debug("isUpload", i),
        i)
      ) {
        let s = Bi(t.proSyncAPIKey, t);
        return await pu(e, s), await Mt(r), "upload";
      }
      let o = !0;
      return (
        (!n?.updatedAt || Object.keys(n).length <= 1) && (o = !1),
        o
          ? (su(n.proSyncAPIKey, t, n),
            (n.override = !0),
            await a(up(n, t)),
            await Mt(r),
            cu(void 0, r),
            "override")
          : (await Mt(r), "noupdate")
      );
    } catch (n) {
      throw (ts(n), n);
    }
  }
  function up(e, t) {
    let a = { ...e };
    return (
      $u.forEach((r) => {
        n(r);
      }),
      a
    );
    function n(r) {
      !Iv(e.translationServices || {}, e[r]) && (a[r] = t[r]);
    }
  }
  function Iv(e, t) {
    if (!t) return !0;
    let a = Object.keys(Dn.translationServices).find((n) => t == n);
    return !a && t ? e[t]?.type == "custom-ai" : a;
  }
  function lp(e) {
    return (location.href?.indexOf("popup.html") > 0 ? ja : rt)({
      responseType: "json",
      url: $a + "v1/user/settings",
      method: "get",
      headers: { token: e },
    }).then((a) => ({
      remoteSetting: a.data,
      remoteTimestamp: a.timestamp * 1e3,
    }));
  }
  function pu(e, t) {
    return (
      delete t.localUpdatedAt,
      delete t.override,
      (location.href?.indexOf("popup.html") > 0 ? ja : rt)({
        responseType: "json",
        url: $a + "v1/user/settings",
        method: "post",
        headers: { token: e, "content-type": "application/json" },
        body: JSON.stringify(t),
      }).then((n) => n.data)
    );
  }
  async function cp() {
    if (Xt())
      try {
        await Pv();
        let e = await st.get(Xe, null),
          t = await rs.getUserInfo();
        if (t && t.token !== e?.token) {
          st.set(Xe, t);
          let a = await bn();
          Jt(t) && (await sp(t.token, a, yn)),
            document.dispatchEvent(
              new CustomEvent(z + "DocumentMessageUser", { detail: t })
            );
        }
      } catch {}
  }
  function Pv() {
    return new Promise((e) => {
      ns.handleMessageOnce("bridgeReady").then(() => {
        e(!0);
      });
    });
  }
  var gu = class {
      bridge;
      waitForBridge(t = 1e4) {
        return !Xt() && !Zt()
          ? Promise.resolve(!1)
          : globalThis.WebViewJavascriptBridge
          ? ((this.bridge = globalThis.WebViewJavascriptBridge),
            Promise.resolve(!0))
          : new Promise((a) => {
              let n = Date.now(),
                r = () => {
                  if (globalThis.WebViewJavascriptBridge)
                    return (
                      (this.bridge = globalThis.WebViewJavascriptBridge), a(!0)
                    );
                  if (Date.now() - n > t) return a(!1);
                  requestAnimationFrame(r);
                };
              r();
            });
      }
      registerHandler(t, a) {
        this.bridge && this.bridge.registerHandler(t, a);
      }
      callHandler(t, a, n) {
        this.bridge && this.bridge.doSend({ type: t, ...a }, n);
      }
    },
    We = new gu();
  var hu = class {
      constructor() {}
      getRandomId() {
        return (new Date().getTime() + Math.random()) * Math.random();
      }
      sendAsyncMessages({ type: t, data: a }) {
        return new Promise((n) => {
          let r = this.getRandomId(),
            i = this.handleMessage(t, (o) => {
              o.id === r && (i(), n(o.payload));
            });
          this.sendMessages({ type: t, id: r, data: a });
        });
      }
      sendMessages(t) {
        globalThis.document.dispatchEvent(
          new CustomEvent(ll, {
            detail: JSON.stringify({
              id: t.id || this.getRandomId(),
              type: t.type,
              data: t.data,
            }),
          })
        );
      }
      handleMessages(t) {
        let a = (n) => {
          let r = n;
          if (r.detail)
            try {
              let i = JSON.parse(r.detail);
              t(i);
            } catch {}
        };
        return (
          globalThis.document.addEventListener(Lr, a),
          () => {
            globalThis.document.removeEventListener(Lr, a);
          }
        );
      }
      handleMessage(t, a) {
        return this.handleMessages((n) => {
          n.type === t && a(n);
        });
      }
    },
    Fv = new hu(),
    Bv = {
      get(e, t, a) {
        return t in e
          ? (...n) => {
              let r = e[t];
              return typeof r == "function"
                ? r.apply(e, n)
                : Reflect.get(e, t, a);
            }
          : (n) => {
              if (t.startsWith("getAsync") || t.endsWith("Async"))
                return e.sendAsyncMessages({ type: t, data: n });
              e.sendMessages({ type: t, data: n });
            };
      },
    },
    jt = new Proxy(Fv, Bv);
  function dp(e, t) {
    let a =
      "right: unset; bottom: unset; left: 50%; top: 0; transform: translateX(-50%);";
    globalThis.innerWidth > 450 &&
      (a = "left: unset; top: 0; right: 20px; bottom: unset; transform: none;"),
      jt.togglePopup({
        style: e.style || a,
        isSheet: e.isSheet || !1,
        overlayStyle: e.overlayStyle || "background-color: transparent;",
      }),
      t({ result: !0 });
  }
  function mp(e, t) {
    let a =
      "right: unset; bottom: unset; left: 50%; top: 0; transform: translateX(-50%);";
    globalThis.innerWidth > 450 &&
      (a = "left: unset; top: 0; right: 20px; bottom: unset; transform: none;"),
      jt.openPopup({
        style: e.style || a,
        isSheet: e.isSheet || !1,
        overlayStyle: e.overlayStyle || "background-color: transparent;",
      }),
      t({ result: !0 });
  }
  function pp(e, t) {
    jt.closePopup(), t({ result: !0 });
  }
  function gp(e, t) {
    jt.translatePage(), t({ result: !0 });
  }
  function hp(e, t) {
    jt.restorePage(), t({ result: !0 });
  }
  async function fp(e, t) {
    let a = await jt.getPageStatusAsync();
    t({ result: !0, status: a, pageTranslated: a == "Translated" });
  }
  function bp(e, t) {
    jt.openImageTranslationFeedback(), t({ result: !0 });
  }
  function yp(e, t) {
    jt.openWebTranslationFeedback(), t({ result: !0 });
  }
  var ji = [];
  function vp(e, t) {
    try {
      let { imageUrl: a } = e;
      if (!bu(a))
        return t({ result: !1, errMsg: "\u56FE\u7247\u4E0D\u5B58\u5728" });
      yu({ originalUrl: a, triggerResultCallback: t }),
        jt.triggerTranslateImageBySrc(a);
    } catch {
      t({
        result: !1,
        errMsg: "\u7FFB\u8BD1\u8FC7\u7A0B\u53D1\u751F\u9519\u8BEF",
      });
    }
  }
  function xp(e, t) {
    let { imageId: a, imageUrl: n } = e,
      r = "";
    if (n) {
      let i = bu(n);
      i || t({ result: !1, errMsg: "\u627E\u4E0D\u5230\u539F\u56FE" }),
        (r = i?.getAttribute("bak_src") || "");
    } else {
      let i = Sr({ urlHash: a });
      if (!i) {
        t({
          result: !1,
          errMsg: "\u627E\u4E0D\u5230\u7FFB\u8BD1\u540E\u7684\u56FE",
        });
        return;
      }
      if (!bu(i.originalUrl)) {
        t({ result: !1, errMsg: "\u627E\u4E0D\u5230\u539F\u56FE" });
        return;
      }
      r = i.originalUrl;
    }
    jt.cleanTranslateImageBySrc(r);
  }
  function wp(e) {
    let { urlHash: t, imgData: a, originalUrl: n } = e,
      r = Sr({ originalUrl: n });
    r || (r = { originalUrl: n, urlHash: t }),
      (r.urlHash = t),
      yu(r),
      fu(t, { state: "extension_uploading", errorMsg: "" }),
      We.callHandler(
        "imageTextRecognition",
        { imageId: t, imageUrl: n, imageData: a },
        function (i) {
          let { imageId: o, boxes: s, result: u, errMsg: c } = i;
          u &&
            s &&
            fu(o, {
              state: "saved",
              errorMsg: "",
              result: { ocrTime: 0, boxesWithText: s },
            }),
            !u && c && fu(o, { state: "error", errorMsg: c });
        }
      );
  }
  function Ap(e) {
    let { urlHash: t } = e,
      a = Sr({ urlHash: t });
    if (!a) return;
    let n = a.imgState;
    return { urlHash: t, state: n };
  }
  function Ep(e) {
    let { imgHash: t, originalUrl: a, ok: n, errMsg: r } = e,
      i = Sr({ originalUrl: a });
    i && (yu(i), i.triggerResultCallback?.({ result: n, errMsg: r }));
  }
  function yu(e) {
    let t = Rv(e);
    if (t !== -1) {
      ji[t] = e;
      return;
    }
    ji.push(e);
  }
  function fu(e, t) {
    let a = Sr({ urlHash: e });
    a && (a.imgState = t);
  }
  function Rv(e) {
    return ji.findIndex(
      (t) => e.urlHash === t.urlHash || e.originalUrl === t.originalUrl
    );
  }
  function Sr(e) {
    return ji.find(
      (t) => e.urlHash === t.urlHash || e.originalUrl === t.originalUrl
    );
  }
  function bu(e) {
    let t =
      document.querySelector(`img[src="${e}"]`) ||
      document.querySelector(`img[bak_src="${e}"]`);
    if (t) return t;
    let a =
      document.querySelector(`[srcset*="${e}"]`) ||
      document.querySelector(`[bak_srcset*="${e}"]`);
    return a instanceof HTMLSourceElement
      ? a.parentElement?.querySelector("img")
      : a instanceof HTMLImageElement
      ? a
      : a instanceof HTMLPictureElement
      ? a.querySelector("img")
      : null;
  }
  async function vu() {
    try {
      if (!(await We.waitForBridge())) return;
      We.registerHandler("translateImage", vp),
        We.registerHandler("restoreImage", xp),
        We.registerHandler("translatePage", gp),
        We.registerHandler("restorePage", hp),
        We.registerHandler("getPageStatus", fp),
        We.registerHandler("togglePopup", dp),
        We.registerHandler("openPopup", mp),
        We.registerHandler("closePopup", pp),
        We.registerHandler("openImageTranslationFeedback", bp),
        We.registerHandler("openWebTranslationFeedback", yp),
        _v(),
        li.sendMessages({ type: "bridgeReady" });
    } catch {}
  }
  function _v() {
    li.handleMessages(async (e) => {
      try {
        let { type: t, data: a } = e,
          n = null;
        if (t === "triggerClientTranslateImage") wp(a);
        else if (t === "queryImageTranslateState") n = Ap(a);
        else if (t === "notifyClientImageTranslatedResult") Ep(a);
        else if (t === "getUserInfo") n = await jv();
        else if (t === "getBaseInfo") n = await Lv();
        else if (t === "updatePageStatus")
          We.callHandler("updatePageStatus", a, (r) => {});
        else return;
        li.sendMessages({ type: t, id: e.id, data: n });
      } catch {}
    });
  }
  function jv() {
    return new Promise((e) => {
      We.callHandler("getUserInfo", {}, (t) => {
        t.data ? e(t.data) : e(null);
      });
    });
  }
  function Lv() {
    return new Promise((e) => {
      We.callHandler("getBaseInfo", {}, (t) => {
        t.data ? e(t.data) : e(null);
      });
    });
  }
  pe() || vu();
  async function Nv() {
    try {
      let e = await Hv(_o(), {});
      if ((cp(), e.isTranslateExcludeUrl)) return;
      Gv(e);
      let t = e.rule.subtitleRule;
      t && t.type && !t.disabled && t.isInject && (Ov(), Uv(e));
      let a = e.rule.imageRule;
      a?.enable && a.type === "manga" && zv(e), qv(e);
    } catch {}
  }
  function Ov() {
    if (pe())
      try {
        globalThis.trustedTypes.defaultPolicy = trustedTypes.createPolicy(
          "default",
          { createHTML: (e) => e, createScript: (e) => e }
        );
      } catch (e) {
        X.error("breakTrustedTypes error", e);
      }
  }
  function zv(e) {
    if (pe()) {
      let a = ke().IMMERSIVE_TRANSLATE_IMAGE_INJECT,
        n = document.createElement("script");
      (n.id = "imt-image-inject"),
        (n.textContent = a),
        n.setAttribute("async", "true"),
        document.head?.insertBefore(n, document.head?.firstChild);
    } else {
      let t = ie.runtime.getURL("image/inject.js");
      if (document.querySelector(`script[src='${t}']`)) return;
      let n = document.createElement("script");
      (n.src = t),
        (n.id = "imt-image-inject"),
        n.setAttribute("async", "true"),
        document.head?.insertBefore(n, document.head?.firstChild);
    }
  }
  function Uv(e) {
    if (pe()) {
      let a = ke().IMMERSIVE_TRANSLATE_VIDEO_SUBTITLE_INJECT,
        n = document.createElement("script");
      (n.id = "imt-subtitles-inject"),
        (n.textContent = a),
        n.setAttribute("async", "true"),
        document.head?.insertBefore(n, document.head?.firstChild);
    } else {
      let t = ie.runtime.getURL("video-subtitle/inject.js");
      if (document.querySelector(`script[src='${t}']`)) return;
      let n = document.createElement("script");
      (n.src = t),
        n.setAttribute("async", "true"),
        (n.id = "imt-subtitles-inject"),
        document.head?.insertBefore(n, document.head?.firstChild);
    }
  }
  function qv(e) {
    if (!(!Xt() && !Zt()))
      if (pe()) {
        vu();
        return;
      } else {
        let t = ie.runtime.getURL("browser-bridge/inject.js");
        if (document.querySelector(`script[src='${t}']`)) return;
        let n = document.createElement("script");
        (n.src = t),
          (n.id = "imt-browser-bridge-inject"),
          n.setAttribute("async", "true"),
          document.head?.insertBefore(n, document.head?.firstChild);
      }
  }
  function Gv(e) {
    _i.handleMessages(({ id: t, type: a }) => {
      if (a === "getConfig") {
        let n = e.rule.subtitleRule;
        _i.sendMessages({ id: t, data: n });
      }
    });
  }
  Nv();
  var Fn = null;
  async function Hv(e, t) {
    let a = Object.keys(t);
    if (Fn) {
      let n = { url: e, config: Fn.config, state: { ...Fn.state, ...t } };
      Fn = await mu(n);
    } else {
      let n = await Kv(),
        r = t;
      a.length === 0 && (r = void 0),
        (Fn = await mu({ url: e, config: n, state: r }));
    }
    return Fn;
  }
  function Kv() {
    return pe()
      ? aa()
      : Wv({
          method: "getConfig",
          data: { userAgent: globalThis.navigator.userAgent },
        });
  }
  async function Wv(e) {
    return await iu().sendMessage("background:main", e);
  }
})();
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
