(() => {
  function Js(e) {
    let {
        onChange: t,
        type: n,
        title: r,
        command: a,
        description: i,
        shortcut: o,
        disabled: s,
        service: l,
        serviceIdList: u,
      } = e,
      [c, m] = z(!1),
      { t: p } = H();
    if (n === "finger")
      return d("div", {
        class: "nav",
        children: [
          d(Ce, { title: r, description: i || "" }),
          d("select", {
            class: "select !w-36",
            onChange: (g) => {
              g.preventDefault();
              let f = g.target.value;
              t(f);
            },
            children: Object.keys(V0).map((g, f) =>
              d(
                "option",
                { value: g, selected: o === g, children: `${p(g)}` },
                "finger-" + f
              )
            ),
          }),
        ],
      });
    if (n === "mouseHoverHoldKey") {
      let g = Si.includes(o);
      return d("div", {
        children: [
          d("div", {
            class: "nav",
            children: [
              d(Ce, { title: r, description: i || "" }),
              d(la, {
                className: "select",
                maxWidth: 250,
                items: Si.filter(
                  (f) => !((g && f === "OtherCustom") || f === "OtherCustom")
                ).map((f) => {
                  let h = p("mouseHoldKey", { key: Rr(f) }),
                    x = Si.includes(o),
                    b = f === o;
                  return (
                    o === "Off" && f === o && c && (b = !1),
                    f === "Auto"
                      ? (h = p("mouseHoldKeyAuto"))
                      : f === "Off"
                      ? (h = p("mouseHoldKeyOff"))
                      : f === "OtherCustom"
                      ? (h = x
                          ? p("mouseHoldKeyOther")
                          : p("mouseHoldKeyCustomKey", { key: o }))
                      : f === "Other"
                      ? (h = p("mouseHoldKeyOther"))
                      : f === "MouseHoldKeyPressHold" &&
                        (h = p("mouseHoldKeyPressHold")),
                    x || (f === "Other" && (b = !0)),
                    {
                      label: h,
                      value: f,
                      selected: b,
                      onSelected: (y) => {
                        y.value === "Other"
                          ? (m(!0), t("Off", !0))
                          : (m(!1), t(y.value));
                      },
                    }
                  );
                }),
              }),
            ],
          }),
          (!g || c) &&
            d("div", {
              class: "nav",
              children: [
                d(Ce, {
                  title: p("mouseHoverCustomKeyTitle"),
                  description: p("mouseHoverCustomKeyDescription") || "",
                }),
                d("input", {
                  type: "text",
                  class: "!w-36",
                  placeholder: p("mouseHoverShortcutPlaceholder"),
                  name: "mouseHoverHoldKey",
                  value: o === "Off" ? "" : o,
                  onChange: (f) => {
                    t(f.target.value);
                  },
                }),
              ],
            }),
        ],
      });
    } else if (n?.startsWith("translateWithCustom") && u?.length)
      return d("div", {
        class: "nav flex-wrap",
        children: [
          d(Ce, { title: r, description: i || "" }),
          d("div", {
            class: "nav",
            children: [
              d(la, {
                className: "select",
                style: {
                  marginBottom: "var(--spacing)",
                  marginRight: "var(--spacing)",
                },
                maxWidth: 250,
                items: u.map(({ service: g, name: f }) => {
                  let h = l === g;
                  return {
                    label: f || p(`translationServices.${g}`),
                    value: g,
                    selected: h,
                    onSelected: (x) => {
                      x.value === "Other"
                        ? (m(!0), t("Off", !0))
                        : (m(!1), t(x.value));
                    },
                  };
                }),
              }),
              d("div", {
                children: [
                  d("input", {
                    type: "text",
                    class: "!w-36",
                    disabled: s,
                    id: a,
                    name: a,
                    value: o,
                    onChange: (g) => {
                      t(g.target.value);
                    },
                  }),
                  s &&
                    d("div", {
                      class: "ml-2 inline-block",
                      children: d(Qe, {
                        width: 200,
                        multiple: !0,
                        text: p("shortcutDisableTooltip"),
                        children: d("span", {
                          children: d(b9, { style: { width: 20, height: 16 } }),
                        }),
                      }),
                    }),
                ],
              }),
            ],
          }),
        ],
      });
    return d("div", {
      class: "nav",
      children: [
        d(Ce, { title: r, description: i || "" }),
        d("div", {
          children: [
            d("input", {
              type: "text",
              class: "!w-36",
              disabled: s,
              id: a,
              name: a,
              value: o,
              onChange: (g) => {
                t(g.target.value);
              },
            }),
            s &&
              d("div", {
                class: "ml-2 inline-block",
                children: d(Qe, {
                  width: 200,
                  multiple: !0,
                  text: p("shortcutDisableTooltip"),
                  children: d("span", {
                    children: d(b9, { style: { width: 20, height: 16 } }),
                  }),
                }),
              }),
          ],
        }),
      ],
    });
  }
  function _5() {
    let { settingsHookValue: e, config: t, ctx: n } = ut(),
      [r, a, i, o, s] = e;
    ct(r, a);
    let [l, u] = di(),
      { t: c } = H();
    return t
      ? d(dt, {
          children: d(pe.Provider, {
            value: e,
            children: [
              d("div", {
                class: "nav",
                children: d("strong", {
                  class: "text-lg",
                  children: c("mouseHoverOptions"),
                }),
              }),
              d(mi, {
                image: An("images/hover_intro.png"),
                nightImage: An("images/hover_intro_night.png"),
                title: c("intro.hoverTitle"),
                description: c("intro.hoverDescription"),
              }),
              d(Ooe, { setSettings: a, config: t, localConfig: l }),
              d(joe, { setSettings: a, config: t, localConfig: l }),
              d(qs, {
                config: t,
                ctx: n,
                serviceType: "mouseHoverTranslationService",
              }),
            ],
          }),
        })
      : null;
  }
  function Ooe({ setSettings: e, config: t, localConfig: n }) {
    let { t: r } = H();
    return mr(n)
      ? d(Js, {
          onChange: (a, i) => {
            e((o) => {
              let l = {
                ...(o.generalRule || {}),
                mouseHoverHoldKey: a || "Off",
              };
              return (
                a !== "Off" && (l.mouseHoverPreferenceKey = a),
                { ...o, generalRule: l }
              );
            }),
              i || $e(r("saved"));
          },
          type: "mouseHoverHoldKey",
          title: r("mouseHoverHoldKey"),
          command: "mouseHoverHoldKey",
          description: r("desc.mouseHoverHoldKey"),
          shortcut: t.generalRule.mouseHoverHoldKey,
          disabled: !1,
        })
      : null;
  }
  function joe({ setSettings: e, config: t, localConfig: n }) {
    let { t: r } = H();
    return mr(n)
      ? null
      : d(Js, {
          onChange: (a, i) => {
            e((o) => {
              let l = {
                ...(o.generalRule || {}),
                touchShortcutsToggleTranslateTouchElement:
                  a || "touchShortcutsOff",
              };
              return (
                a &&
                  a !== "touchShortcutsOff" &&
                  (l.touchShortcutsToggleTranslateTouchElementPreferenceKey =
                    a),
                { ...o, generalRule: l }
              );
            }),
              i || $e(r("saved"));
          },
          type: "finger",
          title: r("touchShortcutsToggleTranslateTouchElement"),
          command: "touchShortcutsToggleTranslateTouchElement",
          description: r("desc.touchShortcutsToggleTranslateTouchElement"),
          shortcut: t.generalRule.touchShortcutsToggleTranslateTouchElement,
          disabled: !1,
        });
  }
  function B5() {
    let { settingsHookValue: e, ctx: t } = ut(),
      [n, r, a, i, o] = e;
    ct(n, r);
    let s = oe(
        (m) => {
          t && r((p) => Ia({ ...p }, m));
        },
        [t, r]
      ),
      l = Kl(t, n),
      { t: u } = H(),
      c = oe((m, p) => u("floatBallOptions." + m, p), []);
    return !t || !l
      ? d("div", { children: "loading" })
      : d(dt, {
          children: d(pe.Provider, {
            value: e,
            children: [
              d("div", {
                class: "nav",
                children: d("strong", {
                  class: "text-lg",
                  children: u("floatBallOptions"),
                }),
              }),
              d(mi, {
                image: An("images/float_ball_intro.png"),
                nightImage: An("images/float_ball_intro_night.png"),
                title: u("intro.floatBallTitle"),
                description: u("intro.floatBallDescription"),
              }),
              d(Uoe, {
                config: t.config,
                t: c,
                rawT: u,
                rule: l,
                updateFloatBallRule: s,
              }),
              d(zoe, {
                config: t.config,
                t: c,
                rawT: u,
                rule: l,
                updateFloatBallRule: s,
              }),
              d(Hoe, { t: c, rule: l, updateFloatBallRule: s }),
              d(qoe, { t: c, rule: l, updateFloatBallRule: s }),
              d(Voe, { t: c, rule: l, updateFloatBallRule: s }),
              d(Goe, { t: c, rule: l, updateFloatBallRule: s }),
            ],
          }),
        });
  }
  function Uoe({ rule: e, config: t, updateFloatBallRule: n, t: r, rawT: a }) {
    return d(ce, {
      children: d("div", {
        class: "nav",
        children: [
          d(Ce, {
            title: r("enable"),
            description: e.isPc
              ? r("enableDescPc")
              : r("enableDesc", {
                  touch: a(
                    "fingers." +
                      t.generalRule.fingerCountToToggleTranslagePageWhenTouching
                  ),
                }),
          }),
          d("label", {
            for: "enable",
            children: d("input", {
              type: "checkbox",
              id: "enable",
              name: "switch",
              role: "switch",
              onChange: (i) => {
                let o = i.target.checked;
                (e.enable = o), n(e);
              },
              checked: e.enable,
            }),
          }),
        ],
      }),
    });
  }
  function zoe({ rule: e, config: t, updateFloatBallRule: n, t: r, rawT: a }) {
    if (xe().any) return null;
    let i = e?.enablePinSidePanel == !1;
    return d(ce, {
      children: d("div", {
        class: "nav",
        children: [
          d(Ce, {
            title: r("enableSidePanel"),
            description: r("enableSidePanelDesc"),
          }),
          d("label", {
            for: "enable",
            children: d("input", {
              type: "checkbox",
              id: "enable",
              name: "switch",
              role: "switch",
              onChange: (o) => {
                let s = o.target.checked;
                (e.enablePinSidePanel = !s), n(e);
              },
              checked: i,
            }),
          }),
        ],
      }),
    });
  }
  function Hoe({ t: e, rule: t, updateFloatBallRule: n }) {
    let [r, a] = z(!1),
      [i, o] = z(""),
      [s, l] = z(-1);
    K(() => {
      Ot("esc", () => {
        a(!1);
      });
    }, []);
    let u = () => {
        a(!r);
      },
      c = (g, f, h, x) => {
        u(),
          s == f && f != -1
            ? (t.blockUrls[f] = g)
            : (t.blockUrls = Ln(g, t.blockUrls)),
          n(t),
          l(-1),
          o("");
      },
      m = (g, f) => {
        g.forEach((h) => {
          t.blockUrls = Qt(h, t.blockUrls);
        }),
          n(t),
          l(-1),
          o("");
      },
      p = (g, f, h) => {
        a(!r), o(g), l(h);
      };
    return d(ce, {
      children: [
        d("div", {
          class: "nav",
          children: [
            d(Ce, { title: e("blockUrls"), description: e("blockUrlDesc") }),
            d("div", {
              class: "nav-right",
              children: d("a", {
                href: "#",
                role: "button",
                class: "add-button secondary outline",
                onClick: (g) => {
                  g.preventDefault(), u();
                },
                children: e("add"),
              }),
            }),
            r
              ? d(Pa, {
                  toggleModal: (g) => {
                    g.preventDefault(), u();
                  },
                  onSubmitAddUrl: c,
                  type: "excludeMatches",
                  urlValue: i,
                  index: s,
                })
              : null,
          ],
        }),
        d(ui, {
          urls: t.blockUrls || [],
          type: "excludeMatches",
          onDeleteUrl: m,
          onEditUrl: p,
        }),
      ],
    });
  }
  function qoe({ t: e, rule: t, updateFloatBallRule: n }) {
    return d(ce, {
      children: d("div", {
        class: "nav",
        children: [
          d(Ce, { title: e("clickType"), description: e("clickTypeDesc") }),
          d("select", {
            class: "select",
            onChange: (r) => {
              r.preventDefault(), (t.clickType = r.target.value), n(t);
            },
            children: [
              d("option", {
                value: "translate",
                selected: t.clickType == "translate",
                children: e("clickTranslate"),
              }),
              d("option", {
                value: "popup",
                selected: t.clickType == "popup",
                children: e("clickPopup"),
              }),
            ],
          }),
        ],
      }),
    });
  }
  function Voe({ t: e, rule: t, updateFloatBallRule: n }) {
    return d(ce, {
      children: d("div", {
        class: "nav",
        children: [
          d(Ce, {
            title: e("fixedPosition"),
            description: e("fixedPositionDesc"),
          }),
          d("select", {
            class: "select",
            onChange: (r) => {
              r.preventDefault(), (t.fixedPosition = r.target.value), n(t);
            },
            children: [
              d("option", {
                value: "left",
                selected: t.fixedPosition == "left",
                children: e("fixedPositionLeft"),
              }),
              d("option", {
                value: "right",
                selected: t.fixedPosition == "right",
                children: e("fixedPositionRight"),
              }),
            ],
          }),
        ],
      }),
    });
  }
  function Goe({ t: e, rule: t, updateFloatBallRule: n }) {
    return d(ce, {
      children: d("div", {
        class: "nav",
        children: [
          d(Ce, { title: e("opacity"), description: e("opacityDesc") }),
          d("input", {
            class: "select",
            type: "number",
            value: t.transparency != null ? t.transparency : 30,
            min: 0,
            max: 100,
            onChange: (r) => {
              if (!r.target) return;
              let a = Number(r.target.value),
                i = Math.min(Math.max(a, 0), 100);
              (t.transparency = i), n(t);
            },
          }),
        ],
      }),
    });
  }
  function N5() {
    let { settingsHookValue: e, config: t, ctx: n } = ut(),
      [r, a, i, o, s] = e,
      [l] = di();
    ct(r, a);
    let { t: u, lang: c } = H(),
      m = J(
        () => (n ? ls(n, "translationService", c).map((h) => h.id) : []),
        [n, c]
      );
    if (!n || !t) return d("div", { children: "loading" });
    let p = cg
        .filter((f) => {
          if (f.name === "touch") {
            let h = mr(l);
            return I.debug("isSupportMouseHover", h), !h;
          } else if (f.name === "mouse") {
            let h = mr(l);
            return I.debug("isSupportMouseHover", h), h;
          } else return !0;
        })
        .map((f) => ({
          name: f.name,
          shortcuts: f.shortcuts.map((h) => {
            let x = "",
              b = "normal";
            typeof h == "string"
              ? (x = h)
              : ((x = h.command), h.type && (b = h.type));
            let y = "";
            u(`desc.${x}`) !== `desc.${x}` && (y = u(`desc.${x}`));
            let C = "";
            b === "finger"
              ? (C = F5(n, r, x))
              : b === "mouseHoverHoldKey"
              ? r?.generalRule && r?.generalRule[x] !== void 0
                ? (C = r.generalRule[x])
                : (C = t.generalRule[x])
              : r?.shortcuts && r?.shortcuts[x] !== void 0
              ? (C = r.shortcuts[x])
              : (C = t.shortcuts[x]);
            let S = u(`browser.${x}`, {
              targetLanguage: kt(t.targetLanguage, t.interfaceLanguage, !1, !0),
            });
            (b === "finger" || b === "mouseHoverHoldKey") && (S = u(`${x}`));
            let T, w;
            return (
              b.startsWith("translateWithCustom") &&
                ((T = r?.shortcuts?.translateWithCustomServices?.[x]),
                (w = m.map((A) => {
                  let M = { service: A, name: null };
                  return (M.name = va(n.config, A)), M;
                }))),
              {
                command: x,
                type: b,
                shortcut: C,
                title: S,
                description: y,
                service: T,
                serviceIdList: w,
                disabled: !(X(!0) || Pu()),
              }
            );
          }),
        })),
      g = () =>
        d("blockquote", {
          class: "text-sm",
          children: [
            u("browserShortcutsSucks"),
            " ",
            d("kbd", { children: "Ctrl+A" }),
            "\xA0",
            d("kbd", { children: "Alt+B" }),
            "\xA0",
            d("kbd", { children: "Command+C" }),
            "\xA0 ",
            d("kbd", { children: "Ctrl+Shift+D" }),
            " ",
          ],
        });
    return (
      X(!0) ||
        (In()
          ? (g = () =>
              d("blockquote", {
                class: "text-sm",
                children: [
                  u("browserShortcutsNoteForFirefox"),
                  "  ",
                  d("a", {
                    target: "_blank",
                    href: "https://support.mozilla.org/zh-CN/kb/%E7%AE%A1%E7%90%86Firefox%E7%9A%84%E6%89%A9%E5%B1%95%E5%BF%AB%E6%8D%B7%E6%96%B9%E5%BC%8F",
                    children: [u("help"), "?"],
                  }),
                ],
              }))
          : Pu() ||
            (g = () =>
              d("blockquote", {
                class: "text-sm",
                children: [
                  u("browserShortcutsNoteForChrome"),
                  "\xA0",
                  d("a", {
                    href: "#",
                    onClick: (f) => {
                      f.preventDefault(),
                        Y.tabs.create({ url: "chrome://extensions/shortcuts" });
                    },
                    children: u("goSettings"),
                  }),
                ],
              }))),
      d(dt, {
        children: d(pe.Provider, {
          value: e,
          children: [
            d("div", {
              class: "nav",
              children: d("strong", {
                class: "text-lg",
                children: u("shortcutSettingsTitle"),
              }),
            }),
            d("div", { children: d(g, {}) }),
            p.map((f, h) =>
              d(
                "article",
                {
                  class: "mb-8",
                  children: [
                    d("div", {
                      class: "text-gray-500 text-sm",
                      children: u(`shortcutGroup.${f.name}`),
                    }),
                    f.shortcuts.map((x, b) =>
                      el(Js, {
                        ...x,
                        key: `shortcut-${b}`,
                        onChange: (y, v) => {
                          a((C) => {
                            if (x.type === "finger") {
                              let T = {
                                ...(C.generalRule || {}),
                                [x.command]: y || "touchShortcutsOff",
                              };
                              return (
                                x.command ===
                                  "touchShortcutsToggleTranslateTouchElement" &&
                                  y &&
                                  y !== "touchShortcutsOff" &&
                                  (T.touchShortcutsToggleTranslateTouchElementPreferenceKey =
                                    y),
                                { ...C, generalRule: T }
                              );
                            } else if (x.type === "mouseHoverHoldKey") {
                              let T = {
                                ...(C.generalRule || {}),
                                [x.command]: y || "Off",
                              };
                              return (
                                y !== "Off" && (T.mouseHoverPreferenceKey = y),
                                { ...C, generalRule: T }
                              );
                            } else if (
                              x.type.startsWith("translateWithCustom")
                            ) {
                              let S = { ...C?.shortcuts };
                              if (m.includes(y)) {
                                let w = S.translateWithCustomServices || {};
                                return {
                                  ...C,
                                  shortcuts: {
                                    ...S,
                                    translateWithCustomServices: {
                                      ...w,
                                      [x.command]: y,
                                    },
                                  },
                                };
                              }
                              let T = {
                                ...C,
                                shortcuts: { ...S, [x.command]: y },
                              };
                              return y || (T.shortcuts[x.command] = ""), T;
                            } else {
                              let S = { ...C?.shortcuts },
                                T = {
                                  ...C,
                                  shortcuts: { ...S, [x.command]: y },
                                };
                              return y || (T.shortcuts[x.command] = ""), T;
                            }
                          }),
                            v || $e(u("saved"));
                        },
                      })
                    ),
                  ],
                },
                `shortcut-group-${h}`
              )
            ),
          ],
        }),
      })
    );
  }
  function O5() {
    let { settingsHookValue: e, config: t } = ut(),
      [n, r, a, i, o] = e,
      { t: s, setLang: l } = H();
    if (!t) return null;
    return d(dt, { children: d("div", { class: "pb-4", children: d(u, {}) }) });
    function u() {
      return (
        K(() => {
          n && c();
        }, [n]),
        d(ce, {
          children: d("div", {
            style: { width: "100%" },
            children: d("p", {
              children: [
                s("download_started"),
                " ",
                d("a", {
                  href: "#",
                  class: "secondary",
                  onClick: (m) => {
                    m.preventDefault(), c();
                  },
                  children: s("here"),
                }),
              ],
            }),
          }),
        })
      );
      async function c() {
        let m = location.href.includes("withTerms=true"),
          p = { ...n },
          g = "config";
        if (m) {
          let h = await td(s);
          h
            ? ((p.terms = h), (g = "config-with-terms"))
            : Ah(s("terms.noTermsToExport"));
        }
        let f = new Blob([JSON.stringify(p, null, 2)], {
          type: "application/json;charset=utf-8",
        });
        nl.saveAs(f, `${j}-${g}-${cc()}.json`);
      }
    }
  }
  function j5({ config: e, ctx: t }) {
    let n = xn(),
      { t: r, lang: a } = H(),
      i = Bt(e, t.isPro);
    return d(ce, {
      children: [
        d("header", {
          className: "header-navbar",
          children: [
            d("a", {
              class: "header-navbar-brand",
              href: i ? "javascript:void(0)" : te,
              target: "_blank",
              children: [
                d(UP, {}),
                d("h1", { children: r("browser.shortBrandName") }),
              ],
            }),
            d("span", {
              style: { cursor: "pointer" },
              class: "version",
              onClick: (o) => {
                o.preventDefault(),
                  !i && qr(`${te}docs/CHANGELOG/#${n.replace(/\./gi, "")}`);
              },
              children: ql(e),
            }),
            d("div", { style: { flex: 1 } }),
            d("div", {
              class: "header-right",
              hidden: i,
              children: [d(Woe, {}), d(Koe, {})],
            }),
          ],
        }),
        d("div", { className: "header-navbar-height" }),
      ],
    });
  }
  function Woe() {
    let { t: e } = H();
    return d("a", {
      class: "header-navbar-item mobile-hidden",
      href: JT,
      target: "_blank",
      children: [
        d(zP, {}),
        d("span", { children: e("options.navbar.installApp") }),
      ],
    });
  }
  function Koe() {
    let { t: e } = H();
    return d(Qe, {
      text: e("translateFileTooltip"),
      position: "bottom",
      width: 180,
      multiple: !0,
      containerClass: "btn-animate",
      children: d("a", {
        class: "header-navbar-item ml-8",
        href: sw,
        target: "_blank",
        children: [
          d(Vh, {}),
          d("span", { children: e("options.navbar.translateFile") }),
        ],
      }),
    });
  }
  function U5() {
    let { settingsHookValue: e, config: t, ctx: n } = ut(),
      [r, a, i, o, s] = e;
    ct(r, a);
    let { t: l } = H();
    if (!n || !t) return d("div", { children: "loading" });
    let u = Bg(t.supportedMangaSites),
      c = `<div>
<br>
    <a target="_blank" href="${GT}">${l("feedbackOrMore")}</a>
  </div>`,
      m = [
        ...u.map(
          (g, f) => `<span>
        <a target="_blank" href="${g.url}">${g.name}</a>
        ${f !== u.length - 1 ? ", " : ""}
      </span>`
        ),
        c,
      ].join(""),
      p = Ni(n, !0);
    return d(dt, {
      children: d(pe.Provider, {
        value: e,
        children: [
          d("div", {
            class: "nav",
            children: d("strong", {
              class: "text-lg",
              children: l(p ? "mangaAndImage" : "manga"),
            }),
          }),
          d(mi, {
            image: An("images/popup-manga-guide.png"),
            nightImage: An("images/popup-manga-guide.png"),
            title: l("intro.mangaTitle"),
            description: l("intro.mangaDescription", { 1: a3 }) + m,
            children: d("div", {
              class: "px-6",
              children: d(WN, { config: n.config, ctx: n }),
            }),
          }),
          d(WN, { config: n.config, ctx: n, className: "manga-enable" }),
          d("div", {
            className: "intro-footer",
            dangerouslySetInnerHTML: {
              __html: l("intro.mangaDescription", { 1: a3 }) + "<br/>" + m,
            },
          }),
          d(Yoe, { ctx: n }),
          d(Qoe, { ctx: n }),
        ],
      }),
    });
  }
  function WN({ config: e, ctx: t, className: n }) {
    let [r, a, i, o] = Ae(pe),
      { t: s } = H();
    return d(ce, {
      children: d("div", {
        class: `nav ${n}`,
        children: [
          d(Ce, {
            title: s("image.enableMangaFloatBall"),
            description: s("image.enableMangaFloatBallDesc"),
          }),
          d("label", {
            for: "enable",
            children: d("input", {
              type: "checkbox",
              id: "enable",
              name: "switch",
              role: "switch",
              onChange: (l) => {
                let u = l.target.checked;
                a((c) => ({
                  ...c,
                  generalRule: {
                    ...c.generalRule,
                    "imageRule.add": {
                      ...c.generalRule?.["imageRule.add"],
                      enableMangaFloatBall: u,
                    },
                  },
                }));
              },
              checked: e.generalRule.imageRule.enableMangaFloatBall,
            }),
          }),
        ],
      }),
    });
  }
  var KN = 768;
  function Yoe({ ctx: e }) {
    let { t } = H(),
      n = Ni(e, !0);
    return globalThis.innerWidth < KN || !n
      ? null
      : d(ce, {
          children: [
            d(mi, {
              image: An("images/image_tansalte_intro-qkKVLH.png"),
              nightImage: An("images/image_tansalte_intro-qkKVLH.png"),
              title: t("image.imageTranslate"),
              description: t("image.imageTranslateDes", { 1: aw }),
              children: d("div", {
                class: "px-6",
                children: [
                  d(Zoe, { ctx: e }),
                  d(Joe, { ctx: e, styles: { paddingTop: 0 } }),
                  d(QN, { ctx: e, styles: { paddingTop: 0 } }),
                  d(ZN, { ctx: e, styles: { paddingTop: 0 } }),
                ],
              }),
            }),
            d(YN, { ctx: e }),
          ],
        });
  }
  function Qoe({ ctx: e }) {
    let t = Ni(e, !0);
    return globalThis.innerWidth > KN || !t
      ? null
      : d(ce, {
          children: d("div", {
            style: { paddingTop: 24 },
            children: [
              d(QN, { ctx: e, styles: { paddingBottom: 0 } }),
              R0(e) && d(YN, { ctx: e }),
              d(ZN, { ctx: e, styles: { paddingTop: 0 } }),
            ],
          }),
        });
  }
  function YN({ ctx: e }) {
    let [t, n, r, a, i] = Ae(pe),
      { t: o } = H(),
      s = e.config;
    return mr(e.localConfig || {})
      ? null
      : d(Js, {
          onChange: (l, u) => {
            n((c) => {
              let p = {
                ...(c.generalRule || {}),
                touchShortcutsToggleTranslateTouchElement:
                  l || "touchShortcutsOff",
              };
              return (
                l &&
                  l !== "touchShortcutsOff" &&
                  (p.touchShortcutsToggleTranslateTouchElementPreferenceKey =
                    l),
                { ...c, generalRule: p }
              );
            }),
              u || $e(o("saved"));
          },
          type: "finger",
          title: o("touchShortcutsToggleTranslateTouchAndImgElement"),
          command: "touchShortcutsToggleTranslateTouchElement",
          description: o(
            "desc.touchShortcutsToggleTranslateTouchAndImgElement"
          ),
          shortcut: s.generalRule.touchShortcutsToggleTranslateTouchElement,
          disabled: !1,
        });
  }
  function Zoe({ ctx: e, className: t }) {
    let [n, r, a, i, o] = Ae(pe),
      s = e.rule.imageRule,
      { t: l } = H();
    return d(ce, {
      children: d("div", {
        class: `nav ${t}`,
        children: [
          d(Ce, { title: l("image.enableTools") }),
          d("label", {
            for: "enable",
            children: d("input", {
              type: "checkbox",
              id: "enable",
              name: "switch",
              role: "switch",
              onChange: (u) => {
                let c = u.target.checked;
                r((m) => ({
                  ...m,
                  generalRule: {
                    ...m.generalRule,
                    "imageRule.add": {
                      ...m.generalRule?.["imageRule.add"],
                      enableTools: c,
                    },
                  },
                }));
              },
              checked: s.enableTools,
            }),
          }),
        ],
      }),
    });
  }
  function QN({ ctx: e, className: t, styles: n }) {
    let [r, a, i, o, s] = Ae(pe),
      { t: l } = H();
    return d(ce, {
      children: d("div", {
        class: `nav ${t}`,
        style: { ...n },
        children: [
          d(Ce, {
            title: Ar(e.localConfig || {})
              ? l("image.touchShortcuts")
              : l("image.enableMouseHover"),
          }),
          d("label", {
            for: "enable",
            children: d("input", {
              type: "checkbox",
              id: "enable",
              name: "switch",
              role: "switch",
              onChange: (u) => {
                let c = u.target.checked;
                a((m) => ({
                  ...m,
                  generalRule: {
                    ...m.generalRule,
                    "imageRule.add": {
                      ...m.generalRule?.["imageRule.add"],
                      enableMouseHover: c,
                    },
                  },
                }));
              },
              checked: R0(e),
            }),
          }),
        ],
      }),
    });
  }
  function ZN({ ctx: e, className: t, styles: n }) {
    let r = Ic(),
      a = e.config.generalRule.imageRule;
    if (a.supportPlatform?.[r] != "all") return null;
    let { t: o } = H(),
      s = J(() => q9(e, e.isPro), [e, a]),
      [l, u, c, m, p] = Ae(pe),
      g = oe(
        (f) => {
          let h = f.target.value;
          if (h) {
            if (!e.isPro && h == "pro") {
              globalThis.open(i3, "_blank"), globalThis.location.reload();
              return;
            }
            u((x) => {
              let b = {
                ...x,
                generalRule: {
                  ...x.generalRule,
                  "imageRule.add": {
                    ...x.generalRule?.["imageRule.add"],
                    imageTranslateProvider: h,
                  },
                },
              };
              return (
                h == "client" &&
                  b.disableShowClientImageGuide == null &&
                  (b.disableShowClientImageGuide = !0),
                b
              );
            });
          }
        },
        [u]
      );
    return d("fieldset", {
      class: `nav ${t || ""}`,
      style: { justifyContent: "unset", ...n },
      children: [
        d("legend", { class: "mb-4", children: o("chooseProviderLabel") }),
        d("div", {
          class: "flex flex-col",
          children: ["client", "pro"].map((f) => {
            let h = o(`chooseProvider.${f}`),
              x = o(`chooseProvider.${f}Desc`, { 1: i3 });
            return (
              e.isPro &&
                f == "client" &&
                ((x = o("chooseProvider.clientDescPro")),
                (h = o("chooseProvider.clientPro"))),
              d("label", {
                class: "pro-radio-label",
                for: f,
                children: [
                  d("div", {
                    class: "pro-input-radio",
                    children: d("input", {
                      type: "radio",
                      id: f,
                      name: f,
                      value: f,
                      onClick: g,
                      checked: f === s,
                    }),
                  }),
                  d("div", {
                    class: "pl-4",
                    children: [
                      d("div", { class: "pro-radio-title", children: h }),
                      d("small", {
                        class: "pro-radio-desc muted",
                        dangerouslySetInnerHTML: { __html: x },
                      }),
                    ],
                  }),
                ],
              })
            );
          }),
        }),
      ],
    });
  }
  function Joe({ ctx: e, className: t, styles: n }) {
    let { t: r } = H(),
      [a, i, o, s] = Ae(pe);
    return d("div", {
      class: `nav ${t}`,
      style: n,
      children: [
        d(Ce, {
          title: r("image.toolsDelayTime"),
          description: r("image.toolsDelayTimeDes"),
        }),
        d("label", {
          for: "count",
          class: "option-input",
          children: d("input", {
            type: "number",
            id: "count",
            onChange: (l) => {
              i((u) => ({
                ...u,
                generalRule: {
                  ...u.generalRule,
                  "imageRule.add": {
                    ...u.generalRule?.["imageRule.add"],
                    toolsDelayTime: parseInt(l.target.value || "0"),
                  },
                },
              }));
            },
            name: "count",
            value: e.config.generalRule.imageRule.toolsDelayTime,
            required: !0,
          }),
        }),
      ],
    });
  }
  async function JN() {
    try {
      if (X()) return;
      let e = Y.runtime.getURL("locales.json"),
        n = await (await fetch(e)).json();
      Object.assign(Ts, n);
    } catch {}
  }
  function j4() {
    let { t: e } = H(),
      [t, n] = z(!1),
      [r, a] = z(null),
      [i, o] = z(null),
      [s, l] = z(null),
      u = async (p) => {
        if (!p) {
          o(e("terms.noFileSelected"));
          return;
        }
        n(!0), o(null), l(null);
        try {
          let g = await Xoe(p);
          a(p.name);
          let { glossaries: f, hasInvalidItems: h } = d1(g);
          if (f.length === 0) {
            o(e("terms.noValidTermsInFile")), n(!1);
            return;
          }
          l(f);
        } catch {
          o(e("terms.csvParseError"));
        } finally {
          n(!1);
        }
      };
    return {
      isLoading: t,
      error: i,
      importedGlossaries: s,
      importedFileName: r,
      importFile: u,
      triggerFileSelect: () =>
        new Promise((p) => {
          let g = document.createElement("input");
          (g.type = "file"),
            (g.accept = ".csv,.txt"),
            (g.onchange = async (f) => {
              let x = f.target.files?.[0];
              if (!x) {
                p(!1);
                return;
              }
              await u(x), p(!0);
            }),
            (g.oncancel = () => {
              p(!1);
            }),
            g.click();
        }),
      clearImportResult: () => {
        l(null), o(null);
      },
    };
  }
  function Xoe(e) {
    return new Promise((t, n) => {
      let r = new FileReader();
      (r.onload = (a) => {
        let i = a.target?.result;
        if (!i) {
          n(new Error("Failed to read file buffer"));
          return;
        }
        try {
          let s = new TextDecoder("utf-8", { fatal: !0 }).decode(i);
          t(s);
        } catch {
          try {
            let l = new TextDecoder("gbk").decode(i);
            t(l);
          } catch {
            try {
              let u = new TextDecoder("utf-8", { fatal: !1 }).decode(i);
              t(u);
            } catch {
              n(
                new Error(
                  "Failed to decode file with UTF-8 (fatal and non-fatal) or GBK."
                )
              );
            }
          }
        }
      }),
        (r.onerror = (a) => {
          n(new Error("Failed to read file"));
        }),
        r.readAsArrayBuffer(e);
    });
  }
  function XN(e, t = {}) {
    let { initialItemsPerPage: n = 50 } = t,
      [r, a] = z(1),
      [i, o] = z(n),
      s = J(() => Math.max(1, Math.ceil(e.length / i)), [e.length, i]),
      l = J(() => {
        let g = (r - 1) * i,
          f = Math.min(g + i, e.length);
        return e.slice(g, f);
      }, [e, r, i]),
      u = oe(() => {
        a((g) => Math.max(1, g - 1));
      }, []),
      c = oe(() => {
        a((g) => Math.min(s, g + 1));
      }, [s]),
      m = oe(
        (g) => {
          let f = Math.max(1, Math.min(g, s));
          a(f);
        },
        [s]
      ),
      p = oe((g) => {
        let f = Math.max(1, g);
        o(f), a(1);
      }, []);
    return {
      currentPage: r,
      totalPages: s,
      paginatedData: l,
      itemsPerPage: i,
      goToPrevPage: u,
      goToNextPage: c,
      goToPage: m,
      setItemsPerPage: p,
      setCurrentPage: a,
    };
  }
  function $N({ isOpen: e, onClose: t, onImport: n, onCreateNew: r }) {
    let { t: a } = H();
    if (!e) return null;
    let i = (o) => {
      o?.target?.id === j + "-glossary-overlay" && t();
    };
    return d("dialog", {
      id: j + "-glossary-overlay",
      onClick: i,
      open: !0,
      children: d("article", {
        class: "add-modal",
        children: [
          d("a", {
            "aria-label": "Close",
            class: "close cursor-pointer",
            "data-target": "modal-example",
            onClick: t,
          }),
          d("div", { class: "add-text", children: a("terms.addCustomTitle") }),
          d("p", { children: a("terms.importCsvDescription") }),
          d("blockquote", {
            children: d("div", {
              class: "font-mono text-sm overflow-auto",
              children: [
                "source,target,tgt_lng",
                d("br", {}),
                "LLM,LLM,zh-CN",
                d("br", {}),
                "LLMs,LLM,zh-CN",
              ],
            }),
          }),
          d("div", {
            class: "flex",
            children: [
              d("div", {
                class: "footer-button",
                children: d("a", {
                  href: "#import",
                  role: "button",
                  class: "full-button secondary margin-right",
                  "data-target": "modal-example",
                  onClick: (o) => {
                    o.preventDefault(), n();
                  },
                  children: a("terms.importCsvButton"),
                }),
              }),
              d("div", {
                class: "footer-button",
                children: d("a", {
                  href: "#create",
                  role: "button",
                  class: "full-button margin-left",
                  "data-target": "modal-example",
                  onClick: (o) => {
                    o.preventDefault(), r();
                  },
                  children: a("terms.createButton"),
                }),
              }),
            ],
          }),
        ],
      }),
    });
  }
  function z5() {
    let [e, t] = z([]),
      n = le([]);
    n.current = e;
    let [r, a] = z(!1),
      [i, o] = z(!1),
      { t: s } = H(),
      { settingsHookValue: l, ctx: u } = ut(),
      [c, m, p, g, f] = l;
    ct(c, m);
    let h = le(!1),
      [x, b] = z(!1);
    K(() => {
      if (!u?.rule || h.current) return;
      h.current = !0;
      let M = async () => {
        g1(u.config, (R, O) => {
          (R == 1 && n.current.length > O.length) || (t(O), R == 2 && b(!1));
        });
      };
      p1(u, s).then(() => M()), M();
    }, [u, t, n]);
    let {
      error: y,
      importedGlossaries: v,
      importedFileName: C,
      triggerFileSelect: S,
      clearImportResult: T,
    } = j4();
    K(() => {
      v &&
        v.length > 0 &&
        !r &&
        (async () => {
          a(!0);
          try {
            let M = `custom-${ki(8)}`,
              O = (C || s("terms.unname")).replace(/\.(csv|txt)$/i, ""),
              B = {
                id: M,
                name: O,
                description: "",
                matches: ["*"],
                author: "self",
                glossaries: v,
                active: !0,
                lastUserOpTime: Date.now() + "",
              };
            await Kr(B), (window.location.href = `#terms/${B.id}`);
          } catch {
            alert(s("terms.saveError"));
          } finally {
            a(!1), T();
          }
        })();
    }, [v, r, s, T, C]),
      K(() => {
        y && (alert(y), T());
      }, [y, T]);
    let w = async () => {
        o(!0);
      },
      A = async () => {
        try {
          b(!0);
          let R = {
            id: `custom-${ki(8)}`,
            name: s("terms.unname"),
            description: "",
            matches: ["*"],
            author: "self",
            glossaries: [],
            active: !0,
            lastUserOpTime: Date.now() + "",
          };
          await Kr(R),
            await fc(Date.now() + ""),
            (window.location.href = `#terms/${R.id}`);
        } catch {
          alert("\u521B\u5EFA\u672F\u8BED\u5E93\u5931\u8D25");
        } finally {
          b(!1), o(!1);
        }
      };
    return d(dt, {
      children: d(pe.Provider, {
        value: l,
        children: [
          d("div", {
            style: { minHeight: "calc(100vh - 100px)" },
            children: [
              d("div", {
                className: "nav",
                children: d("div", {
                  children: [
                    d("strong", {
                      className: "text-lg",
                      children: s("field.terms"),
                    }),
                    d(Qe, {
                      text: s("terms.addCustomTooltip"),
                      position: xe().any ? "right" : "bottom",
                      enableMobile: !0,
                      tipStyle: { whiteSpace: "pre" },
                      children: d("div", {
                        class: "ml-2 cursor-pointer text-gray-500",
                        children: d(Zc, {}),
                      }),
                    }),
                  ],
                }),
              }),
              d("div", {
                className: "w-full flex justify-between mb-4",
                children: [
                  d("a", {
                    className: "underline text-sm cursor-pointer",
                    style: { color: "#999999" },
                    onClick: w,
                    children: s("terms.addCustom"),
                  }),
                  d("a", {
                    href: `https://github.com/${j}/terms`,
                    className: "underline text-sm",
                    style: { color: "#999999" },
                    target: "_blank",
                    children: s("terms.contribute"),
                  }),
                ],
              }),
              d("div", {
                className: "flex flex-wrap",
                children: e?.map((M) =>
                  d(
                    $oe,
                    {
                      data: M,
                      targetLanguage: u?.config.interfaceLanguage || "",
                      ctx: u,
                    },
                    M.id
                  )
                ),
              }),
            ],
          }),
          d(s1, { hidden: !x }),
          d($N, {
            isOpen: i,
            onClose: () => o(!1),
            onImport: () => {
              S(), o(!1);
            },
            onCreateNew: A,
          }),
        ],
      }),
    });
  }
  function $oe({ data: e, targetLanguage: t, ctx: n }) {
    let [r, a] = z({}),
      { name: i, description: o } = m1(t || "", e),
      s = async (l) => {
        l.preventDefault(), l.stopPropagation();
        let u = !e.active;
        if (
          ((e.active = u),
          lu(e) &&
            Te({
              key: "terms_switch",
              ctx: n,
              params: { ai_assistant: e.id, ai_assistant_use: u ? "1" : "0" },
            }),
          a({}),
          !e.glossaries?.length && e.author != "self")
        ) {
          let { glossaries: m, langsHash: p } = await nd(
            n?.config.termsBaseUrl || "",
            ["auto", t],
            e
          );
          (e.glossaries = m), (e.localLangsHash = p);
        }
        let c = { ...e, active: u, lastUserOpTime: Date.now() + "" };
        await fc(Date.now() + ""), await Kr(c);
      };
    return d("div", {
      className: "store-container",
      style: { paddingBottom: 0 },
      onClick: () => {
        window.location.href = `#terms/${e.id}`;
      },
      children: [
        d("div", {
          className: "flex items-center justify-between mb-4",
          children: [
            d("div", {
              className:
                "mb-0 pr-2 flex justify-start flex-col text-left assistant-content flex-1 cursor-pointer",
              children: [
                d("div", {
                  className: "text-base font-bold assistant-text",
                  children: i,
                }),
                d("div", {
                  className: "font-normal assistant-text",
                  style: { color: "#999999", fontSize: "14px" },
                  children: e.author ? `@${e.author}` : "",
                }),
              ],
            }),
            d("div", {
              className: "flex items-center",
              onClick: (l) => l.stopPropagation(),
              children: d("label", {
                className: "relative inline-flex items-center cursor-pointer",
                onClick: s,
                children: d("input", {
                  type: "checkbox",
                  role: "switch",
                  checked: e.active,
                }),
              }),
            }),
          ],
        }),
        d("p", {
          class: "cursor-pointer",
          dangerouslySetInnerHTML: { __html: o },
        }),
      ],
    });
  }
  function eO({
    total: e,
    currentPage: t,
    totalPages: n,
    itemsPerPage: r,
    goToPrevPage: a,
    goToNextPage: i,
    onItemsPerPageChange: o,
  }) {
    let { t: s } = H(),
      l = `items-per-page-${t}`;
    return e < r
      ? null
      : d("nav", {
          "aria-label": "Pagination",
          class: "flex flex-row items-center",
          children: [
            d("div", {
              class: "flex flex-row",
              children: [
                d("button", {
                  onClick: a,
                  role: "button",
                  style: "margin-right: var(--spacing);",
                  disabled: t <= 1,
                  "aria-label": s("pagination.prev"),
                  children: s("pagination.prev"),
                }),
                d("button", {
                  onClick: i,
                  role: "button",
                  disabled: t >= n,
                  "aria-label": s("pagination.next"),
                  children: s("pagination.next"),
                }),
              ],
            }),
            d("div", { children: d("div", { children: [t, " / ", n] }) }),
            d("div", {
              children: d("div", {
                children: [
                  d("label", {
                    for: l,
                    class: "sr-only",
                    children: s("pagination.itemsPerPage"),
                  }),
                  d("select", {
                    class: "select",
                    id: l,
                    value: r,
                    onChange: o,
                    children: [
                      d("option", { value: "10", children: "10" }),
                      d("option", { value: "20", children: "20" }),
                      d("option", { value: "50", children: "50" }),
                      d("option", { value: "100", children: "100" }),
                    ],
                  }),
                ],
              }),
            }),
          ],
        });
  }
  function tO({
    glossaries: e,
    onGlossariesUpdate: t,
    onDeleteTerm: n,
    defaultLanguage: r = "en",
    termsName: a = "glossary",
  }) {
    let { t: i } = H(),
      o = En(),
      [s] = o,
      l = Zi(s),
      [u, c] = z(!1),
      [m, p] = z({ k: "", v: "", tl: r }),
      {
        currentPage: g,
        totalPages: f,
        paginatedData: h,
        itemsPerPage: x,
        goToPrevPage: b,
        goToNextPage: y,
        goToPage: v,
        setItemsPerPage: C,
        setCurrentPage: S,
      } = XN(e, { initialItemsPerPage: 50 });
    K(() => {
      g > f && S(f);
    }, [f, g, S]);
    let T = (L) => {
        let q = parseInt(L.target.value, 10);
        C(q);
      },
      {
        error: w,
        importedGlossaries: A,
        importFile: M,
        clearImportResult: R,
      } = j4();
    K(() => {
      A && A.length > 0 && (t(A), R(), S(1));
    }, [A, t, R, S]);
    let O = l?.translationService
        ? Wi(l.translationService, l.targetLanguage, l)
        : [],
      B = (L, q, U) => {
        let V = (g - 1) * x + L,
          G = [...e];
        V >= 0 && V < G.length && ((G[V] = { ...G[V], [q]: U }), t(G));
      },
      _ = () => {
        c(!0);
      },
      N = () => {
        m.k.trim() && (t([m, ...e]), p({ k: "", v: "", tl: r }), c(!1), S(1));
      },
      F = (L) => {
        let q = (g - 1) * x + L;
        if (q >= 0 && q < e.length) {
          let U = e[q];
          U && n(U);
        }
      },
      D = (L) => {
        let q = L.target,
          U = q.files?.[0];
        U && M(U), (q.value = "");
      },
      P = () => {
        let L = X9(e),
          q = new Blob([L], { type: "text/csv" }),
          U = URL.createObjectURL(q),
          V = document.createElement("a");
        (V.href = U),
          (V.download = `${a}_glossary.csv`),
          document.body.appendChild(V),
          V.click(),
          setTimeout(() => {
            document.body.removeChild(V), URL.revokeObjectURL(U);
          }, 0);
      },
      E = (L) => {
        L?.target?.id === j + "-glossary-modal-overlay" && c(!1);
      };
    return d("div", {
      children: [
        d("style", {
          children: `
          .glossary-table th, .glossary-table td {
              padding: 0.1rem 0.4rem; /* Reduced padding */
              font-size: 0.9rem; /* Reduced font size */
              vertical-align: middle; /* Align text vertically */
          }
          .glossary-table td textarea {
            border: 0px;
          }
          .glossary-table textarea, .glossary-table select {
              font-size: 0.9rem; /* Match font size */
              padding: 0.2rem 0.4rem; /* Adjust padding */
              margin-bottom: 0; /* Remove default pico margin */
          }
          .glossary-table textarea {
              min-height: auto; /* Allow textarea to shrink */
              line-height: 1.2; /* Adjust line height */
          }
          .glossary-table select {
              padding-right: calc(1.5rem + 0.4rem); /* Adjust icon padding for select */
          }
          .glossary-table td:last-child a {
              padding: 0.1rem 0.2rem; /* Reduced padding for delete icon */
          }

          @media (max-width: 768px) {
            .glossary-table th:nth-child(1) {
              width: 30%;
            }
            .glossary-table th:nth-child(2) {
              width: 30%;
            }
            .glossary-table th:nth-child(3) {
              width: 30%;
            }
            .glossary-table th:nth-child(4) {
              width: 10%;
            }
            /* Adjust padding for smaller icons/text on mobile */
            .glossary-table td:last-child a {
                padding: 0.05rem 0.1rem; /* Further reduce padding on mobile */
            }
            .glossary-table th, .glossary-table td {
                padding: 0.2rem 0.3rem; /* Smaller padding on mobile */
                font-size: 0.8rem; /* Smaller font on mobile */
            }
            .glossary-table textarea {
                 font-size: 0.8rem; /* Smaller font in textarea */
                 line-height: 1.1;
                 resize: none; /* Hide resize handle on mobile */
            }
            .glossary-table select {
                font-size: 0.8rem; /* Smaller font in select */
                padding: 0.1rem 0.2rem; /* Adjust padding */
                padding-right: calc(1.2rem + 0.2rem); /* Adjust icon padding */
            }
          }
        `,
        }),
        d("div", {
          class: "grid",
          style: { marginBottom: "var(--spacing)" },
          children: d("div", {
            children: [
              d("input", {
                id: "import-glossary",
                type: "file",
                accept: ".csv,.txt",
                style: { display: "none" },
                onChange: D,
              }),
              d("a", {
                href: "javascript:void(0)",
                role: "button",
                class: "secondary",
                style: { marginRight: "var(--spacing)" },
                onClick: () =>
                  document.getElementById("import-glossary")?.click(),
                children: [Wh, i("pureImport")],
              }),
              d("a", {
                href: "javascript:void(0)",
                role: "button",
                style: { marginRight: "var(--spacing)" },
                onClick: P,
                children: [Gh, i("pureExport")],
              }),
              d("a", {
                href: "javascript:void(0)",
                role: "button",
                onClick: _,
                children: i("add"),
              }),
            ],
          }),
        }),
        w &&
          d("blockquote", {
            style: { color: "var(--del-color)" },
            children: w,
          }),
        d("div", {
          children: [
            d("table", {
              role: "grid",
              class: "glossary-table",
              children: [
                d("thead", {
                  children: d("tr", {
                    children: [
                      d("th", { style: { width: "5%" } }),
                      d("th", {
                        style: { width: "30%" },
                        children: [i("terms.term"), "(", e.length, ")"],
                      }),
                      d("th", {
                        style: { width: "30%" },
                        children: i("terms.translation"),
                      }),
                      d("th", {
                        style: { width: "25%" },
                        children: i("terms.language"),
                      }),
                      d("th", {
                        style: { width: "10%" },
                        children: i("actions"),
                      }),
                    ],
                  }),
                }),
                d("tbody", {
                  children: [
                    h.map((L, q) =>
                      d(
                        "tr",
                        {
                          children: [
                            d("td", {
                              style: { fontSize: "0.8rem" },
                              children: q + 1 + (g - 1) * x,
                            }),
                            d("td", {
                              children: d("textarea", {
                                value: L.k,
                                rows: 1,
                                placeholder: i("terms.termPlaceholder"),
                                onChange: (U) => B(q, "k", U.target.value),
                              }),
                            }),
                            d("td", {
                              children: d("textarea", {
                                value: L.v,
                                rows: 1,
                                placeholder: i("terms.translationPlaceholder"),
                                onChange: (U) => B(q, "v", U.target.value),
                              }),
                            }),
                            d("td", {
                              children: d("select", {
                                value: L.tl || "auto",
                                onChange: (U) =>
                                  B(
                                    q,
                                    "tl",
                                    U.target.value === "auto"
                                      ? ""
                                      : U.target.value
                                  ),
                                children: [
                                  d("option", {
                                    value: "auto",
                                    children: i("auto"),
                                  }),
                                  O.filter((U) => U !== "auto").map((U) =>
                                    d(
                                      "option",
                                      {
                                        value: U,
                                        children: kt(U, l?.interfaceLanguage),
                                      },
                                      `lang-${q}-${U}`
                                    )
                                  ),
                                ],
                              }),
                            }),
                            d("td", {
                              children: d("a", {
                                href: "javascript:void(0)",
                                onClick: () => F(q),
                                title: i("delete"),
                                style: {
                                  display: "inline-flex",
                                  alignItems: "center",
                                  justifyContent: "center",
                                  padding: "0.25rem",
                                },
                                children: d(si, {
                                  style: { width: 32, height: 32 },
                                }),
                              }),
                            }),
                          ],
                        },
                        `${L.k}-${q}`
                      )
                    ),
                    d("tr", {
                      hidden: e.length > 0,
                      children: d("td", {
                        colSpan: 5,
                        style: { textAlign: "center", padding: "1rem" },
                        children: i("noData"),
                      }),
                    }),
                  ],
                }),
              ],
            }),
            d(eO, {
              total: e.length,
              currentPage: g,
              totalPages: f,
              itemsPerPage: x,
              goToPrevPage: b,
              goToNextPage: y,
              onItemsPerPageChange: T,
            }),
          ],
        }),
        u &&
          d("dialog", {
            id: j + "-glossary-modal-overlay",
            onClick: E,
            open: !0,
            children: d("article", {
              class: "add-modal",
              children: [
                d("a", {
                  "aria-label": "Close",
                  class: "close cursor-pointer",
                  "data-target": "modal-example",
                  onClick: () => c(!1),
                }),
                d("div", {
                  class: "add-text",
                  children: i("terms.addNewTerm"),
                }),
                d("div", {
                  children: [
                    d("label", { children: i("terms.term") }),
                    d("input", {
                      type: "text",
                      value: m.k,
                      onChange: (L) => p({ ...m, k: L.target.value }),
                      placeholder: i("terms.termPlaceholder"),
                    }),
                  ],
                }),
                d("div", {
                  children: [
                    d("label", { children: i("terms.translation") }),
                    d("input", {
                      type: "text",
                      value: m.v,
                      onChange: (L) => p({ ...m, v: L.target.value }),
                      placeholder: i("terms.translationPlaceholder"),
                    }),
                  ],
                }),
                d("div", {
                  children: [
                    d("label", { children: i("terms.language") }),
                    d("select", {
                      value: m.tl,
                      onChange: (L) => p({ ...m, tl: L.target.value }),
                      children: [
                        d("option", { value: "auto", children: i("auto") }),
                        O.filter((L) => L !== "auto").map((L) =>
                          d(
                            "option",
                            { value: L, children: kt(L, l?.interfaceLanguage) },
                            L
                          )
                        ),
                      ],
                    }),
                  ],
                }),
                d("div", {
                  class: "flex",
                  children: [
                    d("div", {
                      class: "footer-button",
                      children: d("a", {
                        href: "javascript:void(0)",
                        role: "button",
                        class: "full-button secondary margin-right",
                        "data-target": "modal-example",
                        onClick: () => c(!1),
                        children: i("cancel"),
                      }),
                    }),
                    d("div", {
                      class: "footer-button",
                      children: d("a", {
                        href: "javascript:void(0)",
                        role: "button",
                        class: "full-button margin-left",
                        "data-target": "modal-example",
                        onClick: N,
                        children: i("confirm"),
                      }),
                    }),
                  ],
                }),
              ],
            }),
          }),
      ],
    });
  }
  function H5() {
    let e = cL(),
      { t } = H(),
      [n, r] = z(null),
      { settingsHookValue: a, config: i, ctx: o } = ut(),
      [s, l, u, c, m] = a,
      [p, g] = z(!1),
      [f, h] = z(null),
      x = le(!1),
      [b, y] = z(!0),
      [v, C] = z(!1);
    K(() => {
      if (!e || !o?.rule || n || x.current) return;
      (x.current = !0),
        (async () => {
          let _ = null,
            N = await Ja(e);
          if (
            (N?.id && ((_ = N), y(!1), r(N), g(!0)),
            o.isPro && o.user?.token && _)
          )
            try {
              let [F] = (await t6([_], o, !1)) || [];
              F &&
                (r(F),
                y(!1),
                I.debug(
                  `Pro\u7528\u6237\u672F\u8BED\u5E93 ${e} \u540C\u6B65\u5B8C\u6210`
                ));
            } catch (F) {
              I.error(
                `\u540C\u6B65Pro\u7528\u6237\u672F\u8BED\u5E93 ${e} \u5931\u8D25:`,
                F
              );
            }
          if (_) {
            let [F] = (await n6([_], o.config)) || [];
            F && (r(F), y(!1));
          }
          if (!_ && ((_ = await J9(o?.config.termsBaseUrl || "", e)), _)) {
            let { glossaries: F } = await nd(
              o?.config.termsBaseUrl || "",
              ["auto", o.targetLanguage || ""],
              _
            );
            r({ ..._, glossaries: F }), y(!1);
          }
          if (_?.localLangsHash && lu(_)) {
            let { glossaries: F } = await nd(
              o?.config.termsBaseUrl || "",
              Object.keys(_?.localLangsHash || {}),
              _
            );
            h(F);
          }
          y(!1);
        })();
    }, [e, o, n, i?.targetLanguage]);
    let S = (B) => {
        r(B);
        let _ = { ...B, lastUserOpTime: Date.now() + "" };
        if ((Kr(_), o?.isPro && o.user?.token))
          try {
            (async () => {
              try {
                await c1([_]),
                  _.glossaries?.length && (await Z9(_.id, _.glossaries)),
                  I.debug(
                    `\u6210\u529F\u5C06\u672F\u8BED\u5E93 ${_.id} \u7684\u66F4\u6539\u540C\u6B65\u5230\u670D\u52A1\u5668`
                  );
              } catch (N) {
                I.error(
                  `\u540C\u6B65\u672F\u8BED\u5E93 ${_.id} \u5230\u670D\u52A1\u5668\u5931\u8D25:`,
                  N
                );
              }
            })();
          } catch (N) {
            I.error(
              "\u540C\u6B65\u672F\u8BED\u5E93\u5230\u670D\u52A1\u5668\u5931\u8D25:",
              N
            );
          }
      },
      T = (B) => {
        n && S({ ...n, glossaries: B });
      },
      w = (B) => {
        if (!n) return;
        let _ = { ...n };
        if (lu(n) && f && f.some((P) => P.k === B.k && P.tl === B.tl)) {
          let P = _.deleteRemoteGlossaries || [];
          P.some((E) => E.k === B.k && E.tl === B.tl) ||
            (_ = { ..._, deleteRemoteGlossaries: [...P, B] });
        }
        let F = (_.glossaries || []).filter(
          (D) => !(D.k === B.k && D.tl === B.tl)
        );
        (_ = { ..._, glossaries: F }), S(_);
      },
      A = n?.author !== "self",
      { name: M, description: R } = m1(i?.interfaceLanguage || "", n),
      O = Object.keys(n || {}).length > 0;
    return d("div", {
      children: [
        d("div", {
          className: "flex flex-row justify-between mt-4 mb-2",
          children: [
            d("div", {
              className: "store-return",
              onClick: (B) => {
                B.preventDefault(), (window.location.href = "#terms");
              },
              children: [
                d("a", { class: "back", href: "#" }),
                M,
                "\xA0",
                t("setting"),
              ],
            }),
            p &&
              d("div", {
                className: "cursor-pointer text-red-500",
                children: d("a", {
                  "aria-busy": v,
                  onClick: async (B) => {
                    if (
                      (B.preventDefault(),
                      !(!e || v) &&
                        globalThis.confirm(
                          t("terms.confirmDelete", { name: M })
                        ))
                    ) {
                      C(!0);
                      try {
                        lu(n) &&
                          Te({
                            key: "terms_switch",
                            ctx: o,
                            params: { ai_assistant: e, ai_assistant_use: "0" },
                          }),
                          await jf(e),
                          await We(100),
                          o?.isPro && n && (await c1([n], !0)),
                          (window.location.href = "#terms");
                      } catch (_) {
                        nt(_.message), I.error("Failed to delete terms:", _);
                      } finally {
                        C(!1);
                      }
                    }
                  },
                  children: t("terms.delete"),
                }),
              }),
          ],
        }),
        d(_L, { hidden: !b }),
        d("div", {
          className: "tipMsg",
          hidden: b || O,
          children: t("terms.notFound"),
        }),
        d("div", {
          className: "mt-3",
          hidden: b || !O,
          children: [
            d("label", { children: t("terms.name") }),
            d("input", {
              className: "input",
              value: M,
              disabled: A,
              onChange: (B) => {
                n &&
                  S(
                    $9(n, i?.targetLanguage || "", "name", B.target.value || "")
                  );
              },
            }),
          ],
        }),
        d("div", {
          className: "mt-3",
          hidden: b || !O,
          children: [
            d("label", { children: t("terms.description") }),
            d("textarea", {
              className: "input",
              value: R,
              disabled: A,
              placeholder: t("terms.descriptionPlaceholder"),
              onChange: (B) => {
                n &&
                  S(
                    $9(
                      n,
                      i?.targetLanguage || "",
                      "description",
                      B.target.value
                    )
                  );
              },
            }),
          ],
        }),
        n &&
          !b &&
          d(tO, {
            glossaries: n?.glossaries || [],
            onGlossariesUpdate: T,
            onDeleteTerm: w,
            defaultLanguage: i?.targetLanguage,
            termsName: n.name,
          }),
      ],
    });
  }
  var Ko = 1200;
  function ese(e) {
    let t = Fo(),
      { t: n, lang: r } = H(),
      { config: a } = e,
      i = Bt(a, e.ctx.isPro),
      o = a.generalRule?.imageRule?.enable;
    nse();
    let s = ase(e.ctx, a, t);
    tse(t);
    let l = !0;
    rt() && (l = a.generalRule.showSponsorOnSafari);
    let u = {
      "#terms": z5,
      "#terms/:id": H5,
      "#selection_translation": G9,
      "#general": a1,
      "#services": j9,
      "#ai": H9,
      "#mouse_hover": _5,
      "#input": V9,
      "#shortcuts": N5,
      "#floating": B5,
      "#import_export": r6,
      "#about": a6,
      "#advanced": i6,
      "#developer": W9,
      "#subtitle": l6,
      "#manga": U5,
      "#contact": o6,
      "#download_config": O5,
    };
    o || delete u["#manga"];
    let c = on(e.ctx.config.rtlLanguages, a.interfaceLanguage);
    return d(dt, {
      children: [
        d(j5, { config: a, ctx: e.ctx }),
        d(ce, {
          children: d("main", {
            class: "container-fluid flex-1",
            dir: c ? "rtl" : "ltr",
            onTouchStart: zo.bind(null, !0),
            onMouseDown: zo.bind(null, !1),
            children: [
              d("aside", {
                class: `min-[${Ko}px]:mt-6`,
                children: d("nav", {
                  class: `menu-nav flex flex-col h-full justify-between min-[${Ko}px]:pr-2`,
                  children: [
                    d("div", {
                      class: `max-[${Ko}px]:px-2`,
                      children: d("ul", {
                        class: `flex flex-wrap flex-start min-[${Ko}px]:block min-[${Ko}px]:text-base`,
                        children: s.map((m, p) =>
                          d(
                            "li",
                            {
                              class: "li",
                              children: d("a", {
                                ...m.props,
                                children: m.name,
                              }),
                            },
                            `nav-${p}`
                          )
                        ),
                      }),
                    }),
                    d("div", {
                      class: `flex flex-wrap min-[${Ko}px]:flex-col max-[${Ko}px]:px-4`,
                      children: [
                        l &&
                          d("a", {
                            class: "py-3 no-focus secondary mr-2",
                            href: e.donateUrl,
                            onClick: (m) => {
                              m.preventDefault(), qr(e.donateUrl);
                            },
                            children: n("donateCafe"),
                          }),
                        d("a", {
                          class: "py-3 no-focus secondary  mr-2",
                          href: um,
                          hidden: i,
                          onClick: (m) => {
                            m.preventDefault(), qr(um);
                          },
                          children: n("document"),
                        }),
                        d("a", {
                          class: "py-3 no-focus secondary  mr-2",
                          hidden: i,
                          href: te + "docs/CHANGELOG/",
                          onClick: (m) => {
                            m.preventDefault(), qr(te + "docs/CHANGELOG/");
                          },
                          children: n("changelog"),
                        }),
                        d("a", {
                          class: "py-3 no-focus secondary  mr-2",
                          href: o3,
                          hidden: i,
                          onClick: (m) => {
                            m.preventDefault(), qr(o3);
                          },
                          children: n("feedbackAndJoin"),
                        }),
                        r !== "zh-CN" &&
                          d("a", {
                            class: "py-3 no-focus secondary  mr-2",
                            target: "_blank",
                            hidden: i,
                            href: `https://weblate.${et}/browse/${j}/extension/${r}/`,
                            children: n("helpToTranslate"),
                          }),
                        d("a", {
                          class: "py-3 no-focus secondary  mr-2",
                          href: "#developer",
                          hidden: i,
                          children: n("developer"),
                        }),
                      ],
                    }),
                  ],
                }),
              }),
              d("div", {
                role: "main",
                class: `relative max-[${Ko}px]:px-4 min-[${Ko}px]:pt-6`,
                children: d(tv, { value: t, defaultCase: a1, cases: u }),
              }),
            ],
          }),
        }),
      ],
    });
  }
  async function nO() {
    await JN();
    let e = document.getElementById("mount");
    e &&
      (e.classList.add("min-h-screen", "flex", "flex-col"),
      (async () => {
        h2(KL);
        let t = await Ct();
        if (location.href.includes(Lu) && t.joinJobs) {
          let r = xw.replace(
            "{jobs}",
            t.joinJobs.map((a) => `    \u2022 ${a}`).join(`
`)
          );
        }
        t.debug && I.setLevel("debug"),
          globalThis.location.hash || (globalThis.location.hash = "#general");
        let n = await vr({ url: "http://localhost", config: t });
        Te({
          key: "options_page_view",
          ctx: { ...n, sourceLanguage: globalThis.location.hash },
        }),
          Fs(document, n),
          da(
            d(Ui, {
              lang: t.interfaceLanguage,
              children: d(ese, { donateUrl: t.donateUrl, config: t, ctx: n }),
            }),
            e
          );
      })());
  }
  function tse(e) {
    let t = le(0),
      n = le(0),
      r = le(),
      a = oe(async () => {
        if (!r.current) {
          (r.current = e), (t.current = Date.now());
          return;
        }
        t.current && (n.current += Date.now() - t.current),
          await dA({
            pageTitle: r.current,
            pageLocation: r.current,
            time: n.current,
          }),
          (n.current = 0),
          (r.current = e),
          (t.current = Date.now());
      }, [t, n, r, e]);
    K(() => {
      let i = () => {
        document.hidden
          ? (t.current && (n.current += Date.now() - t.current),
            (t.current = 0))
          : (t.current = Date.now());
      };
      return (
        a(),
        globalThis.addEventListener("visibilitychange", i),
        () => {
          globalThis.removeEventListener("visibilitychange", i);
        }
      );
    }, [a]),
      K(() => {
        let i = async () => (await a(), !0);
        return (
          globalThis.addEventListener("beforeunload", i),
          () => {
            globalThis.removeEventListener("beforeunload", i);
          }
        );
      }, [a]);
  }
  function nse() {
    let e = En(),
      [t, n] = e;
    K(() => {
      if (!t || !n) return;
      (async () => {
        try {
          let a = new URL(globalThis.location.href),
            i = a.searchParams.get("token");
          if (
            !i ||
            (a.searchParams.delete("token"),
            globalThis.history.replaceState(null, "", a.toString()),
            (await ze.get(Xe, null))?.token === i)
          )
            return;
          let l = (await rse(i)).data;
          (l.token = i),
            await ze.set(Xe, l),
            await iu(l.token, t, n),
            globalThis.location.reload();
        } catch {}
      })();
    }, [t]);
  }
  function rse(e) {
    return De({
      responseType: "json",
      url: Oe + "v1/user",
      method: "get",
      headers: { token: e },
    });
  }
  function ase(e, t, n) {
    let r = xn(),
      a = vn(r, "1.16.1"),
      { t: i, lang: o } = H(),
      { beta: s } = t,
      l = Bt(t, e.isPro),
      u = t.generalRule?.imageRule?.enable,
      c = [
        {
          name: i("general"),
          props: { href: "#general", className: "secondary" },
        },
        {
          name: i("translationServiceNav"),
          props: { href: "#services", className: "secondary" },
        },
        ...(s || t.enableAiAssistant
          ? [
              {
                name: i("field.assistant"),
                props: { href: "#ai", className: "secondary" },
              },
            ]
          : []),
        ...(a
          ? [
              {
                name: i("field.terms"),
                props: { href: "#terms", className: "secondary" },
              },
            ]
          : []),
        {
          name: i("subtitle"),
          props: { href: "#subtitle", className: "secondary" },
        },
        ...(u && !l
          ? [
              {
                name: Ni(e, !0) ? i("mangaAndImage") : i("manga"),
                props: { href: "#manga", className: "secondary" },
              },
            ]
          : []),
        {
          name: i("inputOptions"),
          props: { href: "#input", className: "secondary" },
        },
        ...(xe().any || X()
          ? []
          : [
              {
                name: i("selectionTranslation"),
                props: {
                  href: "#selection_translation",
                  className: "secondary",
                },
              },
            ]),
        {
          name: i("mouseHoverOptions"),
          props: { href: "#mouse_hover", className: "secondary" },
        },
        {
          name: i("floatBallOptions"),
          props: { href: "#floating", className: "secondary" },
        },
        {
          name: i("shortcutSettings"),
          props: { href: "#shortcuts", className: "secondary" },
        },
        {
          name: i("advanced"),
          props: { href: "#advanced", className: "secondary" },
        },
        {
          name: i("import_export"),
          props: { href: "#import_export", className: "secondary" },
        },
        { name: i("about"), props: { href: "#about", className: "secondary" } },
      ];
    return (
      o.startsWith("zh") ||
        c.splice(c.length - 1, 0, {
          name: i("contact"),
          props: { href: "#contact", className: "secondary" },
        }),
      c.forEach((m) => {
        if (m.props.href === "#services") {
          if (!n.startsWith("#services")) return;
          (m.props.className = "primary"), (m.props["aria-current"] = "page");
          return;
        }
        if (m.props.href === "#ai") {
          if (!n.startsWith("#ai")) return;
          (m.props.className = "primary"), (m.props["aria-current"] = "page");
          return;
        }
        n === m.props.href &&
          ((m.props.className = "primary"), (m.props["aria-current"] = "page"));
      }),
      c
    );
  }
  nO();
})();
/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
/*!
 * Toastify js 1.12.0
 * https://github.com/apvarun/toastify-js
 * @license MIT licensed
 *
 * Copyright (C) 2018 Varun A P
 */
/*! Bundled license information:

lottie-web/build/player/lottie.js:
  (*!
   Transformation Matrix v2.0
   (c) Epistemex 2014-2015
   www.epistemex.com
   By Ken Fyrstenberg
   Contributions by leeoniya.
   License: MIT, header required.
   *)
*/
