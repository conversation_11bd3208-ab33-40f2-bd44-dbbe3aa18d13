(() => {
  var l9 = {
    get: (e, t, n) =>
      te.storage[n].get(e).then((r) => (r[e] === void 0 && (r[e] = t), r)),
    set: (e, t, n) => te.storage[n].set({ [e]: t }),
  };
  function Ph(e, t, n) {
    let [r] = V(() => (typeof t == "function" ? t() : t)),
      [a] = V(n),
      [i, o] = V(r),
      [s, l] = V(!1),
      [u, c] = V("");
    Q(() => {
      l9.get(e, r, a)
        .then((m) => {
          m[e] && o(m[e]), l(!0), c("");
        })
        .catch((m) => {
          l(!1), c(m);
        });
    }, [e, r, a]);
    let d = Me(
      (m) => {
        let p = typeof m == "function" ? m(i) : m;
        I.debug("new settings", p),
          l9
            .set(e, p, a)
            .then(() => {
              o(p), l(!0), c("");
            })
            .catch((g) => {
              o(p), l(!1), c(g);
            });
      },
      [a, e, i]
    );
    return [i, d, s, u];
  }
  function u9(e, t, n) {
    let r = [];
    return function () {
      let [i, o, s, l] = Ph(e, t, n),
        u = Me((c) => {
          for (let d of r) d(c);
        }, []);
      return (
        Q(
          () => (
            r.push(o),
            () => {
              r.splice(r.indexOf(o), 1);
            }
          ),
          [o]
        ),
        [i, u, s, l]
      );
    };
  }
  function $I(e, t) {
    return u9(e, t, "local");
  }
  var kJ = null,
    c9 = $I(sc, kJ);
  function Gi(e) {
    let [t, n, r, a] = c9(),
      [i, o] = V();
    Q(() => {
      (async function () {
        let l = await Gl();
        o(l);
      })();
    }, [t]);
    let s = Me(
      async (l) => {
        let u = typeof l == "function" ? l(i) : l;
        u && qo(u), n(u), e && e(u);
      },
      [i]
    );
    return [i, s, r, a, n];
  }
  var Br = [],
    Ks = "",
    gd = [];
  async function m9() {
    if (gd.length) return { list: gd, syncTimestamp: Ks };
    try {
      let e = await qr.get("v1/user/glossary-meta");
      return Array.isArray(e.list) && e.list.length
        ? ((gd = e.list), (Ks = e.syncTimestamp), e)
        : { list: null, syncTimestamp: Ks };
    } catch (e) {
      return (
        I.error("fetchProTermsMeta error", e), { list: null, syncTimestamp: Ks }
      );
    }
  }
  async function MJ(e, t = !1) {
    Br = (await m9())?.list || e;
    let n = Br.find((a) => e.some((i) => i.id === a.id));
    n
      ? Object.assign(
          n,
          e.find((a) => a.id === n.id)
        )
      : Br.push(...e),
      t && (Br = Br.filter((a) => !e.some((i) => i.id === a.id))),
      (Ks = Date.now() + ""),
      (gd = Br),
      await E3(Ks);
    let r = [];
    if (
      (r.push(
        qr.post("v1/user/glossary-meta", { list: DJ(gd), syncTimestamp: Ks })
      ),
      t)
    )
      for (let a of e) r.push(PJ(a.id));
    return (Br = []), Promise.all(r);
  }
  function DJ(e) {
    return e.map((t) => {
      let { glossaries: n, ...r } = t;
      return { ...r };
    });
  }
  async function IJ(e) {
    let t = await qr.get(`v1/user/glossary-config/${e}`);
    return tP(t).glossaries;
  }
  async function PJ(e) {
    return (await qr.delete("v1/user/glossary-config", { metaKey: e })).data;
  }
  async function LJ(e, t) {
    return (
      await qr.post("v1/user/glossary-config", {
        metaKey: e,
        userGlossaryConfig: BJ(t),
      })
    ).data;
  }
  async function eP(e = `${E6}static/terms/`) {
    if (Br.length > 0) return Br;
    let t = await fetch(e + "meta/index.json");
    return t.ok && (Br = await t.json()), Br;
  }
  async function RJ(e, t) {
    await eP(e);
    let n = Br.find((a) => a.id == t);
    if (n) return n;
    let r = await fetch(e + `meta/${t}.json`);
    return r.ok ? r.json() : null;
  }
  async function FJ(e, t, n) {
    let r = {},
      a = [];
    if (t.length === 0 || !n.id) return { glossaries: [], langsHash: r };
    t.forEach((i) => {
      n.langsHash?.[i] && ((r[i] = n.langsHash?.[i] || ""), a.push(i));
    });
    try {
      let i = await nP(e, n.id, a, r);
      return { glossaries: NJ(i.flat()), langsHash: r };
    } catch {
      return { glossaries: [], langsHash: r };
    }
  }
  function d9(e) {
    if (e == null) return "";
    let t = String(e);
    return t.includes(",") ||
      t.includes('"') ||
      t.includes(`
`)
      ? `"${t.replace(/"/g, '""')}"`
      : t;
  }
  function BJ(e) {
    let t = "source,target,tgt_lng",
      n = e.map((r) => [d9(r.k), d9(r.v), d9(r.tl)].join(","));
    return `${t}
${n.join(`
`)}`;
  }
  function tP(e) {
    try {
      let t = e
        .split(
          `
`
        )
        .filter((d) => d.trim());
      if (t.length === 0) return { glossaries: [], hasInvalidItems: !1 };
      let n = t[0].toLowerCase(),
        a = n.includes("source") && n.includes("target") ? 1 : 0,
        i = t.slice(a),
        o = i.filter((d) => (d.split(",")[0] || "").trim() && d.includes(",")),
        s = i.length !== o.length,
        l = o.map((d) => {
          let m = [],
            p,
            g = /"((?:[^"]|"")*)"|([^,]*)/g;
          for (; (p = g.exec(d)) !== null; ) {
            let y = p[1] !== void 0 ? p[1].replace(/""/g, '"') : p[2];
            if ((m.push(y.trim()), d[g.lastIndex] !== ",")) break;
            g.lastIndex++;
          }
          for (; m.length < 3; ) m.push("");
          let [h, x, f] = m.slice(0, 3);
          return { k: h, v: x, tl: f };
        }),
        u = l.filter((d) => d.k),
        c = s || u.length !== l.length;
      return { glossaries: u, hasInvalidItems: c };
    } catch {
      throw new Error("Failed to parse CSV content");
    }
  }
  function _J(e) {
    return !!e && (e.author === "immersive" || e.id == "default");
  }
  function NJ(e = [], t = []) {
    let n = new Map(),
      r = new Set(),
      a = [];
    for (let i of t) i?.k && n.set(i.k, i);
    for (let i of e)
      if (i?.k) {
        let o = i.k;
        r.add(o);
        let s = n.has(o) ? n.get(o) : i;
        a.push(s);
      }
    for (let [i, o] of n.entries()) r.has(i) || a.push(o);
    return a;
  }
  function OJ(e) {
    return (
      e?.sort((t, n) => {
        if (t.active && !n.active) return -1;
        if (!t.active && n.active) return 1;
        let r = t.order || 9,
          a = n.order || 9;
        return r === a
          ? t.author === "self" && n.author !== "self"
            ? -1
            : (t.author !== "self" && n.author === "self") ||
              (t.lastUserOpTime || 0) < (n.lastUserOpTime || 0)
            ? 1
            : -1
          : r < a
          ? -1
          : 1;
      }) || []
    );
  }
  async function nP(e, t, n, r) {
    let a = n.map((o) => {
      let s = r[o] || "",
        l = `${e}glossaries/${t}_${o}.csv?hash=${s}`;
      return (
        o == "auto" && (l = `${e}glossaries/${t}.csv?hash=${s}`),
        fetch(l)
          .then((u) => {
            if (!u.ok) {
              if (u.status === 404) return null;
              throw new Error(`[${t}] Failed to fetch ${l}: ${u.statusText}`);
            }
            return u.text();
          })
          .then((u) => {
            if (u === null) return [];
            let { glossaries: c, hasInvalidItems: d } = tP(u);
            return c.map((m) => ({ ...m, tl: m.tl || o }));
          })
          .catch((u) => [])
      );
    });
    return await Promise.all(a);
  }
  function jJ(e, t, n) {
    let r = new Set();
    if (n)
      for (let o of n) {
        let s = `${o.k}|${o.tl || ""}`;
        r.add(s);
      }
    let a = t.filter((o) => {
        let s = `${o.k}|${o.tl || ""}`;
        return !r.has(s);
      }),
      i = new Map();
    for (let o of e) {
      let s = `${o.k}|${o.tl || ""}`;
      r.has(s) || i.set(s, o);
    }
    for (let o of a) {
      let s = `${o.k}|${o.tl || ""}`;
      i.has(s) || i.set(s, o);
    }
    return Array.from(i.values());
  }
  function pd(e) {
    if (typeof e == "string") {
      let t = parseInt(e, 10);
      return isNaN(t) ? 0 : t;
    } else if (typeof e == "number") return e;
    return 0;
  }
  async function zJ(e, t, n = !1) {
    if (!t.isPro)
      return (
        I.debug(
          "\u7528\u6237\u4E0D\u662FPro\u4F1A\u5458\uFF0C\u8DF3\u8FC7\u540C\u6B65"
        ),
        null
      );
    I.debug(
      `\u5F00\u59CB\u540C\u6B65Pro\u7528\u6237\u672F\u8BED\u5E93 (fullSync: ${n})`
    );
    let r = null;
    try {
      let a = pd(await Ew()),
        { list: i = [], syncTimestamp: o } = await m9(),
        s = pd(o),
        l = await Ao();
      I.debug(
        `\u672C\u5730\u540C\u6B65\u65F6\u95F4: ${new Date(
          a
        ).toLocaleString()}, \u8FDC\u7A0B\u6700\u65B0\u65F6\u95F4: ${new Date(
          s
        ).toLocaleString()}`
      );
      let u = s > a;
      u
        ? I.info(
            "\u68C0\u6D4B\u5230\u8FDC\u7A0B\u53EF\u80FD\u6709\u66F4\u65B0 (\u5168\u5C40\u65F6\u95F4\u6233\u8F83\u65B0)"
          )
        : a > s
        ? I.warn(
            "\u672C\u5730\u540C\u6B65\u65F6\u95F4\u6233\u5927\u4E8E\u8FDC\u7A0B\u5168\u5C40\u65F6\u95F4\u6233\uFF0C\u53EF\u80FD\u5B58\u5728\u65F6\u949F\u95EE\u9898\u6216\u672A\u540C\u6B65\u7684\u672C\u5730\u66F4\u6539"
          )
        : I.info(
            "\u5168\u5C40\u65F6\u95F4\u6233\u4E00\u81F4\uFF0C\u68C0\u67E5\u5355\u4E2A\u672F\u8BED\u5E93\u66F4\u65B0"
          );
      let c = new Map();
      l.forEach((x) => x.id && c.set(x.id, x));
      let d = new Map();
      i?.forEach((x) => x.id && d.set(x.id, x));
      let m = new Set([...c.keys(), ...d.keys()]),
        p = [],
        g = [],
        h = new Set();
      n && u && c.forEach((x) => h.add(x.id));
      for (let x of m) {
        let f = c.get(x),
          y = d.get(x);
        if (f && y) {
          n && u && h.delete(x);
          let b = pd(f.lastUserOpTime),
            v = pd(y.lastUserOpTime);
          b > v
            ? (I.debug(
                `\u672F\u8BED\u5E93 ${x}: \u672C\u5730\u8F83\u65B0\uFF0C\u51C6\u5907\u4E0A\u4F20`
              ),
              p.push(f))
            : v > b
            ? (I.debug(
                `\u672F\u8BED\u5E93 ${x}: \u8FDC\u7A0B\u8F83\u65B0\uFF0C\u51C6\u5907\u4E0B\u8F7D`
              ),
              g.push(y))
            : I.debug(
                `\u672F\u8BED\u5E93 ${x}: \u65F6\u95F4\u6233\u76F8\u540C\uFF0C\u65E0\u9700\u64CD\u4F5C`
              );
        } else if (f && !y)
          n && u
            ? I.debug(
                `\u672F\u8BED\u5E93 ${x}: \u4EC5\u672C\u5730\u5B58\u5728\u4E14\u8FDC\u7A0B\u6709\u66F4\u65B0\uFF0C\u6807\u8BB0\u4E3A\u5F85\u5220\u9664`
              )
            : u
            ? I.debug(
                `\u672F\u8BED\u5E93 ${x}: \u4EC5\u672C\u5730\u5B58\u5728\uFF0C\u975E\u5168\u91CF\u540C\u6B65\u6216\u8FDC\u7A0B\u65E0\u66F4\u65B0\uFF0C\u4FDD\u7559`
              )
            : (I.debug(
                `\u672F\u8BED\u5E93 ${x}: \u4EC5\u672C\u5730\u5B58\u5728\u4E14\u8FDC\u7A0B\u65E0\u66F4\u65B0\uFF0C\u51C6\u5907\u4E0A\u4F20`
              ),
              p.push(f));
        else if (!f && y) {
          if (!n) continue;
          I.debug(
            `\u672F\u8BED\u5E93 ${x}: \u4EC5\u8FDC\u7A0B\u5B58\u5728\uFF0C\u51C6\u5907\u4E0B\u8F7D`
          ),
            g.push(y);
        }
      }
      if (g.length > 0) {
        I.info(
          `\u5F00\u59CB\u4E0B\u8F7D ${g.length} \u4E2A\u672F\u8BED\u5E93...`
        );
        let x = g.map(async (f) => {
          try {
            let y = (await IJ(f.id)) || [],
              b = po(y),
              v = { ...f, glossaries: b };
            y.length && (await eu(v)),
              I.debug(
                `\u6210\u529F\u4E0B\u8F7D\u5E76\u4FDD\u5B58 ${f.id} \u6570\u91CF:`,
                b.length
              );
          } catch (y) {
            I.error(
              `\u4E0B\u8F7D\u6216\u4FDD\u5B58\u672F\u8BED\u5E93 ${f.id} \u5931\u8D25:`,
              y
            );
          }
        });
        await Promise.all(x);
      }
      if (p.length > 0) {
        I.info(
          `\u5F00\u59CB\u4E0A\u4F20 ${p.length} \u4E2A\u672F\u8BED\u5E93...`
        );
        let x = p.map(async (y) => {
          try {
            let b = await $l(y.id),
              v = b?.glossaries ? po(b.glossaries) : [];
            await MJ([y]),
              await LJ(y.id, v),
              I.debug(`\u6210\u529F\u4E0A\u4F20 ${y.id}`);
          } catch (b) {
            I.error(`\u4E0A\u4F20\u672F\u8BED\u5E93 ${y.id} \u5931\u8D25:`, b);
          }
        });
        await Promise.all(x);
        let { syncTimestamp: f } = await m9();
        (r = pd(f)),
          I.info(
            `\u4E0A\u4F20\u64CD\u4F5C\u5B8C\u6210\uFF0C\u83B7\u53D6\u5230\u6700\u65B0\u8FDC\u7A0B\u65F6\u95F4\u6233: ${r}`
          );
      } else s >= a ? (r = s) : (r = a);
      if (n && u && h.size > 0) {
        I.info(
          `\u5F00\u59CB\u5220\u9664 ${h.size} \u4E2A\u672C\u5730\u672F\u8BED\u5E93...`
        );
        let x = Array.from(h).map(async (f) => {
          try {
            await ww(f),
              I.debug(
                `\u6210\u529F\u5220\u9664\u672C\u5730\u672F\u8BED\u5E93 ${f}`
              );
          } catch (y) {
            I.error(
              `\u5220\u9664\u672C\u5730\u672F\u8BED\u5E93 ${f} \u5931\u8D25:`,
              y
            );
          }
        });
        await Promise.all(x);
      }
      return (
        r !== null && r !== a
          ? (await E3(r + ""),
            I.info(
              `\u672C\u5730\u540C\u6B65\u65F6\u95F4\u6233\u66F4\u65B0\u4E3A: ${new Date(
                r
              ).toLocaleString()}`
            ))
          : I.info(
              `\u672C\u5730\u540C\u6B65\u65F6\u95F4\u6233\u65E0\u9700\u66F4\u65B0 (${new Date(
                a
              ).toLocaleString()})`
            ),
        I.debug(
          "\u540C\u6B65\u6D41\u7A0B\u5B8C\u6210\uFF0C\u8FD4\u56DE\u6700\u65B0\u7684\u672C\u5730\u672F\u8BED\u5E93\u5217\u8868"
        ),
        n
          ? await Ao()
          : (await Promise.all(e.map(async (f) => await $l(f.id)))).filter(
              (f) => f !== null
            )
      );
    } catch (a) {
      return (
        I.error(
          "\u540C\u6B65Pro\u7528\u6237\u672F\u8BED\u5E93\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9876\u5C42\u9519\u8BEF:",
          a
        ),
        null
      );
    } finally {
      I.debug("\u7ED3\u675F\u540C\u6B65Pro\u7528\u6237\u672F\u8BED\u5E93");
    }
  }
  async function rP(e, t) {
    let n = [];
    for (let r = 0; r < e.length; r++) {
      let a = e[r];
      if (a.id.startsWith("custom")) continue;
      let i = await RJ(t.termsBaseUrl, a.id);
      if (!i || !_J(a)) continue;
      let { i18ns: o } = i;
      if (
        (i.id != "default" && (a.i18ns = o),
        await eu({ ...a }),
        !!a.localLangsHash)
      ) {
        for (let s of Object.keys(a.localLangsHash))
          if (i.langsHash?.[s] && a.localLangsHash[s] != i.langsHash[s]) {
            let { glossaries: l } = await FJ(
                t.termsBaseUrl,
                [t?.targetLanguage],
                i
              ),
              u = jJ(
                l || [],
                a?.glossaries || [],
                a?.deleteRemoteGlossaries || []
              );
            a.localLangsHash = { ...a.localLangsHash, [s]: i.langsHash?.[s] };
            let c = { ...a, glossaries: u };
            n.push(c),
              await eu(c),
              I.debug(
                `\u672F\u8BED\u5E93\u5E02\u573A\u4E2D\u5F53\u524D\u672F\u8BED\u5E93 ${a.id} ${s} \u6709\u66F4\u65B0\uFF0C\u5DF2\u540C\u6B65`
              );
            break;
          }
      }
    }
    return (Br = []), n;
  }
  async function aP(e, t) {
    let n = await Ao();
    n.find((r) => r.id == "default") ||
      eu({
        id: "default",
        name: t("terms.default") || "",
        description: t("terms.defaultDescription"),
        matches: ["*"],
        author: "self",
        active: !0,
        order: 0,
        glossaries: e.rule.glossaries,
        lastUserOpTime: Date.now() + "",
      }),
      await zJ(n, e, !0),
      await rP(n, e.config);
  }
  async function iP(e) {
    let t = await Ao(),
      n = ye.bind(null, e.config.interfaceLanguage);
    t.find((r) => r.id == "default") ||
      (eu({
        id: "default",
        name: n("terms.default"),
        description: n("terms.defaultDescription"),
        matches: ["*"],
        author: "self",
        active: !0,
        order: 0,
        glossaries: e.rule.glossaries,
        lastUserOpTime: Date.now() + "",
      }),
      await rP(t, e.config));
  }
  async function oP(e, t) {
    let n = OJ((await Ao()) || []);
    n.length && t?.(1, n);
    let r = await eP(e.termsBaseUrl);
    r = r.filter(
      (i) => i.langs?.includes(e.targetLanguage) || i.langs?.includes("auto")
    );
    let a = [...n, ...r.filter((i) => !n?.find((o) => o.id === i.id))];
    return t?.(2, a), a;
  }
  async function sP(e, t, n) {
    let r = {};
    for (let a of n) {
      let i = await $l(a);
      if (i) r[a] = i.glossaries || [];
      else {
        let o = await nP(e.termsBaseUrl, a, ["auto", t], {
          auto: Date.now() + "",
          [t]: Date.now() + "",
        });
        r[a] = o.flat();
      }
    }
    return r;
  }
  var OOe = 1e3 * 3600 * 24;
  async function Lh(e, t) {
    try {
      let n = new Date();
      I.debug(
        "cron task start, next will run at",
        new Date(n.getTime() + e).toLocaleString()
      ),
        await te.storage.local.set({ [ic]: n.toISOString() }),
        await hd();
      let r = ye.bind(null, t.config.interfaceLanguage);
      await aP(t, r), J() || (await vw());
    } catch (n) {
      I.error("run cron task failed", n);
    }
  }
  async function lP(e) {
    let n = (await Wn()).interval;
    if (n) {
      let r = await te.storage.local.get(ic);
      if (r && r[ic]) {
        let a = r[ic];
        if (Date.now() - new Date(a).getTime() < n) {
          let i = new Date(new Date(a).getTime() + n).toLocaleString();
          I.debug(`cron task not run, next will run at ${i}`);
          return;
        } else Lh(n, e);
      } else Lh(n, e);
    }
  }
  async function hd() {
    try {
      let e = await Wn(),
        t = await Se({ url: ac });
      Bi(e);
      let n = e.buildinConfigUpdatedAt,
        r = new Date(n),
        a = t.buildinConfigUpdatedAt,
        i = new Date(a),
        o = t.minVersion,
        s = te.runtime.getManifest().version;
      ku(s, o)
        ? i > r
          ? (await te.storage.local.set({ buildinConfig: t }),
            I.info(
              `sync remote rules success, latest: ${new Date(
                a
              ).toLocaleString()}`
            ),
            qa(
              { method: "updateGlobalCtx", data: {} },
              { tab: { id: 1, url: "https://www.fake.com", active: !0 } }
            ).catch((l) => {
              I.error("send content message request failed from cron task", l);
            }))
          : I.debug(`no need to sync rules, latest: ${r}`)
        : I.info(`local version is too old, please update to ${o} or later`);
    } catch (e) {
      I.error("sync rules error: ", e);
    }
  }
  var $Oe = Bl([]);
  function uP() {
    let [e, t] = V({}),
      n = ge(null),
      r = ge(null),
      a = ge(null),
      [i, o, s, l] = c9(),
      u = async () => {
        let d = await Gl();
        (n.current = d),
          (r.current = await tn()),
          r.current &&
            ((a.current = await Fn({
              url: "http://localhost",
              config: r.current,
              state: { cache: !1 },
            })),
            t({}));
      };
    Q(() => {
      i != null && u();
    }, [i]);
    let c = Me(async (d) => {
      let m = typeof d == "function" ? d(n.current) : d;
      m && qo(m), o(m);
    }, []);
    return {
      settingsHookValue: [n.current, c, s, l, o],
      config: r.current,
      ctx: a.current,
      ctxRef: a,
      configRef: r,
      refreshCtx: u,
    };
  }
  function cP(e) {
    let [t, n] = V(null);
    return (
      Q(() => {
        tn().then((r) => {
          n(r);
        });
      }, [e]),
      t
    );
  }
  function dP() {
    let [e, t] = V(null);
    Q(() => {
      nn().then((r) => {
        t(r || null);
      });
    }, []);
    let n = Me((r) => {
      t(r), Qt(r);
    }, []);
    return [e, n];
  }
  function mP() {
    let [e, t] = V(null);
    return (
      Q(() => {
        et.get(gt, null).then((n) => {
          t(n);
        });
      }, [t]),
      e
    );
  }
  var pP = qt(UJ, 1e3);
  async function UJ(e, t, n) {
    try {
      if (t === null) return "noupdate";
      let r = await nn();
      if (t.updatedAt) {
        let l = new Date().getTime(),
          u = new Date(t.updatedAt).getTime();
        if (l - u < 2e3) {
          let c = bo(t.proSyncAPIKey, t);
          return await p9(e, c), await Qt(r), "upload";
        }
      }
      let { remoteSetting: a, remoteTimestamp: i } = await fP(e);
      (r.accountLastSyncedAt = Date.now()),
        I.debug("settings", t),
        I.debug("remoteSettings", a),
        I.debug("local settings.updatedAt", Hu(t.updatedAt)),
        I.debug("remote settings.updatedAt", Hu(a.updatedAt)),
        I.debug("last synced at", Hu(r.accountLastSyncedAt)),
        YS(t, i);
      let o = !1;
      if (
        (t.updatedAt && (!a || !a.updatedAt) && (o = !0),
        !o && t.updatedAt > a.updatedAt && (o = !0),
        I.debug("isUpload", o),
        o)
      ) {
        let l = bo(t.proSyncAPIKey, t);
        return await p9(e, l), await Qt(r), "upload";
      }
      let s = !1;
      return (
        a.updatedAt && (!t || !t.updatedAt) && (s = !0),
        !s && t.updatedAt < a.updatedAt && (s = !0),
        s
          ? ((a.override = !0),
            s3(a.proSyncAPIKey, t, a),
            await n(hP(a, t)),
            await Qt(r),
            Bi(void 0, r),
            "override")
          : (await Qt(r), "noupdate")
      );
    } catch (r) {
      throw (i3(r), r);
    }
  }
  var Hu = (e) =>
    e ? new Date(e).toLocaleString("zh-CN", { timeZone: "Asia/Shanghai" }) : "";
  async function gP(e, t, n) {
    try {
      if (t === null) return "noupdate";
      let { remoteSetting: r } = await fP(e),
        a = await nn();
      (a.accountLastSyncedAt = Date.now()),
        I.debug("settings", t),
        I.debug("remoteSettings", r),
        I.debug("local settings.updatedAt", Hu(t.updatedAt)),
        I.debug("remote settings.updatedAt", Hu(r.updatedAt)),
        I.debug("last synced at", Hu(a.accountLastSyncedAt));
      let i = !1;
      if (
        (t.updatedAt &&
          (!r?.updatedAt || Object.keys(r).length <= 1) &&
          (i = !0),
        I.debug("isUpload", i),
        i)
      ) {
        let s = bo(t.proSyncAPIKey, t);
        return await p9(e, s), await Qt(a), "upload";
      }
      let o = !0;
      return (
        (!r?.updatedAt || Object.keys(r).length <= 1) && (o = !1),
        o
          ? (s3(r.proSyncAPIKey, t, r),
            (r.override = !0),
            await n(hP(r, t)),
            await Qt(a),
            Bi(void 0, a),
            "override")
          : (await Qt(a), "noupdate")
      );
    } catch (r) {
      throw (i3(r), r);
    }
  }
  function hP(e, t) {
    let n = { ...e };
    return (
      x6.forEach((a) => {
        r(a);
      }),
      n
    );
    function r(a) {
      !HJ(e.translationServices || {}, e[a]) && (n[a] = t[a]);
    }
  }
  function HJ(e, t) {
    if (!t) return !0;
    let n = Object.keys(Ba.translationServices).find((r) => t == r);
    return !n && t ? e[t]?.type == "custom-ai" : n;
  }
  function fP(e) {
    return (location.href?.indexOf("popup.html") > 0 ? ro : Se)({
      responseType: "json",
      url: Oe + "v1/user/settings",
      method: "get",
      headers: { token: e },
    }).then((n) => ({
      remoteSetting: n.data,
      remoteTimestamp: n.timestamp * 1e3,
    }));
  }
  function p9(e, t) {
    return (
      delete t.localUpdatedAt,
      delete t.override,
      (location.href?.indexOf("popup.html") > 0 ? ro : Se)({
        responseType: "json",
        url: Oe + "v1/user/settings",
        method: "post",
        headers: { token: e, "content-type": "application/json" },
        body: JSON.stringify(t),
      }).then((r) => r.data)
    );
  }
  function Rh(e, t) {
    let n = mP(),
      [r, a] = dP();
    Q(() => {
      !n ||
        !n.token ||
        !r ||
        r.proAutoSync === !1 ||
        (ZS(n) && pP(n.token, e, t));
    }, [e, n, r]);
  }
  function Fh(e) {
    let { onClose: t } = e,
      [n, r] = V("Original"),
      [a, i, o, s] = Gi(() => {
        setTimeout(() => {
          E("updateGlobalContext", !1)();
        }, 250);
      });
    Rh(a, i);
    let [l, u] = V(null),
      [c, d] = V(globalThis.location.href),
      [m, p] = V("auto"),
      [g, h] = V(null),
      [x, f] = V({ visible: !1, preview: !1 }),
      y = ge(!1);
    Q(() => {
      !g ||
        y.current ||
        ((y.current = !0), Te({ key: "show_page_popup", ctx: g }));
    }, [g, y]);
    let b = (R) => {
      r(R.detail);
    };
    Q(
      () => (
        document.addEventListener(Zo, b, !1),
        Wn().then((R) => {
          u(R);
          let M = rn();
          p(M);
          let P = Ye();
          r(P), g && Lh(R.interval, g);
        }),
        document.addEventListener("urlChange", T),
        () => {
          document.removeEventListener("pageTranslatedStatus", b),
            document.removeEventListener("urlChange", T);
        }
      ),
      []
    ),
      Q(() => {
        Wn().then((R) => {
          u(R);
        });
      }, [a]),
      Q(() => {
        c &&
          l &&
          Vi().then((R) => {
            h(R);
          });
      }, [c, l]),
      Q(() => {
        let R = Op();
        f({ visible: !!R?.used, preview: R?.sourceProgram == "html" });
      }, []);
    let v = Me((R, M) => {
      E("change_translate_service", !1, { translation_service: `${R}_${M}` })();
    }, []);
    if (!l || !g) return null;
    return C(s9, {
      type: "float_ball_popup",
      className: e.className,
      onSwitchTranslationMode: (R) => {
        E("switchTranslationMode", !0, { mode: R })();
      },
      aiContextInfo: x,
      request: Se,
      onClose: A,
      onToggleEnabled: D,
      onChangeService: v,
      onTranslateTheWholePage: E("translateTheWholePage", !0),
      openOptionsPage: _,
      openRewardCenter: B,
      onToggleTranslate: E("toggleTranslatePage", !0, {
        currentPageStatus: Ye(),
      }),
      onTranslateTheMainPage: E("translateTheMainPage", !0),
      onTranslateToThePageEndImmediately: E(
        "translateToThePageEndImmediately",
        !0
      ),
      onTranslatePage: E("translatePage", !0),
      onRestorePage: E("restorePage", !1),
      onWebReport: E("webReport", !0),
      onTranslatePdf: () => {
        O(Ds(g.rule) || c);
      },
      openAboutPage: j,
      openSharePage: E("shareToDraft", !0),
      onSetPageLanguage: S,
      setSettings: i,
      config: l,
      pageStatus: n,
      ctx: g,
      currentUrl: c,
      currentLang: m,
      onSetLocalConfig: Pi,
      onSetBuildinConfig: dw,
      onAutoEnableSubtitleChanged: E("autoEnableSubtitleChanged", !0),
      onToggleEnableEditTranslation: E("toggleEnableEditTranslation", !0),
      updateContextState: (R) => E("updateContextState", !0, R)(),
      onOpenUrl: F,
      onUpdateFloatBallEnable: E("updateFloatBallEnable", !0),
      onReport: (R) => {
        E("popupEventReport", !1, R)();
      },
    });
    function S(R) {
      p(R);
      let M = Bk(c, R, l.sourceLanguageUrlPattern);
      i((P) => ({ ...P, sourceLanguageUrlPattern: M })), Mr(R);
    }
    function T() {
      d(globalThis.location.href);
    }
    function E(R, M, P = {}) {
      let w = { trigger: "page_popup", ...P };
      return () => {
        dn({ method: R, data: w }), M && t();
      };
    }
    function A() {
      t();
    }
    function D() {
      i((R) => ({ ...R, enabled: !R.enabled })),
        setTimeout(() => {
          A();
        }, 50);
    }
    function F(R, M) {
      let P = R;
      M &&
        (P = `${R}?utm_source=extension&utm_medium=extension&utm_campaign=${M}`),
        xw(P, !0),
        setTimeout(() => {
          A();
        }, 50);
    }
    function O(R) {
      R &&
        (Zl(!1, R),
        setTimeout(() => {
          A();
        }, 50));
    }
    function _(R = !0, M = "") {
      gr(R, M, l?.useOnlineOptions || !1),
        setTimeout(() => {
          t();
        }, 50);
    }
    function B() {
      g && (Jl(g, "float_ball_popup"), t());
    }
    function j() {
      Gp(),
        setTimeout(() => {
          t();
        }, 50);
    }
  }
  async function Qs(e, t) {
    let n = t || (await un()),
      r = await te.storage.sync.get(e);
    return { userValue: n[e], localValue: r[e] };
  }
  async function qu(e, t) {
    let n = await un();
    pr({ ...n, [e]: t }), te.storage.sync.set({ [e]: t });
  }
  var Gu = null,
    Vu = null;
  function CP() {
    let [e, t] = V("Original");
    return (
      Q(() => {
        let n = Ye();
        t(n);
        let r = (a) => {
          t(a.detail);
        };
        return (
          document.addEventListener(Zo, r),
          () => {
            document.removeEventListener(Zo, r);
          }
        );
      }, []),
      { pageStatus: e }
    );
  }
  function SP() {
    let [e, t] = V("Original");
    return (
      Q(() => {
        let n = Ql();
        t(n);
        let r = (a) => {
          t(a.detail);
        };
        return (
          document.addEventListener(Wi, r),
          () => {
            document.removeEventListener(Wi, r);
          }
        );
      }, []),
      { mangaStatus: e }
    );
  }
  var bP = 6,
    qJ = { position: "right", top: 335 };
  function TP({
    handleBallClick: e,
    isShow: t,
    localConfig: n,
    handleMobileBallLongPress: r,
    popupVisible: a,
    rule: i,
    updateFloatBallRule: o,
  }) {
    let s = ge(null),
      l = ge(null),
      [u, c] = V(!1),
      d = ge(!1);
    d.current = u;
    let m = ge(null),
      [p, g] = V(!1),
      h = ge(!1);
    h.current = p;
    let x = ge(n.floatBallConfig || qJ),
      f = ge(0),
      y = ge(0),
      b = ge(!1),
      v = ge(0),
      S = ge(0),
      T = ge(0),
      E = ge(0),
      A = ge(!1),
      D = ge(!1),
      F = Me(
        (O) => {
          if (!a)
            if ((clearTimeout(m.current), O)) c(O);
            else {
              let _ = Be().any || Cr(n);
              m.current = setTimeout(
                () => {
                  c(O), (b.current = !1);
                },
                _ ? 3e3 : 0
              );
            }
        },
        [c, m, b, a, n]
      );
    return (
      Q(() => {
        a || c(!1);
      }, [a]),
      Q(() => {
        if (s.current && x.current) {
          let O = h9(x.current.top);
          (x.current.top = O),
            (s.current.style.top = `${O}px`),
            (s.current.style.display = "flex");
        }
      }, [t, s]),
      Q(() => {
        if (!l.current || !s.current) return;
        let O = (w) => {
            w.preventDefault && w.preventDefault(),
              (f.current = w.clientX),
              (y.current = w.clientY),
              (v.current = Date.now()),
              (D.current = !1),
              g(!1),
              (h.current = !1),
              clearTimeout(m.current),
              F(!0),
              (T.current = w.clientX),
              (E.current = w.clientY),
              (A.current = !1),
              (Be().any || Cr(n)) &&
                (clearTimeout(S.current),
                (S.current = setTimeout(() => {
                  yP({
                    startX: f.current,
                    startY: y.current,
                    endX: T.current,
                    endY: E.current,
                  }) || ((A.current = !0), r());
                }, 500))),
              document.addEventListener("mousemove", _),
              document.addEventListener("mouseup", B),
              document.addEventListener("touchmove", R, { passive: !1 }),
              document.addEventListener("touchend", M, { passive: !1 }),
              document.addEventListener("touchcancel", M, { passive: !1 });
          },
          _ = (w) => {
            if (
              (w.preventDefault && w.preventDefault(),
              !s.current ||
                !l.current ||
                ((T.current = w.clientX), (E.current = w.clientY), A.current))
            )
              return;
            if (
              yP({
                startX: f.current,
                startY: y.current,
                endX: w.clientX,
                endY: w.clientY,
              }) &&
              !D.current
            ) {
              (D.current = !0), g(!0), (h.current = !0);
              let Ee = globalThis.getComputedStyle(s.current),
                $ = parseFloat(Ee.left),
                ee = parseFloat(Ee.right);
              (s.current.style.left = "auto"),
                (s.current.style.right = "auto"),
                x.current.position === "right"
                  ? (s.current.style.left = `${
                      globalThis.innerWidth - ee - s.current.offsetWidth
                    }px`)
                  : (s.current.style.left = `${$}px`);
            }
            if (!h.current) return;
            let U = l.current.getBoundingClientRect(),
              z = s.current.getBoundingClientRect(),
              G = U.width / 2 + (U.left - z.left),
              q = U.height / 2 + (U.top - z.top),
              W = w.clientX - G,
              Z = w.clientY - q,
              ne = h9(Z),
              fe = Math.max(
                0,
                Math.min(W, globalThis.innerWidth - s.current.offsetWidth)
              );
            (s.current.style.top = `${ne}px`),
              (s.current.style.left = `${fe}px`);
          },
          B = async (w) => {
            if (
              (w.preventDefault && w.preventDefault(),
              clearTimeout(S.current),
              P(),
              g(!1),
              (h.current = !1),
              clearTimeout(m.current),
              b.current || F(!1),
              A.current)
            )
              return;
            if (!D.current) {
              e();
              return;
            }
            let L = h9(w.clientY - 45),
              U = globalThis.innerWidth,
              z = s.current?.offsetWidth || 0,
              q =
                (s.current?.offsetLeft || 0) + z / 2 > U / 2 ? "right" : "left";
            s.current &&
              (q === "right"
                ? ((s.current.style.top = `${L}px`),
                  (s.current.style.right = "0px"),
                  (s.current.style.left = "auto"))
                : ((s.current.style.top = `${L}px`),
                  (s.current.style.left = "0px"),
                  (s.current.style.right = "auto"))),
              (x.current = { ...x.current, top: L, position: q }),
              i && ((i.fixedPosition = q), o(i));
            let W = await or();
            Pi({ ...W, floatBallConfig: x.current });
          },
          j = (w) => {
            w.preventDefault && w.preventDefault(), O(w.changedTouches[0]);
          },
          R = (w) => {
            w.preventDefault && w.preventDefault(), _(w.changedTouches[0]);
          },
          M = (w) => {
            w.preventDefault && w.preventDefault(), B(w.changedTouches[0]);
          },
          P = () => {
            document.removeEventListener("mousemove", _),
              document.removeEventListener("touchmove", R),
              document.removeEventListener("mouseup", B),
              document.removeEventListener("touchend", M),
              document.removeEventListener("touchcancel", M);
          };
        return (
          l.current.addEventListener("mousedown", O),
          l.current.addEventListener("touchstart", j, { passive: !1 }),
          () => {
            l.current &&
              (l.current.removeEventListener("mousedown", O),
              l.current.removeEventListener("touchstart", j));
          }
        );
      }, [t, l, s, m, p, n, F, e]),
      Q(() => {
        if (Be().any) return;
        let O = (B) => {
            (B.target === s.current && !b.current) ||
              (!d.current && vP(B) && m.current) ||
              ((b.current = !0), F(!0), clearTimeout(m.current));
          },
          _ = (B) => {
            if (!(!d.current && vP(B) && m.current)) {
              if (h.current) {
                b.current = !1;
                return;
              }
              (B.target === s.current && !b.current) || F(!1);
            }
          };
        return (
          s.current?.addEventListener("mouseover", O),
          s.current?.addEventListener("mouseout", _),
          () => {
            s.current?.removeEventListener("mouseover", O),
              s.current?.removeEventListener("mouseout", _);
          }
        );
      }, [s, p, F, t, b, d]),
      {
        ballRef: l,
        floatBallConfigRef: x,
        containerRef: s,
        active: u,
        setActive: F,
        isDragging: p,
      }
    );
  }
  function yP({ startX: e, startY: t, endX: n, endY: r }) {
    let a = Math.abs(n - e),
      i = Math.abs(r - t);
    return a > bP || i > bP;
  }
  function wP(e, t) {
    let n = e?.fixedPosition || "right",
      r = e?.clickType === "translate",
      a = le(
        () =>
          t
            ? n == "left"
              ? "translateX(55px)"
              : "unset"
            : n == "left"
            ? "translateX(-100%)"
            : "translateX(100%)",
        [n, t]
      ),
      i = le(
        () =>
          t && r ? "" : n == "left" ? "translateX(-15px)" : "translateX(15px)",
        [n, t, r]
      ),
      o = le(
        () =>
          t && r
            ? n == "left"
              ? "translateX(8px)"
              : "translateX(-8px)"
            : n == "left"
            ? "translateX(-60px)"
            : "translateX(60px)",
        [n, t, r]
      ),
      s = le(
        () =>
          t && r
            ? n == "left"
              ? "translateX(8px)"
              : "translateX(-8px)"
            : n == "left"
            ? "translateX(4px)"
            : "translateX(-4px)",
        [n, t, r]
      ),
      l = le(
        () =>
          t && r
            ? n == "left"
              ? "translateX(16px)"
              : "translateX(-2px)"
            : n == "left"
            ? "translateX(10px)"
            : "translateX(2px)",
        [n, t, r]
      );
    return {
      closeTransform: a,
      logoContainerTransform: i,
      settingTransform: o,
      mangaTransform: l,
      sideTransform: s,
    };
  }
  function EP() {
    let [e, t] = V(!1);
    return (
      Q(() => {
        let n = () => {
          document.querySelector("video") && t(!!document.fullscreenElement);
        };
        return (
          document.addEventListener("fullscreenchange", n),
          () => {
            document.removeEventListener("fullscreenchange", n);
          }
        );
      }, []),
      { isFullScreen: e }
    );
  }
  var g9 = "floatBallGuideShowCount",
    GJ = "2024-01-06";
  function AP(e, t) {
    let [n, r] = V(!1),
      a = Me(() => {
        Dt(g9, 0).then((s) => {
          let l = s + 1;
          Ct(g9, l), r(!0);
        });
      }, [r]);
    Q(() => {
      if (!e.config.enableShowFloatingBallGuide || rn() == e.targetLanguage)
        return;
      let s = nt(e.url, t?.guideBlockUrls);
      !t ||
        s ||
        (async () => {
          let l = await Dt(g9, 0),
            u = (await Ha("reportActive")) || "",
            c = await Dt("installedAt", "");
          (c && new Date(c) < new Date(GJ)) ||
            u ||
            (l < (t?.defaultShowGuideNums || 1) && a());
        })();
    }, [e, t, a]),
      Vu ||
        ((Vu = () => {
          a();
        }),
        document.addEventListener(Nd, Vu)),
      Q(
        () => () => {
          Vu && (document.removeEventListener(Nd, Vu), (Vu = null));
        },
        []
      );
    let o = Me(() => {
      r(!1);
    }, []);
    return { isShowGuide: n, handleCloseGuide: o };
  }
  function kP({ ctx: e, fixedPosition: t, setPopupVisible: n }) {
    Gu ||
      ((Gu = async () => {
        n(!0),
          await We(300),
          Ob(e, { arrowDirection: t, type: "translate-service" });
      }),
      document.addEventListener(Od, Gu)),
      Q(
        () => () => {
          Gu && (document.removeEventListener(Od, Gu), (Gu = null));
        },
        []
      );
  }
  function MP() {
    document.dispatchEvent(new CustomEvent(Nd));
  }
  function DP() {
    document.dispatchEvent(new CustomEvent(Od));
  }
  function IP(e, t) {
    let n = ge(0),
      [r, a] = V(!0),
      [i, o] = V(!1),
      s = Gi(),
      [l, u] = s,
      { t: c } = ae(),
      d = Me(() => {
        e?.config.floatBallTooltipRule &&
          (Be().any || Cr(e.localConfig)
            ? o(
                n.current >=
                  (e.config.floatBallTooltipRule.h5MainBtnTooltipMaxShowCount ||
                    2)
              )
            : a(
                n.current >=
                  (e.config.floatBallTooltipRule
                    .mainBtnTooltipImmediateShowCount || 5)
              ));
      }, [a, n, e, o]);
    Q(() => {
      e &&
        ((n.current = e.config.pcFloatBallMainBtnTooltipShownCount || 0),
        Be().any &&
          (n.current = e.config.h5FloatBallMainBtnTooltipShownCount || 0),
        d());
    }, [e]);
    let m = Me(async () => {
        if (!e) return;
        (n.current = n.current + 1), d();
        let h = await un();
        u((x) => {
          let f = { ...h };
          return (
            Be().any
              ? (f.h5FloatBallMainBtnTooltipShownCount = n.current)
              : (f.pcFloatBallMainBtnTooltipShownCount = n.current),
            f
          );
        });
      }, [u, d, n, e]),
      p = le(
        () =>
          Be().any || !r || Cr(e?.localConfig || {})
            ? 0
            : e?.config.floatBallTooltipRule?.mainBtnTooltipDelayTime || 2e3,
        [r, e]
      ),
      g = le(() => {
        if (Be().any || Cr(e?.localConfig || {}))
          return c("floatBall.longPress");
        let h = "";
        return (
          t !== "Original"
            ? (h = c("floatBall.showOriginal"))
            : e?.targetLanguage
            ? (h = c("floatBall.translateToLanguage", {
                language: Pr(e.targetLanguage, e.targetLanguage, !0),
              }))
            : (h = c("floatBall.translate")),
          e?.config.shortcuts.toggleTranslatePage &&
            (h += `(${yo(e.config.shortcuts.toggleTranslatePage)})`),
          h
        );
      }, [t, e]);
    return {
      disableTooltip: i,
      tooltipDelay: p,
      tooltipText: g,
      handleShowTooltip: m,
    };
  }
  function h9(e) {
    return Math.max(Math.min(e, globalThis.innerHeight - 200), 10);
  }
  var f9 = 0;
  function vP(e) {
    return e.target?.id == "manga-button" ||
      e?.fromElement?.id == "manga-button"
      ? ((f9 = Date.now()), !0)
      : Date.now() - f9 < 100;
  }
  function PP(e, t) {
    Q(() => {
      if (wr() || Mt()) return;
      let n = (r) => {
        let a = r.detail;
        a?.open === void 0 || a?.open === null ? e(!t.current) : e(a.open);
      };
      return (
        globalThis.document.addEventListener(Jo, n),
        () => {
          globalThis.document.addEventListener(Jo, n);
        }
      );
    }, [e, t]);
  }
  function LP(e, t) {
    let n = ge(e);
    (n.current = e),
      Q(() => {
        !n.current || !t || Te({ key: "show_float_ball", ctx: n.current });
      }, [n, t]);
  }
  var xP = "imt-float-ball-active";
  function RP(e) {
    Q(() => {
      let t = !!document.querySelector(`meta[name='${xP}'][content='true']`);
      t ||
        (t = new URL(globalThis.location.href).searchParams.get(xP) === "true"),
        t &&
          setTimeout(() => {
            e(!0);
          }, 10);
      let n = (r) => {
        r.detail.active ? e(!0) : e(!1);
      };
      return (
        document.addEventListener(Bd, n),
        () => {
          document.removeEventListener(Bd, n);
        }
      );
    }, []);
  }
  function FP(e) {
    document.dispatchEvent(new CustomEvent(Bd, { detail: { active: e } }));
  }
  function BP() {
    let [e, t] = V(0);
    return (
      Q(() => {
        let n = new MutationObserver(() => {
          let r = globalThis.document.body.style.marginRight;
          t(r ? parseInt(r) : 0);
        });
        return (
          n.observe(globalThis.document.body, { attributeFilter: ["style"] }),
          () => n.disconnect()
        );
      }, []),
      { bodyRight: e }
    );
  }
  function _P() {
    let [e, t] = V(!1);
    Q(() => {
      (async () => {
        let a = await Dt("installedAt", "");
        if (a && new Date(a) < new Date("2025-06-20")) return;
        let { userValue: i, localValue: o } = await Qs("sideNewBadgeClicked");
        i || o || t(!0);
      })();
    }, []);
    let n = Me(async () => {
      qu("sideNewBadgeClicked", !0), t(!1);
    }, [t]);
    return [e, n];
  }
  function b9(e) {
    let [t, n] = V(!1);
    return (
      Q(() => {
        let r = null,
          a = () => {
            r = setTimeout(() => {
              n(!0);
            }, 300);
          },
          i = () => {
            r && (clearTimeout(r), (r = null)), n(!1);
          };
        return (
          e.current?.addEventListener("mouseenter", a),
          e.current?.addEventListener("mouseleave", i),
          () => {
            e.current?.removeEventListener("mouseenter", a),
              e.current?.removeEventListener("mouseleave", i);
          }
        );
      }, [e]),
      [t, n]
    );
  }
  function NP(e, t) {
    let [n, r] = V("hidden");
    return (
      Q(() => {
        async function a() {
          if (X8(e)) {
            r("close");
            return;
          }
          let { userValue: o, localValue: s } = await Qs(
            "rewardCenterOpenTime"
          );
          if (o || s) {
            r("hidden");
            return;
          }
          r("pin");
        }
        a();
      }, []),
      n == "pin" && !t?.enablePinSidePanel && (n = "hidden"),
      t?.disableRewardCenter && (n = "close"),
      n
    );
  }
  function y9(e) {
    return le(
      () =>
        e == "left"
          ? {
              wrapper: { paddingRight: "10px" },
              close: {
                position: "absolute",
                right: 0,
                top: 0,
                display: "block",
                transform: "translate(30%, -30%)",
              },
            }
          : {
              wrapper: { paddingLeft: "10px" },
              close: {
                position: "absolute",
                right: 0,
                top: 0,
                display: "block",
                transform: "translate(30%, -30%)",
              },
            },
      [e]
    );
  }
  var v9 = "imt-fb";
  function jP(e) {
    if (globalThis.innerWidth <= 385)
      return C(ju, {
        isOpen: e.visible,
        onClose: e.onClose,
        children: C(OP, {
          contentStyle: { width: "100%" },
          onChange: e.onChange,
          checked: e.checked,
          title: e.title,
        }),
      });
    let r = (a) => {
      a?.target?.id === `${N}-popup-overlay` && e.onClose();
    };
    return e.visible
      ? C("div", {
          onClick: r,
          id: `${N}-popup-overlay`,
          class: `${N}-popup-overlay side-panel-settings-modal`,
          children: C("div", {
            class: `${N}-popup-wrapper`,
            style: e.style,
            children: C(OP, {
              onClose: e.onClose,
              checked: e.checked,
              onChange: e.onChange,
              title: e.title,
            }),
          }),
        })
      : null;
  }
  function OP(e) {
    let { t } = ae(),
      [n, r] = V(e.checked);
    return C("div", {
      class: `${v9}-close-content`,
      style: e.contentStyle,
      children: [
        C("div", {
          class: "flex justify-between",
          children: [
            C("div", {
              class: `${v9}-close-title`,
              children: t("floatBall.setting"),
            }),
            C("div", {
              class: "clickable",
              onClick: e.onClose,
              children: C(dt, { type: "modal-close" }),
            }),
          ],
        }),
        C("div", {
          class: `${v9}-close-radio-content mt-3`,
          children: C("div", {
            class: "flex items-center justify-between pt-2 pb-2",
            children: [
              C("span", { class: "mr-2 text-sm", children: e.title }),
              C("label", {
                for: "enable",
                class: "flex items-center justify-between mb-0",
                children: C("input", {
                  type: "checkbox",
                  id: "enable",
                  name: "switch",
                  role: "switch",
                  class: "mt-0",
                  onChange: (a) => {
                    r(a.target.checked), e.onChange(a.target.checked);
                  },
                  checked: n,
                }),
              }),
            ],
          }),
        }),
      ],
    });
  }
  function x9({ position: e, isHideContent: t }) {
    let n = ge(null),
      r = ge(null),
      [a, i] = V(null);
    Q(() => {
      fetch(rm)
        .then((s) => s.json())
        .then((s) => i(s))
        .catch((s) => {});
    }, []),
      Q(
        () => (
          n.current &&
            a &&
            (r.current = Au.loadAnimation({
              container: n.current,
              renderer: "svg",
              loop: !0,
              autoplay: !0,
              animationData: a,
            })),
          () => {
            r.current && r.current.destroy();
          }
        ),
        [a]
      );
    let o = {
      width: "146px",
      height: "auto",
      position: "absolute",
      ...(e === "left" ? { left: "35px" } : { right: "20px" }),
      bottom: t ? "-145px" : "-8px",
      transform: e === "left" ? "rotate(5deg) scaleX(-1)" : "rotate(5deg)",
    };
    return C("div", { ref: n, style: { ...o } });
  }
  var fd = "imt-fb";
  function zP(e) {
    let { t } = ae(),
      n = le(() => {
        if (!e.rule) return !0;
        if (
          !e.rule?.guideContentBlockUrls ||
          e.rule.guideContentBlockUrls.length === 0
        )
          return !1;
        let a = globalThis.location.href;
        return e.rule.guideContentBlockUrls.some((i) => {
          try {
            let o = i.replace(/\*/g, ".*");
            return new RegExp(o).test(a);
          } catch {
            return a.includes(i);
          }
        });
      }, [e.rule]);
    Q(() => {
      let a = () => {
        e.onClose();
      };
      return (
        document.addEventListener("click", a),
        () => document.removeEventListener("click", a)
      );
    }, [e.visible]);
    let r = le(() => {
      let a = { position: "absolute", top: n ? -100 : "unset" };
      return (
        e.fixedPosition == "left" && (a.left = 33),
        e.fixedPosition == "right" && (a.right = 33),
        a
      );
    }, [e.fixedPosition, n]);
    return e.visible
      ? C("div", {
          class: `${fd}-guide-container`,
          style: r,
          children: [
            !n &&
              C(it, {
                children: [
                  C(dt, {
                    type: "close",
                    style: {
                      position: "absolute",
                      top: "20px",
                      [e.fixedPosition]: "15px",
                      cursor: "pointer",
                    },
                    onClick: e.onClose,
                  }),
                  C("img", {
                    class: `${fd}-guide-bg ${e.fixedPosition}`,
                    src: VJ,
                    style: {
                      left: e.fixedPosition === "left" ? "22px" : "30px",
                    },
                  }),
                  C("div", {
                    class: `${fd}-guide-content ${e.fixedPosition}`,
                    children: [
                      C("img", {
                        class: `${fd}-guide-img`,
                        src: Wg("images/new_float_ball_intro.png"),
                      }),
                      C("div", {
                        class: `${fd}-guide-message`,
                        children: [
                          t("floatBall.guideClickToTranslate"),
                          Be().any
                            ? `
${t("floatBall.longPress")}`
                            : "",
                        ],
                      }),
                    ],
                  }),
                ],
              }),
            C(x9, { position: e.fixedPosition, isHideContent: n }),
          ],
        })
      : null;
  }
  var VJ =
    "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjc2IiBoZWlnaHQ9IjIyNCIgdmlld0JveD0iMCAwIDI3NiAyMjQiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxnIGZpbHRlcj0idXJsKCNmaWx0ZXIwX2RfMjg2MDBfMTUwMjcxKSI+CjxwYXRoIGQ9Ik0yMCAzNi4xMjg5QzIwIDI1LjA4MzIgMjguOTU0MyAxNi4xMjg5IDQwIDE2LjEyODlIMjM2QzI0Ny4wNDYgMTYuMTI4OSAyNTYgMjUuMDgzMiAyNTYgMzYuMTI4OVYxMDMuMTIyVjEwOC4wNjRWMTEzLjc0OVYxODBDMjU2IDE5MS4wNDYgMjQ3LjA0NiAyMDAgMjM2IDIwMEg0MEMyOC45NTQzIDIwMCAyMCAxOTEuMDQ2IDIwIDE4MFYzNi4xMjg5WiIgZmlsbD0idXJsKCNwYWludDBfbGluZWFyXzI4NjAwXzE1MDI3MSkiLz4KPC9nPgo8ZGVmcz4KPGZpbHRlciBpZD0iZmlsdGVyMF9kXzI4NjAwXzE1MDI3MSIgeD0iMCIgeT0iMC4xMjg5MDYiIHdpZHRoPSIyNzYiIGhlaWdodD0iMjIzLjg3MSIgZmlsdGVyVW5pdHM9InVzZXJTcGFjZU9uVXNlIiBjb2xvci1pbnRlcnBvbGF0aW9uLWZpbHRlcnM9InNSR0IiPgo8ZmVGbG9vZCBmbG9vZC1vcGFjaXR5PSIwIiByZXN1bHQ9IkJhY2tncm91bmRJbWFnZUZpeCIvPgo8ZmVDb2xvck1hdHJpeCBpbj0iU291cmNlQWxwaGEiIHR5cGU9Im1hdHJpeCIgdmFsdWVzPSIwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAxMjcgMCIgcmVzdWx0PSJoYXJkQWxwaGEiLz4KPGZlT2Zmc2V0IGR5PSI0Ii8+CjxmZUdhdXNzaWFuQmx1ciBzdGREZXZpYXRpb249IjEwIi8+CjxmZUNvbXBvc2l0ZSBpbjI9ImhhcmRBbHBoYSIgb3BlcmF0b3I9Im91dCIvPgo8ZmVDb2xvck1hdHJpeCB0eXBlPSJtYXRyaXgiIHZhbHVlcz0iMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMCAwIDAgMC4xIDAiLz4KPGZlQmxlbmQgbW9kZT0ibm9ybWFsIiBpbjI9IkJhY2tncm91bmRJbWFnZUZpeCIgcmVzdWx0PSJlZmZlY3QxX2Ryb3BTaGFkb3dfMjg2MDBfMTUwMjcxIi8+CjxmZUJsZW5kIG1vZGU9Im5vcm1hbCIgaW49IlNvdXJjZUdyYXBoaWMiIGluMj0iZWZmZWN0MV9kcm9wU2hhZG93XzI4NjAwXzE1MDI3MSIgcmVzdWx0PSJzaGFwZSIvPgo8L2ZpbHRlcj4KPGxpbmVhckdyYWRpZW50IGlkPSJwYWludDBfbGluZWFyXzI4NjAwXzE1MDI3MSIgeDE9IjE0NS4zMzEiIHkxPSIxNi4xMjg5IiB4Mj0iMTQ1LjMzMSIgeTI9IjIwMCIgZ3JhZGllbnRVbml0cz0idXNlclNwYWNlT25Vc2UiPgo8c3RvcCBzdG9wLWNvbG9yPSIjRkY5MkJDIi8+CjxzdG9wIG9mZnNldD0iMSIgc3RvcC1jb2xvcj0id2hpdGUiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K";
  var UP = "imt-manga";
  function HP({ visible: e, onClose: t, top: n, fixedPosition: r, ctx: a }) {
    let { t: i } = ae(),
      o = (d) => {
        d?.target?.id === N + "-popup-overlay" && t();
      },
      s = le(() => {
        let d = { position: "absolute", top: 60 };
        return (
          r == "left" && (d.left = 48 - 10),
          r == "right" && (d.right = 48 - 10),
          d
        );
      }, [n, r]),
      l = a.isPro ? i("guide.mangaProTip") : i("guide.mangaNoProTip"),
      u = a.isPro ? i("floatBall.iKnow") : i("upgradeToProWithProfile"),
      c = Me(() => {
        t(), !a.isPro && globalThis.open(ov);
      }, [a, t]);
    return e
      ? (Q(() => {
          Ca(a, "manga_guide");
        }, [a]),
        C("div", {
          onClick: o,
          id: N + "-popup-overlay",
          class: `${N}-popup-overlay`,
          style: { pointerEvents: "all" },
          children: C("div", {
            class: "imt-fb-guide-container",
            style: s,
            children: [
              C("img", {
                class: `${UP}-guide-bg ${r}`,
                src: `${M6}static/extension/images/popup-manga-bg.png`,
              }),
              C("div", {
                class: `${UP}-guide-content ${r}`,
                children: [
                  C("div", { class: "imt-fb-guide-message", children: l }),
                  C("div", {
                    class:
                      "imt-fb-primary-btn imt-fb-guide-button img-manga-guide-button",
                    onClick: c,
                    children: u,
                  }),
                ],
              }),
              C(dt, {
                className: "img-manga-close",
                type: "close",
                onClick: t,
              }),
            ],
          }),
        }))
      : null;
  }
  var sr = "imt-fb";
  function S9(e) {
    let { t } = ae(),
      n = Gi(),
      [r, a, i, o] = n;
    Rh(r, a);
    let s = cP(r),
      l = e.ctx,
      { isFullScreen: u } = EP(),
      [c, d] = V(!1),
      m = ge(c);
    m.current = c;
    let p = ge(!1),
      [g, h] = V(!0),
      { pageStatus: x } = CP(),
      f = i0(l, r),
      y = f?.fixedPosition || "right",
      b = y === "right" ? "left" : "right",
      { isShowGuide: v, handleCloseGuide: S } = AP(e.ctx, f);
    kP({ ctx: l, fixedPosition: y, setPopupVisible: d });
    let T = Me(() => {
        if ((v && S(), f?.clickType === "popup")) return d(!0);
        f?.clickType === "translate" &&
          (J()
            ? dn({
                method: "toggleTranslatePage",
                data: { trigger: "float_ball", currentPageStatus: Ye() },
              })
            : l.rule?.enableDeepFrameTranslatePage
            ? Aw("float_ball", Ye())
            : dn({
                method: "toggleTranslatePage",
                data: { trigger: "float_ball", currentPageStatus: Ye() },
              }));
      }, [f, dn, d, v, S]),
      E = Me(() => {
        d(!0);
      }, [d]),
      A = g && !!s,
      {
        containerRef: D,
        active: F,
        ballRef: O,
        floatBallConfigRef: _,
        setActive: B,
        isDragging: j,
      } = TP({
        handleBallClick: T,
        handleMobileBallLongPress: E,
        isShow: A,
        localConfig: e.localConfig,
        popupVisible: c,
        rule: f,
        updateFloatBallRule: (ee) => {
          a((pe) => ja({ ...pe }, ee));
        },
      }),
      {
        closeTransform: R,
        logoContainerTransform: M,
        settingTransform: P,
        sideTransform: w,
        mangaTransform: L,
      } = wP(f, F);
    LP(l, F);
    let {
      tooltipDelay: U,
      disableTooltip: z,
      tooltipText: G,
      handleShowTooltip: q,
    } = IP(l, x);
    PP(d, m), RP(B);
    let Z = globalThis.innerWidth <= 385;
    if (!A) return null;
    let ne = f?.transparency != null ? f?.transparency / 100 : 0.5,
      fe = F ? 1 : 1 - ne,
      Ee = XJ(),
      { bodyRight: $ } = BP();
    return C(it, {
      children: C("div", {
        class: `${sr}-container ${y} notranslate ${j ? "dragging" : ""}`,
        style: {
          zIndex: u ? -1 : Ia - 10,
          pointerEvents: F || c ? "all" : "none",
          right: $,
        },
        ref: D,
        onTouchStart: Uu.bind(null, !0),
        onMouseDown: Uu.bind(null, !1),
        children: [
          C(eX, {
            ctx: l,
            rule: f,
            bodyRight: $,
            tooltipPosition: b,
            sideTransform: w,
            settingTransform: P,
            ballOpacity: fe,
            active: F,
            isShowGuide: v,
            setActive: B,
            setSettings: a,
          }),
          C($J, {
            ctx: l,
            rule: f,
            bodyRight: $,
            tooltipPosition: b,
            sideTransform: w,
            settingTransform: P,
            ballOpacity: fe,
            active: F,
            isShowGuide: v,
            setActive: B,
            setSettings: a,
          }),
          C(YJ, {
            ctx: l,
            mangaResultRef: p,
            mangaTransform: L,
            floatBallConfigRef: _,
            fixedPosition: y,
            hidden: v,
          }),
          !Ee &&
            C(Tt, {
              enableMobile: !0,
              text: G,
              position: b,
              delay: U,
              onShow: q,
              disable: z || j,
              children: C("div", {
                style: {
                  display: "flex",
                  alignItems: "center",
                  flexDirection: "row",
                },
                children: [
                  C(zP, {
                    visible: v,
                    top: _.current.top,
                    fixedPosition: y,
                    rule: f,
                    ctx: l,
                    onClose: () => {
                      S(), B(!1);
                    },
                  }),
                  C(dt, {
                    type: "close",
                    style: {
                      display: y == "left" ? "none" : "block",
                      opacity: 0,
                    },
                  }),
                  C("div", {
                    class: `${sr}-btn  ${y} btn-animate `,
                    dir: "ltr",
                    ref: O,
                    style: { transform: M, opacity: fe },
                    children: C("div", {
                      children: [
                        C(dt, {
                          type: "logo",
                          className: "imt-fb-logo-img-big-bg",
                        }),
                        C(dt, {
                          type: "translated",
                          className: "imt-float-ball-translated",
                          hidden: x === "Original",
                        }),
                      ],
                    }),
                  }),
                  C(dt, {
                    type: "close",
                    style: {
                      display: y == "left" ? "block" : "none",
                      opacity: 0,
                    },
                  }),
                ],
              }),
            }),
          C("div", {
            style: {
              position: "relative",
              width: "100%",
              opacity: F && !v ? 1 : 0,
            },
            children: C(WJ, {
              ctx: l,
              closeTransform: R,
              setVisible: h,
              setActive: B,
              getModalStyle: () => {
                let ee = C9(_.current.top, 300, y);
                return ee.right && (ee.right = ee.right + $), ee;
              },
            }),
          }),
          C("div", {
            style: { marginTop: "10px", transform: P },
            class: `${sr}-more-buttons btn-animate`,
            children: [
              C(Tt, {
                text: t("floatBall.setting"),
                position: b,
                containerClass: "btn-animate",
                children: C("div", {
                  class: `${sr}-more-button`,
                  onClick: () => {
                    B(!0), d(!0);
                  },
                  children: C(dt, {
                    type: "setting",
                    style: { width: 22, height: 22 },
                  }),
                }),
              }),
              !p.current &&
                C(Tt, {
                  text: t("reportTip"),
                  position: b,
                  containerClass: "btn-animate",
                  children: C("div", {
                    class: `${sr}-more-button`,
                    children: C(dt, {
                      type: "feedback",
                      className: `${sr}-feedback`,
                      onClick: () => {
                        document.dispatchEvent(
                          new CustomEvent(cr, { detail: { type: "webReport" } })
                        );
                      },
                    }),
                  }),
                }),
              w9() && C(JJ, { ctx: l, tooltipPosition: b, rule: f, active: F }),
            ],
          }),
          C(T9, {
            onClose: () => {
              B(!1), d(!1);
            },
            isSheet: Z,
            visible: c,
            getModalStyle: () => {
              let ee = C9(_.current.top, 500, y);
              return ee.right && (ee.right = ee.right + $), ee;
            },
          }),
        ],
      }),
    });
  }
  function WJ(e) {
    let { t } = ae(),
      [n, r] = V(!1),
      a = Gi(),
      [i, o, s, l] = a,
      u = i0(e.ctx, i),
      c = Me(
        (m) => {
          e.ctx &&
            u &&
            (e.setVisible(!1),
            m === "AlwaysClose"
              ? o(
                  (p) => (
                    e.ctx?.rule.imageRule.type === "manga" &&
                      (p.generalRule || (p.generalRule = {}),
                      p.generalRule["imageRule.add"] ||
                        (p.generalRule["imageRule.add"] = {}),
                      (p.generalRule["imageRule.add"].enableMangaFloatBall =
                        !1)),
                    ja({ ...p }, { ...u, enable: !1 })
                  )
                )
              : m === "CurrentWebsite" &&
                o((p) =>
                  ja(
                    { ...p },
                    {
                      ...u,
                      blockUrls: [...u.blockUrls, globalThis.location.hostname],
                    }
                  )
                ));
        },
        [i, ja, e.ctx, u, e.setVisible]
      ),
      d = Li(e.ctx.config.useOnlineOptions) + "#floating";
    return C(it, {
      children: [
        C("div", {
          title: t("floatBall.close"),
          class: `${sr}-close-button`,
          style: { transform: e.closeTransform },
          onClick: () => {
            e.setActive(!0), r(!0);
          },
          children: C(dt, { type: "close" }),
        }),
        C(xh, {
          visible: n,
          onClose: () => {
            e.setActive(!1), r(!1);
          },
          getModalStyle: e.getModalStyle,
          onCloseConfirm: c,
          settingUrl: d,
        }),
      ],
    });
  }
  function T9(e) {
    let t = (n) => {
      n?.target?.id === N + "-popup-overlay" && e.onClose();
    };
    return e.isSheet
      ? C(ju, {
          isOpen: e.visible,
          onClose: e.onClose,
          children: C(Fh, {
            onClose: e.onClose,
            className: "popup-container-sheet",
          }),
        })
      : e.visible
      ? C("div", {
          onClick: t,
          style: e.overlayStyle,
          id: N + "-popup-overlay",
          class: `${N}-popup-overlay`,
          children: C("div", {
            class: `${N}-popup-wrapper`,
            style: e.getModalStyle(),
            children: C(Fh, { onClose: e.onClose }),
          }),
        })
      : null;
  }
  function KJ() {
    return {
      width: globalThis.innerWidth || document.documentElement.clientWidth,
      height: globalThis.innerHeight || document.documentElement.clientHeight,
    };
  }
  var C9 = (e, t, n) => {
      let a = KJ().height,
        i = { position: "fixed" },
        o = 0;
      return (
        b6() && (e = QJ.top),
        (i.top = e - o),
        i.top + t >= a
          ? ((i.bottom = 30), delete i.top)
          : i.top <= 10 && (i.top = 10),
        n === "left" ? (i.left = 65) : (i.right = 65),
        i
      );
    },
    QJ = { position: "right", right: 0, top: 335 };
  function YJ({
    ctx: e,
    mangaResultRef: t,
    floatBallConfigRef: n,
    fixedPosition: r,
    hidden: a,
    className: i,
    mangaTransform: o,
  }) {
    let [s, l] = V(!1),
      { mangaStatus: u } = SP(),
      c = e.rule.imageRule,
      d = !c?.type?.startsWith("manga"),
      { t: m } = ae();
    Q(() => {
      d ||
        or().then((g) => {
          g.showMangaGuide || ((g.showMangaGuide = !0), l(!0), Qt(g));
        });
    }, [d, l]);
    let p = Me(async (g) => {
      if ((g.preventDefault(), g.stopPropagation(), !(await gc()))) {
        l(!0);
        return;
      }
      dn({ method: "toggleTranslateManga" });
    }, []);
    return !hh(c) || a || !c?.enable || !c.enableMangaFloatBall
      ? null
      : ((t.current = !d),
        C("div", {
          hidden: d,
          class: `imt-no-events btn-animate ${i || ""}`,
          id: "manga-button",
          style: { position: "relative" },
          children: [
            C("div", {
              class: "imt-manga-button",
              style:
                u != "Original"
                  ? { opacity: 1, transform: o }
                  : { transform: o },
              children: [
                C(Tt, {
                  enableMobile: !0,
                  text: m("reportTip"),
                  position: "left",
                  delay: 100,
                  onShow: void 0,
                  disable: !1,
                  children: C(dt, {
                    type: "feedback",
                    className: "imt-manga-feedback",
                    onClick: () => {
                      document.dispatchEvent(
                        new CustomEvent(cr, { detail: { type: "mangaReport" } })
                      );
                    },
                  }),
                }),
                C("div", {
                  style: { position: "relative" },
                  children: [
                    C(dt, {
                      type: "manga",
                      onClick: p,
                      hidden: u == "Translating",
                    }),
                    C(dt, {
                      type: "translated",
                      onClick: p,
                      hidden: u != "Translated",
                      className: "imt-manga-translated",
                    }),
                  ],
                }),
                C(dt, {
                  type: "loading",
                  onClick: p,
                  style: "margin:9px",
                  hidden: u != "Translating",
                  className: "imt-float-ball-loading",
                }),
              ],
            }),
            C(HP, {
              ctx: e,
              visible: s,
              top: n.current.top,
              fixedPosition: r,
              onClose: () => {
                l(!1);
              },
            }),
          ],
        }));
  }
  var ZJ = "float_ball_upgrade_show_count";
  function JJ(e) {
    let { tooltipPosition: t, ctx: n, rule: r, active: a } = e,
      { t: i } = ae(),
      o = _a(n.config, n.isPro),
      s = ge(!1),
      [l, u] = V(null),
      c = le(() => Iu(n.config), [n]),
      d = (p) => Iu(p)?.upgradeShowCountCacheKey || ZJ;
    Q(() => {
      di(d(n.config), 0).then((p) => {
        u(p);
      });
    }, [n]);
    let m = le(() => {
      if (!r?.upgradeVisible || o || n.isPro || l == null) return !0;
      let g = Iu(n.config)?.upgradeShowLimit || r.upgradeShowLimit;
      return l >= g;
    }, [r, n, o, l]);
    return (
      Q(() => {
        m ||
          !n?.localConfig ||
          s.current ||
          !a ||
          l == null ||
          ((s.current = !0), mi(d(n.config), l + 1));
      }, [m, n, s, a, l]),
      m
        ? null
        : c
        ? C(Tt, {
            text: c.tips || i("floatBall.upgrade"),
            position: t,
            containerClass: "btn-animate",
            multiple: !0,
            tipStyle: { width: 210, maxWidth: 210 },
            children: C("div", {
              class: `${sr}-upgrade-button`,
              onClick: () => {
                globalThis.open(c.floatBallUrl || Bf, "_blank");
              },
              style: {
                width: 34,
                height: 34,
                borderRadius: 17,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                background:
                  "linear-gradient(90deg, #CEFBFA 11.33%, #D7F56F 63.75%, #FCCD5E 100%)",
              },
              children: C("div", {
                style: {
                  width: 30,
                  height: 30,
                  borderRadius: 15,
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  border: "1px solid white",
                  background:
                    "linear-gradient(90deg, #CEFBFA 11.33%, #D7F56F 63.75%, #FCCD5E 100%)",
                },
                children: C(dt, { type: "upgrade" }),
              }),
            }),
          })
        : C(Tt, {
            text: i("floatBall.upgrade"),
            position: t,
            containerClass: "btn-animate",
            multiple: !0,
            tipStyle: { width: 210, maxWidth: 210 },
            children: C("div", {
              class: `${sr}-upgrade-button`,
              onClick: () => {
                globalThis.open(Bf, "_blank");
              },
              children: C(dt, { type: "upgrade" }),
            }),
          })
    );
  }
  function XJ() {
    let e = "";
    return (
      Mt() ? (e = fs() || "") : wr() && (e = kl() || ""), !!e?.startsWith("2.")
    );
  }
  function $J({
    ctx: e,
    rule: t,
    tooltipPosition: n,
    sideTransform: r,
    settingTransform: a,
    ballOpacity: i,
    active: o,
    isShowGuide: s,
    bodyRight: l,
    setActive: u,
    setSettings: c,
  }) {
    if (!t) return null;
    let { t: d } = ae(),
      m = t?.fixedPosition || "right",
      p = t?.enableSidePanel == !1,
      g = t?.enablePinSidePanel == !0,
      h = ge(null),
      [x] = b9(h),
      [f, y] = _P(),
      b = y9(m);
    return w9() || p
      ? null
      : C(it, {
          children: C("div", {
            style: { transform: g ? r : a, opacity: g ? i : 1, ...b.wrapper },
            ref: h,
            class: "btn-animate",
            children: C(Tt, {
              text: d("sidePanelTooltip"),
              position: n,
              containerClass: "btn-animate",
              children: C("div", {
                class: `${sr}-btn ${sr}-more-button ${sr}-side`,
                onClick: () => {
                  y(), Eo(e, "float_ball");
                },
                children: [
                  f && C(dt, { type: "side-new-badge" }),
                  C(dt, { type: "side" }),
                  g
                    ? C("svg", {
                        width: "14",
                        height: "14",
                        viewBox: "0 0 14 14",
                        fill: "none",
                        xmlns: "http://www.w3.org/2000/svg",
                        style: { ...b.close, display: x ? "block" : "none" },
                        onClick: (v) => {
                          v.stopPropagation(),
                            Te({
                              key: "pin_side_panel",
                              ctx: e,
                              params: { trigger: "0" },
                            }),
                            c((S) => {
                              if (t)
                                return (t.enablePinSidePanel = !1), ja(S, t);
                            });
                        },
                        children: [
                          C("g", {
                            "clip-path": "url(#clip0_34242_2353)",
                            children: [
                              C("path", {
                                d: "M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14Z",
                                fill: "#B1B1B1",
                                "fill-opacity": "0.32",
                              }),
                              C("mask", {
                                id: "mask0_34242_2353",
                                style: "mask-type:alpha",
                                maskUnits: "userSpaceOnUse",
                                x: "1",
                                y: "1",
                                width: "12",
                                height: "12",
                                children: C("rect", {
                                  x: "1",
                                  y: "1",
                                  width: "12",
                                  height: "12",
                                  fill: "#D9D9D9",
                                }),
                              }),
                              C("g", {
                                mask: "url(#mask0_34242_2353)",
                                children: C("path", {
                                  d: "M7.86447 3.67324H6.13622V4.72999L4.80409 3.39199C4.75018 3.33699 4.70972 3.27808 4.68272 3.21524C4.65572 3.15241 4.64222 3.09533 4.64222 3.04399C4.64222 2.93141 4.68193 2.8352 4.76134 2.75537C4.84076 2.67562 4.94514 2.63574 5.07447 2.63574H8.98322C9.12864 2.63574 9.25147 2.68883 9.35172 2.79499C9.45189 2.90124 9.50197 3.04578 9.50197 3.22862C9.50197 3.35203 9.46122 3.46245 9.37972 3.55987C9.29822 3.65737 9.18897 3.69516 9.05197 3.67324H8.90197V6.36774C8.90197 6.51316 8.85214 6.63599 8.75247 6.73624C8.65272 6.83641 8.53051 6.88649 8.38585 6.88649C8.24118 6.88649 8.11809 6.83641 8.01659 6.73624C7.91518 6.63599 7.86447 6.51316 7.86447 6.36774V3.67324ZM6.4816 11.974V9.13599H4.57509C4.36193 9.13599 4.19043 9.06703 4.06059 8.92912C3.93076 8.79112 3.86584 8.62983 3.86584 8.44524C3.86584 8.35591 3.88509 8.26499 3.92359 8.17249C3.96209 8.08008 4.01984 7.99437 4.09684 7.91537L5.09872 6.89549V6.36149L2.32422 3.58412C2.22664 3.48645 2.1788 3.37678 2.18072 3.25512C2.18272 3.13345 2.23155 3.02483 2.32722 2.92924C2.42489 2.83158 2.53614 2.78274 2.66097 2.78274C2.7858 2.78274 2.89701 2.83158 2.99459 2.92924L10.9898 10.9245C11.0863 11.0209 11.1351 11.13 11.1361 11.2516C11.1371 11.3733 11.0898 11.4839 10.9941 11.5835C10.8984 11.6772 10.7867 11.7235 10.6588 11.7225C10.5311 11.7215 10.4194 11.6732 10.3237 11.5776L7.87909 9.13599L7.51909 9.14199V11.974C7.51909 12.1195 7.46926 12.2423 7.3696 12.3425C7.26985 12.4427 7.14764 12.4927 7.00297 12.4927C6.8583 12.4927 6.73522 12.4427 6.63372 12.3425C6.5323 12.2423 6.4816 12.1195 6.4816 11.974ZM5.35909 8.09849H6.83872L6.08834 7.35124L6.09434 7.35724L5.35909 8.09849Z",
                                  fill: "white",
                                }),
                              }),
                            ],
                          }),
                          C("defs", {
                            children: C("clipPath", {
                              id: "clip0_34242_2353",
                              children: C("rect", {
                                width: "14",
                                height: "14",
                                fill: "white",
                              }),
                            }),
                          }),
                        ],
                      })
                    : C("svg", {
                        width: "14",
                        height: "14",
                        viewBox: "0 0 14 14",
                        fill: "none",
                        xmlns: "http://www.w3.org/2000/svg",
                        style: { ...b.close, display: x ? "block" : "none" },
                        onClick: (v) => {
                          v.stopPropagation(),
                            Te({
                              key: "pin_side_panel",
                              ctx: e,
                              params: { trigger: "1" },
                            }),
                            c((S) => {
                              if (t)
                                return (t.enablePinSidePanel = !0), ja(S, t);
                            });
                        },
                        children: [
                          C("g", {
                            "clip-path": "url(#clip0_34242_2370)",
                            children: [
                              C("path", {
                                d: "M7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14Z",
                                fill: "#B1B1B1",
                                "fill-opacity": "0.32",
                              }),
                              C("mask", {
                                id: "mask0_34242_2370",
                                style: "mask-type:alpha",
                                maskUnits: "userSpaceOnUse",
                                x: "1",
                                y: "1",
                                width: "12",
                                height: "12",
                                children: C("rect", {
                                  x: "1",
                                  y: "1",
                                  width: "12",
                                  height: "12",
                                  fill: "#D9D9D9",
                                }),
                              }),
                              C("g", {
                                mask: "url(#mask0_34242_2370)",
                                children: C("path", {
                                  d: "M8.90169 3.66124V6.89549L9.94631 7.94012C9.9939 7.9877 10.0317 8.04208 10.0597 8.10324C10.0877 8.16441 10.1017 8.23328 10.1017 8.30987V8.61487C10.1017 8.76095 10.0516 8.88433 9.95144 8.98499C9.85119 9.08566 9.72835 9.13599 9.58294 9.13599H7.51881V11.8871C7.51881 12.0325 7.46898 12.1553 7.36931 12.2555C7.26956 12.3557 7.14735 12.4059 7.00269 12.4059C6.85802 12.4059 6.73494 12.3557 6.63344 12.2555C6.53202 12.1553 6.48131 12.0325 6.48131 11.8871V9.13599H4.41719C4.27177 9.13599 4.14894 9.08566 4.04869 8.98499C3.94852 8.88433 3.89844 8.76095 3.89844 8.61487V8.30987C3.89844 8.23328 3.91244 8.16441 3.94044 8.10324C3.96844 8.04208 4.00623 7.9877 4.05381 7.94012L5.09844 6.89549V3.66124H4.94844C4.82094 3.64733 4.71406 3.59299 4.62781 3.49824C4.54156 3.40358 4.49844 3.28987 4.49844 3.15712C4.49844 3.01245 4.54852 2.88937 4.64869 2.78787C4.74894 2.68645 4.87177 2.63574 5.01719 2.63574H8.98294C9.12835 2.63574 9.25119 2.68558 9.35144 2.78524C9.4516 2.88491 9.50169 3.00712 9.50169 3.15187C9.50169 3.28462 9.45856 3.3992 9.37231 3.49562C9.28606 3.59212 9.17919 3.64733 9.05169 3.66124H8.90169ZM5.36169 8.09849H8.63844L7.86419 7.32424V3.67324H6.13594V7.32424L5.36169 8.09849Z",
                                  fill: "white",
                                }),
                              }),
                            ],
                          }),
                          C("defs", {
                            children: C("clipPath", {
                              id: "clip0_34242_2370",
                              children: C("rect", {
                                width: "14",
                                height: "14",
                                fill: "white",
                              }),
                            }),
                          }),
                        ],
                      }),
                ],
              }),
            }),
          }),
        });
  }
  function eX({
    ctx: e,
    rule: t,
    tooltipPosition: n,
    sideTransform: r,
    settingTransform: a,
    ballOpacity: i,
    active: o,
    isShowGuide: s,
    bodyRight: l,
    setActive: u,
    setSettings: c,
  }) {
    let { t: d } = ae(),
      m = t?.fixedPosition || "right",
      p = t?.enableSidePanel == !1,
      g = ge(null),
      [h] = b9(g),
      x = NP(e, t),
      f = y9(m),
      [y, b] = V(!1),
      [v, S] = V({});
    return w9() || p || x == "close"
      ? null
      : C(it, {
          children: [
            C("div", {
              style: {
                transform: x == "pin" ? r : a,
                opacity: x == "pin" ? i : 1,
                ...(x == "pin" ? f.wrapper : {}),
              },
              ref: g,
              class: "btn-animate",
              children: C(Tt, {
                text: d("rewardCenter.title"),
                position: n,
                containerClass: "btn-animate",
                children: C("div", {
                  class: `${sr}-btn ${sr}-more-button ${sr}-reward`,
                  onClick: () => {
                    Jl(e, "float_ball");
                  },
                  children: [
                    C(dt, { type: "reward" }),
                    C(dt, {
                      type: "close",
                      style: { opacity: h && !s ? 1 : 0, ...f.close },
                      onClick: (T) => {
                        T.preventDefault(), T.stopPropagation();
                        let E = g?.current?.getBoundingClientRect()?.top,
                          A = C9(E ? E - 60 : 0, 300, m);
                        A.right && (A.right = A.right + l), S(A), u(!0), b(!0);
                      },
                    }),
                  ],
                }),
              }),
            }),
            C("div", {
              style: {
                position: "relative",
                width: "100%",
                opacity: o && !s ? 1 : 0,
              },
              children: C(jP, {
                visible: y,
                checked: t?.disableRewardCenter,
                title: d("disableRewardCenter"),
                onClose: () => {
                  u(!1), b(!1);
                },
                onChange: (T) => {
                  Te({ key: "close_reward_center", ctx: e }),
                    u(!1),
                    b(!1),
                    c((E) => {
                      if (t) return (t.disableRewardCenter = T), ja(E, t);
                    });
                },
                style: v,
              }),
            }),
          ],
        });
  }
  function w9() {
    return Be().any || jv();
  }
  async function E9() {
    try {
      let e = await tn(),
        t = { url: globalThis.location.href, config: e },
        n = await Fn(t),
        r = document.createElement("div");
      (r.id = N + "-browser-popup"), r.setAttribute("style", "all: initial");
      let a = r.attachShadow({ mode: "open" });
      tX(a, e, n), document.documentElement.appendChild(r);
    } catch {}
  }
  function tX(e, t, n) {
    let r = document.createElement("div");
    (r.id = "mount"), (r.style.display = "block");
    let a = Le(),
      i = [
        a.IMMERSIVE_TRANSLATE_PICO_CSS,
        a.IMMERSIVE_TRANSLATE_COMMON_CSS,
        a.IMMERSIVE_TRANSLATE_POPUP_CSS,
        a.IMMERSIVE_TRANSLATE_PAGE_POPUP_CSS,
      ].join(`
`);
    nX(e, [i]),
      e.appendChild(r),
      Na(C(Po, { lang: t.interfaceLanguage, children: C(rX, { ctx: n }) }), r);
  }
  function nX(e, t) {
    for (let n of t) {
      if (J() && !zn() && typeof GM !== void 0 && GM.addElement) {
        GM.addElement(e, "style", { textContent: n });
        continue;
      }
      e.appendChild(document.createElement("style")).textContent = n;
    }
  }
  function rX(e) {
    let [t, n] = V(!1),
      r = ge(t);
    r.current = t;
    let a = ge(""),
      i = ge(""),
      [o, s] = V();
    Q(() => {
      let u = (c) => {
        let d = c.detail;
        (a.current = d.style || ""),
          (i.current = d.overlayStyle || ""),
          s(d.isSheet),
          d.open === void 0 || d.open === null ? n(!r.current) : n(d.open);
      };
      return (
        globalThis.document.addEventListener(Jo, u),
        () => {
          globalThis.document.addEventListener(Jo, u);
        }
      );
    }, [r]);
    let l = le(() => {
      if (o != null) return o;
      let u = globalThis.innerWidth;
      return Be().any && u <= 450;
    }, [o]);
    return C(T9, {
      visible: t,
      overlayStyle: `z-index: 2147483647; background-color: rgba(0, 0, 0, 0.5); ${i.current}`,
      getModalStyle: () =>
        `position: fixed; top: 50%; left: 50%; transform: translateX(-50%) translateY(-50%); ${a.current}`,
      isSheet: l,
      onClose: () => {
        n(!1);
      },
    });
  }
  function Bh(e) {
    document.dispatchEvent(new CustomEvent(Jo, { detail: e || {} }));
  }
  function aX(e, t) {
    for (let n of t) {
      if (J() && !zn() && typeof GM !== void 0 && GM.addElement) {
        GM.addElement(e, "style", { textContent: n });
        continue;
      }
      e.appendChild(document.createElement("style")).textContent = n;
    }
  }
  async function _h() {
    (gs()?.supportedMangaSites?.length || 0) < 10 && (await hd());
    let n = await Wn(),
      r = await un(),
      a = { url: globalThis.location.href, config: n },
      i = await Fn(a),
      o = await n3(i, r),
      s = nt(i.url, o?.blockUrls),
      l = await nn();
    if (
      !(
        o?.enable ||
        (i.rule.imageRule.enableMangaFloatBall &&
          i.rule.imageRule.type === "manga")
      ) ||
      s
    )
      return E9();
    (wr() || Mt()) && E9();
    let c = document.createElement("div");
    (c.id = N + "-popup"), c.setAttribute("style", "all: initial");
    let d = c.attachShadow({ mode: "open" });
    return iX(d, n, l, i), document.documentElement.appendChild(c), c;
  }
  function qP() {
    let e = document.getElementById(N + "-popup");
    e && e.remove(), _h();
  }
  function iX(e, t, n, r) {
    let a = document.createElement("div");
    (a.id = "mount"), (a.style.display = "block");
    let i = Le(),
      o = [
        i.IMMERSIVE_TRANSLATE_PICO_CSS,
        i.IMMERSIVE_TRANSLATE_COMMON_CSS,
        i.IMMERSIVE_TRANSLATE_POPUP_CSS,
        i.IMMERSIVE_TRANSLATE_PAGE_POPUP_CSS,
      ].join(`
`);
    aX(e, [o]),
      e.appendChild(a),
      Na(
        C(Po, {
          lang: t.interfaceLanguage,
          children: C(S9, { localConfig: n, ctx: r }),
        }),
        a
      );
  }
  var A9 = !1;
  async function GP(e) {
    if (!tt()) {
      if (!e) {
        let t = await Wn(),
          n = { url: globalThis.location.href, config: t };
        e = await Fn(n);
      }
      e.config.debug && I.setLevel("debug"),
        e.isTranslateExcludeUrl
          ? I.debug("detect exclude url, do not inject anything.")
          : ((A9 = !0),
            _h().catch((t) => {
              I.debug("init popup error", t);
            }));
    }
  }
  async function VP() {
    A9 ||
      ((A9 = !0),
      _h().catch((e) => {
        I.error("init popup error", e);
      }));
  }
  async function WP(e, t) {
    let n, r, a;
    if (t) {
      let s = bg(e);
      (n = new Blob([s], { type: "text/html" })),
        (r = await jt()),
        (r = bo(!1, r)),
        (a = Lp());
    }
    let o = { subtitlePositions: (await nn()).subtitlePositions };
    return {
      siteUrl: location.href,
      pluginVersion: Pt(),
      isMobile: Be().any,
      platform: ya(),
      sourceLanguage: e.sourceLanguage,
      targetLanguage: e.targetLanguage,
      userConfig: {
        targetLanguage: e.targetLanguage,
        userTranslationServices: { translationService: e.translationService },
        ...r,
      },
      localConfig: o,
      usedGlossaries: a,
      userAgent: navigator.userAgent,
      htmlBlob: n,
    };
  }
  async function KP(e, t, n, r, a, i) {
    let { htmlBlob: o, ...s } = r,
      l = await Wr(),
      u = {
        resource: r.siteUrl,
        reason: t,
        metaData: JSON.stringify(s),
        feedType: a,
        deviceId: l,
        domain: location.hostname,
      };
    return (
      o && n && (u.base64_file = await ml(o)),
      i && (u.contactInfo = i),
      Es()
        ? await Se({
            url: `${Oe}v1/feed-back/web-report-log`,
            method: "POST",
            requestType: "formData",
            body: u,
            headers: { token: e.user?.token || "" },
          })
        : await fetch(`${Oe}v1/feed-back/web-report-log`, {
            method: "POST",
            body: Yi(u),
            headers: { token: e.user?.token || "" },
          }).then(async (c) => (await za(c, "web"), c.json()))
    );
  }
  function QP(e, t, n) {
    if (e == "manga" || e == "image") return HD(t, n);
    if (e == "web" || e == "docTranslationForWeb") return WP(t, n);
  }
  function YP(e, t, n, r, a, i) {
    if (e == "manga" || e == "image") return qD(t, n, a, i, e, r);
    if (e == "web" || e == "docTranslationForWeb") return KP(t, n, a, i, e, r);
  }
  var Nn = {
    background: "#FAFBFC",
    border: "#ECF0F7",
    text: "#333333",
    textSecondary: "#666666",
    error: "#EA4C89",
    required: "#F53F3F",
    success: "#68CD52",
    buttonBackground: "#007AFF",
    buttonText: "#FFFFFF",
    inputBackground: "#FAFBFC",
    modalBackground: "#FFFFFF",
    checkColor: "#EA4C89",
  };
  function Nh(e, t, n, r, a, i, o) {
    let s = ye.bind(null, t.config.interfaceLanguage),
      l =
        /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9-]+(?:\.[a-zA-Z0-9-]+)+$/;
    function u(O) {
      return l.test(O.trim());
    }
    let c = () => p.value.trim() === "" || !u(x.value) || x.value.trim() === "",
      d = document.createElement("div");
    (d.innerText = s("reportInfo.title")),
      d.setAttribute(
        "style",
        `text-align:left;margin-top:-20px;color:${Nn.text};`
      ),
      r.append(d);
    let m = document.createElement("div");
    m.setAttribute("style", "display:flex;flex-direction:column;");
    let p = document.createElement("textarea");
    (p.placeholder = s("reportInfo.reasonDesc")),
      (p.required = !0),
      p.setAttribute(
        "style",
        `border-radius: 10px;
      border: 1px solid ${Nn.border};
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      min-height: 100px;
      padding: 9px 12px;
      flex: 1;
      outline: none;`
      ),
      p.setAttribute("id", "reason");
    let g = document.createElement("div");
    g.setAttribute("style", "display: flex; text-align: left;");
    let h = document.createElement("label");
    (h.htmlFor = p.id),
      (h.innerHTML = `<span style="color: ${Nn.error};">*</span>${s(
        "reportInfo.reasonLabel"
      )}`),
      h.setAttribute("style", `color: ${Nn.text};margin-right: 10px;`),
      g.append(h),
      g.append(p),
      p.addEventListener("input", function () {
        A.disabled = c();
      }),
      p.addEventListener("keydown", function (O) {
        O.stopPropagation();
      }),
      m.append(g);
    let x = document.createElement("input");
    (x.type = "email"),
      (x.id = "emailReport"),
      x.setAttribute(
        "style",
        `border: 1px solid ${Nn.border};
      border-radius: 10px;
      padding: 4px 8px;
      background: ${Nn.inputBackground};
      color: ${Nn.text};
      font-size: 14px;
      width: 303px;
      height: 45px;
      box-sizing: border-box;
      margin: 0 1px;
      outline: none;`
      ),
      (x.placeholder = s("reportInfo.emailPlaceholder")),
      et.get(gt, null).then((O) => {
        if (!O) return;
        let _ =
          t.config.ignoreReportEmailRegex &&
          new RegExp(t.config.ignoreReportEmailRegex).test(O.email);
        O.email && !_ && (x.value = O.email);
      });
    let f = document.createElement("label");
    (f.htmlFor = x.id),
      (f.innerHTML = `<span style="color: transparent;">*</span>${s(
        "reportInfo.email"
      )}`),
      f.setAttribute(
        "style",
        `display: flex; margin-right: 10px;word-break:keep-all;color:${Nn.text};`
      );
    let y = document.createElement("div");
    y.setAttribute(
      "style",
      "display: flex; align-items: center; text-align: left;"
    );
    let b = document.createElement("div");
    (b.textContent = s("reportInfo.emailError")),
      b.setAttribute(
        "style",
        `color: ${Nn.error}; font-size: 12px; margin-top: 4px;margin-bottom: 4px; visibility: hidden;padding-right: 140px;`
      );
    let v = (O) => {
      let _ = O.trim();
      return _
        ? u(_)
          ? ((b.style.visibility = "hidden"), !0)
          : ((b.textContent = s("reportInfo.emailError")),
            (b.style.visibility = "visible"),
            !1)
        : ((b.textContent = s("reportInfo.emailEmptyMsg")),
          (b.style.visibility = "visible"),
          !1);
    };
    y.append(f),
      y.append(x),
      a.append(y),
      a.append(b),
      a.append(m),
      setTimeout(() => {
        v(x.value), (A.disabled = c());
      }, 100),
      x.addEventListener("input", function () {
        v(this.value), (A.disabled = c());
      }),
      x.addEventListener("keydown", function (O) {
        O.stopPropagation();
      });
    let S = document.createElement("input");
    (S.type = "checkbox"),
      (S.checked = !0),
      (S.id = "cbMangaReport"),
      S.setAttribute("style", `background-color:${Nn.inputBackground};`);
    let T = document.createElement("label");
    (T.htmlFor = S.id),
      (T.innerText = s("reportInfo.attachLog")),
      T.setAttribute("style", `margin-left:8px;color:${Nn.text};`);
    let E = document.createElement("div");
    E.setAttribute(
      "style",
      `margin-left: 58px;text-align:left;display:flex;align-items:center;accent-color:${Nn.checkColor};`
    ),
      E.append(S),
      E.append(T),
      a.append(E);
    let A = document.createElement("button");
    (A.className = N + "-btn"),
      (A.innerText = s("reportInfo.submit")),
      (A.disabled = !0);
    let D = null;
    (wr() || Mt()) &&
      Co.getBaseInfo().then((O) => {
        D = O;
      }),
      (A.onclick = async () => {
        if (A.uploading || p.value.trim() === "" || !u(x.value)) return;
        let O = await QP(e, t, S.checked);
        D && (O = { ...O, ...D });
        try {
          ZP(A, !0),
            await YP(e, t, p.value, x.value, S.checked, O),
            oX(s, r, a, i, o);
        } catch {
          sX(t, n, s, r, a, i, o, e);
        } finally {
          (A.uploading = !1), ZP(A, !1);
        }
      }),
      i.appendChild(A);
    let F = document.createElement("div");
    F.setAttribute("style", "flex-basis:100%;width:100%;"), i.appendChild(F);
  }
  function ZP(e, t) {
    let n = e.querySelector("#report-loading");
    n?.remove(),
      t &&
        ((n = document.createElement("div")),
        (n.id = "report-loading"),
        n.classList.add(N + "-loading-spinner"),
        e.appendChild(n));
  }
  function oX(e, t, n, r, a) {
    (t.innerHTML = ""), (n.innerHTML = ""), (r.innerHTML = "");
    let i = document.createElement("div");
    (i.innerHTML = `<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12094_60954)">
  <path d="M36 72C26.4522 72 17.2955 68.2072 10.5442 61.4558C3.79285 54.7045 0 45.5478 0 36C0 26.4522 3.79285 17.2955 10.5442 10.5442C17.2955 3.79285 26.4522 0 36 0C45.5478 0 54.7045 3.79285 61.4558 10.5442C68.2072 17.2955 72 26.4522 72 36C72 45.5478 68.2072 54.7045 61.4558 61.4558C54.7045 68.2072 45.5478 72 36 72ZM32.5131 45.4783L22.2171 35.0589L15.4286 41.544C20.3657 44.2029 27.2829 49.0526 33.2074 56.5714C37.3937 48.7029 50.2971 32.6057 56.5714 31.1657C55.5583 27.108 54.9874 19.4914 56.5714 15.4286C43.704 23.9143 32.5131 45.4783 32.5131 45.4783Z" fill="#68CD52"/>
  </g><defs><clipPath id="clip0_12094_60954">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `),
      n.append(i);
    let o = document.createElement("div");
    (o.innerText = e("reportInfo.submitSuccess")),
      o.setAttribute(
        "style",
        `color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`
      ),
      n.append(o);
    let s = document.createElement("button");
    s.setAttribute("style", "margin-top:36px"),
      (s.className = N + "-btn"),
      (s.innerText = e("reportInfo.ok")),
      (s.onclick = () => {
        a(s, !0);
      }),
      r.append(s);
  }
  function sX(e, t, n, r, a, i, o, s) {
    (r.innerHTML = ""), (a.innerHTML = ""), (i.innerHTML = "");
    let l = document.createElement("div");
    (l.innerHTML = `<svg width="72" height="72" viewBox="0 0 72 72" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g clip-path="url(#clip0_12096_60967)">
  <path d="M36 0C16.1494 0 0 16.1474 0 35.9955C0 55.8526 16.1494 72 36 72C55.8506 72 72 55.8526 72 36.0045C72 16.1565 55.8506 0 36 0ZM36 61.8458C32.2086 61.8458 29.1304 58.768 29.1304 54.9771C29.1304 51.1862 32.2086 48.1083 36 48.1083C39.7914 48.1083 42.8696 51.1862 42.8696 54.9771C42.8696 58.777 39.7914 61.8458 36 61.8458ZM36 44.3535C31.4233 44.3535 27.7492 22.962 27.7492 18.4129C27.7492 13.8548 31.4503 10.1632 36.009 10.1632C40.5677 10.1632 44.2598 13.8548 44.2598 18.4129C44.2508 22.962 40.5767 44.3535 36 44.3535Z" fill="#EA4C89"/>
  </g><defs><clipPath id="clip0_12096_60967">
  <rect width="72" height="72" fill="white"/></clipPath></defs></svg>
  `),
      a.append(l);
    let u = document.createElement("div");
    (u.innerText = n("reportInfo.submitFail")),
      u.setAttribute(
        "style",
        `color: ${Nn.text};text-align: center;font-size: 20px;font-weight: 700;margin: 24px 36px;`
      ),
      a.append(u);
    let c = document.createElement("div");
    (c.innerText = n("reportInfo.submitFailDes")),
      c.setAttribute(
        "style",
        `color: ${Nn.text};text-align: center;font-size: 14px;font-style: normal;font-weight: 400;margin:12px; 16px 0;`
      ),
      a.append(c);
    let d = document.createElement("button");
    d.setAttribute("style", "margin-top:36px"),
      (d.className = N + "-btn"),
      (d.innerText = n("error.retry")),
      (d.onclick = () => {
        (r.innerHTML = ""),
          (a.innerHTML = ""),
          (i.innerHTML = ""),
          Nh(s, e, t, r, a, i, o);
      }),
      i.append(d);
  }
  var ai = null;
  async function JP(e, { srcUrl: t }) {
    if (!ai) {
      I.error("rightMenu image ele not found", t, ai);
      return;
    }
    if (ai.translationStatus !== "translating") {
      if (ai.hasTranslate) {
        ka(ai);
        return;
      }
      if (!aI(t, ai))
        try {
          let n = new URL(t),
            r = new URL(ai.src);
          if (!(n.origin === r.origin && n.pathname === r.pathname)) {
            I.error("rightMenu image srcUrl not match", n, r);
            return;
          }
        } catch (n) {
          I.error("rightMenu image srcUrl not invalid", t, ai.src, n);
          return;
        }
      Fr({ ctx: e, ele: ai, isSingleImage: !0, trigger: "image_right_menu" });
    }
  }
  function XP() {
    document.addEventListener("contextmenu", (e) => {
      e.target instanceof HTMLImageElement && (ai = e.target);
    });
  }
  var $P = 0;
  function Ys(e) {
    let t = Date.now();
    if (t - $P < 2e3 || (($P = t), lX())) return;
    let n = `${N}-modal-root`,
      r = `${N}-modal`,
      a = document.getElementById(n),
      i = `${N}-modal-title`,
      o = `${N}-modal-body`,
      s = `${N}-modal-footer`,
      l;
    if (a) {
      l = a.shadowRoot.querySelector(`.${r}`);
      let u = l.querySelector(`.${i}`),
        c = l.querySelector(`.${s}`),
        d = l.querySelector(`.${o}`);
      (u.innerHTML = ""),
        (c.innerHTML = ""),
        (d.innerHTML = ""),
        e(l, u, d, c, bd);
    } else {
      (a = document.createElement("div")),
        a.setAttribute("translate", "no"),
        (a.className = `no-translate ${N}-error-modal-shadow-root`),
        (a.id = n),
        (a.style.all = "initial"),
        (a.style.zIndex = "2147483647"),
        document.body.appendChild(a);
      let u = a.attachShadow({ mode: "open" }),
        c = document.createElement("style"),
        d = Le();
      (c.textContent = d.IMMERSIVE_TRANSLATE_INPUT_INJECTED_CSS),
        u.appendChild(c),
        (l = document.createElement("div")),
        (l.className = r + " notranslate"),
        (l.id = r);
      let m = document.createElement("div");
      (m.className = N + "-modal-content notranslate"), l.appendChild(m);
      let p = document.createElement("span");
      (p.textContent = "\xD7"), (p.className = N + "-close"), m.appendChild(p);
      let g = document.createElement("div");
      (g.className = i + " notranslate"), m.appendChild(g);
      let h = document.createElement("div");
      (h.className = o + " notranslate"), m.appendChild(h);
      let x = document.createElement("div");
      (x.className = s),
        m.appendChild(x),
        u.appendChild(l),
        (p.onclick = function () {
          l.style.display = "none";
        }),
        u.addEventListener("click", (f) => {
          f.target == l && (l.style.display = "none");
        }),
        e(l, g, h, x, bd);
    }
    setTimeout(() => {
      uX();
    }, 100);
  }
  function lX() {
    let e = document.querySelector(`#${N}-modal-root`);
    return e && e.shadowRoot
      ? e.shadowRoot.querySelector(`#${N}-modal`)?.style.display == "block"
      : !1;
  }
  function uX() {
    let e = document.querySelector(`#${N}-modal-root`);
    if (e && ((e.style.display = "block"), e.shadowRoot)) {
      let t = e.shadowRoot.querySelector(`#${N}-modal`);
      t && (t.style.display = "block");
    }
  }
  function bd(e, t = !1) {
    let n = document.querySelector(`#${N}-modal-root`);
    if (!n || n.style.display == "none") return;
    let r = n.shadowRoot.querySelector(`#${N}-modal`);
    r && r !== e && ((!t && r.contains(e)) || (r.style.display = "none"));
  }
  function eL(e, t, n = "sameLang", r, a, i, o) {
    let s = ye.bind(null, e.config.interfaceLanguage),
      l = t;
    n == "sameLang" && (l = s("sameLangNoTranslate")),
      (a.innerText = l),
      (i.innerText = s("neverShowFuture")),
      n == "sameLang" &&
        ((i.style.display = "flex"),
        (i.onclick = async () => {
          let u = await un();
          Yt({ ...u, sameLangCheck: !1 }), o(i, !0);
        }));
  }
  var tL = 0;
  async function nL(e) {
    let t = Date.now();
    if (t - tL < 2e3 || ((tL = t), cX())) return;
    let n = `${N}-toast-root`,
      r = `${N}-toast`,
      a = document.getElementById(n),
      i = `${N}-toast-msg`,
      o = `${N}-toast-content`,
      s = `${N}-toast-hidden`,
      l,
      u,
      c = () => {
        u && k9(u, !0);
      },
      d,
      m;
    if (a)
      (m = a.shadowRoot.querySelector(`.${r}`)),
        (l = m.querySelector(`.${i}`)),
        (u = m.querySelector(`.${s}`)),
        (l.innerHTML = ""),
        e(m, l, u, k9);
    else {
      (a = document.createElement("div")),
        a.setAttribute("translate", "no"),
        (a.className = `no-translate ${N}-toast-shadow-root`),
        (a.id = n),
        (a.style.all = "initial"),
        (a.style.zIndex = "2147483647"),
        document.body.appendChild(a);
      let h = a.attachShadow({ mode: "open" }),
        x = document.createElement("style"),
        f = Le();
      (x.textContent = f.IMMERSIVE_TRANSLATE_INPUT_INJECTED_CSS),
        h.appendChild(x),
        (m = document.createElement("div")),
        (m.className = r + " notranslate"),
        (m.id = r);
      let y = document.createElement("div");
      (y.className = o),
        m.appendChild(y),
        (l = document.createElement("div")),
        (l.className = i + " notranslate"),
        y.appendChild(l),
        (u = document.createElement("div")),
        (u.className = s + " notranslate"),
        (u.style.display = "none"),
        y.appendChild(u),
        h.appendChild(m);
      let b = document.createElement("span");
      (b.textContent = "\xD7"),
        (b.className = N + "-toast-close"),
        m.appendChild(b),
        (b.onclick = function () {
          m.style.display = "none";
        }),
        h.addEventListener("click", (v) => {
          v.target == m && (m.style.display = "none");
        }),
        e(m, l, u, k9);
    }
    let p = () => {
        clearTimeout(d);
      },
      g = () => {
        clearTimeout(d), (d = setTimeout(c, 2500));
      };
    m.addEventListener("mouseenter", p),
      m.addEventListener("mouseleave", g),
      setTimeout(() => {
        dX();
      }, 100),
      (d = setTimeout(c, 2500));
  }
  function cX() {
    let e = document.querySelector(`#${N}-toast-root`);
    return e && e.shadowRoot
      ? e.shadowRoot.querySelector(`#${N}-toast`)?.style.display == "flex"
      : !1;
  }
  function dX() {
    let e = document.querySelector(`#${N}-toast-root`);
    if (e && ((e.style.display = "flex"), e.shadowRoot)) {
      let t = e.shadowRoot.querySelector(`#${N}-toast`);
      t && (t.style.display = "flex");
    }
  }
  function k9(e, t = !1) {
    let n = document.querySelector(`#${N}-toast-root`);
    if (!n || n.style.display == "none") return;
    let r = n.shadowRoot.querySelector(`#${N}-toast`);
    r && r !== e && ((!t && r.contains(e)) || (r.style.display = "none"));
  }
  function M9(e, t, n, r = "retry", a, i, o, s, l, u) {
    let c = ye.bind(null, e.config.interfaceLanguage),
      d = t || c("errorModalTitle"),
      m = c("unknownError");
    (o.innerText = d),
      (s.innerHTML = st.sanitize(n || m, { ADD_ATTR: ["target"] })),
      (l.innerText = "");
    let p = "",
      g = null,
      h = document.createElement("button");
    if (r == "retry") {
      (p = c("retryAllButton")), h.setAttribute(`data-${N}-action`, "retry");
      let x = document.createElementNS("http://www.w3.org/2000/svg", "svg");
      x.setAttribute("viewBox", "0 0 16 16"),
        x.setAttribute("width", "20"),
        x.setAttribute("height", "20"),
        (x.innerHTML =
          '<path fill-rule="evenodd" clip-rule="evenodd" d="M15.7216 7.60092C15.9278 7.88015 15.7216 8.26938 15.2147 8.26938H13.6769C13.4509 8.26919 13.2651 8.44472 13.2559 8.66707C13.0202 11.4434 10.6721 13.5835 7.8434 13.6001C7.45033 13.6011 7.05839 13.5585 6.67498 13.4732C8.97932 12.9575 10.6873 11.0433 10.9105 8.7263C10.9201 8.60835 10.8791 8.49183 10.7975 8.40501C10.7159 8.31818 10.6011 8.269 10.4809 8.26938H9.46715C9.30707 8.26745 9.16079 8.17968 9.08563 8.04045C9.01048 7.90122 9.01841 7.7327 9.10632 7.60092L12.0617 3.4294C12.1415 3.31245 12.2752 3.2423 12.4183 3.2423C12.5613 3.2423 12.695 3.31245 12.7748 3.4294L15.7216 7.60092ZM0.249983 5.95687C0.32362 5.8189 0.468322 5.73198 0.62658 5.73066H2.34484C2.58058 5.74047 2.78188 5.56466 2.80018 5.33297C3.02618 2.57214 5.34909 0.434648 8.16115 0.399919C8.55422 0.398937 8.94616 0.441512 9.32956 0.526841C7.02244 1.04356 5.31373 2.96199 5.09406 5.2822C5.08428 5.39939 5.12548 5.51515 5.2074 5.60068C5.28932 5.68621 5.40427 5.73346 5.52362 5.73066H6.55458C6.71404 5.72904 6.86074 5.8163 6.9335 5.95606C7.00627 6.09581 6.99262 6.26407 6.89823 6.39066L3.93423 10.5791C3.85344 10.689 3.72404 10.7541 3.58628 10.7541C3.44852 10.7541 3.31912 10.689 3.23833 10.5791L0.274337 6.39066C0.185703 6.26152 0.176346 6.09485 0.249983 5.95687Z" fill="white"/>'),
        h.appendChild(x),
        (h.onclick = () => {
          (i.style.display = "none"),
            rL({ method: "retryFailedParagraphs" }),
            u(h, !0);
        });
    } else if (r == "login")
      Ca(e, "error_modal"),
        (p = c("goLogin")),
        (h.onclick = () => {
          u(h, !0), globalThis.open(av);
        });
    else if (r == "upgrade")
      Ca(e, "error_modal"),
        (p = c("upgradeToPro")),
        (h.onclick = () => {
          u(h, !0), globalThis.open(sv);
        });
    else if (r == "loginOrUpgradeByDownloadSubtitle")
      Ca(e, "subtitle_download"),
        (p = c("upgradeToProWithProfile")),
        (h.onclick = () => {
          u(h, !0), globalThis.open(uv);
        });
    else if (r == "loginOrUpgradeByAiSubtitle")
      Ca(e, "error_modal_ai_subtitle"),
        (p = c("upgradeToProWithProfile")),
        (h.onclick = () => {
          u(h, !0), globalThis.open(cv);
        });
    else if (r == "setting")
      (p = c("goSettings")),
        (h.onclick = () => {
          u(h, !0), globalThis.open(Ae.OPTIONS_URL);
        });
    else if (r == "changeService" || r == "changeProService") {
      n.includes("utm_campaign=service_error") && Ca(e, "service_error");
      let x = e.rule.detectionServiceOrder;
      r == "changeProService" && x && (x = [...nm, ...x]),
        (p = c("detectServiceLoading")),
        (h.onclick = () => {
          u(h, !0), globalThis.open(Ae.OPTIONS_URL);
        }),
        rd(e, e.translationService, !1, x).then((f) => {
          f
            ? ((p = c("toggleToService", { service: Pa(e.config, f) })),
              (h.innerHTML = st.sanitize(p)),
              (h.onclick = () => {
                aL(e.config, f), u(h, !0);
              }))
            : ((p = c("goSettings")), (h.innerHTML = st.sanitize(p)));
        });
    } else if (r == "autoEnableSubtitle")
      (p = c("subtitle.enableDualSubtitleActionLabel")),
        (h.onclick = async () => {
          let x = await jt();
          x.generalRule || (x.generalRule = {}),
            x.generalRule["subtitleRule.add"] ||
              (x.generalRule["subtitleRule.add"] = {}),
            (x.generalRule["subtitleRule.add"].autoEnableSubtitle = !0),
            await Yt(x),
            rL({
              method: "autoEnableSubtitleChanged",
              data: { trigger: "quick_button" },
            }),
            u(h, !0);
        });
    else if (r == "refreshPage")
      (p = c("refreshPage")),
        (h.onclick = () => {
          window.location.reload();
        });
    else if (r == "usageTips") {
      (p = c("iKnow")),
        (g = document.createElement("div")),
        (g.className = N + "-btn-container"),
        g.appendChild(h),
        (h.onclick = () => {
          u(h, !0);
        });
      let x = document.createElement("button");
      (x.className = N + "-link-btn"),
        (x.innerHTML = c("notShowAgain")),
        (x.onclick = async () => {
          u(h, !0);
          let f = await jt();
          await Yt({
            ...f,
            tokenUsageTips: { ...f.tokenUsageTips, enableMaxTips: !1 },
          });
        }),
        g.appendChild(x);
    } else if (r == "none") return;
    (h.className = N + "-btn"),
      (h.innerHTML += st.sanitize(p)),
      g ? l.appendChild(g) : l.appendChild(h);
  }
  function rL(e) {
    qa(e, { tab: { id: 1, url: "https://www.fake.com", active: !0 } }).catch(
      (n) => {
        I.error("send content message request failed", e, n);
      }
    );
    let t = new CustomEvent(ra, { detail: e });
    globalThis.document.dispatchEvent(t);
  }
  var Wu = {};
  function iL(e, t, n) {
    return `${e}_${t}_${n}`;
  }
  async function oL(e, t) {
    let { sourceImageUrl: n, targetLanguage: r, mode: a } = t;
    try {
      let { blob: i, imgHash: o } = await lL(n);
      if (await bh(i)) throw new Ui("GIFs not supported", "gif_not_supported");
      if (!o) throw new Error("Failed to calculate image hash");
      let u = Nu({ sourceUrl: n, to: r });
      if (u?.targetUrl) return { translatedImageUrl: u.targetUrl, mode: a };
      let c,
        d,
        m = { ...e, targetLanguage: r },
        p = { sourceUrl: n, to: r, imgHash: o, siteUrl: location.href },
        g = iL(a, o, r);
      Wu[g] ||
        (Wu[g] = {
          client: { state: "extension_uploading" },
          pro: { state: "extension_uploading" },
        });
      try {
        a === "client"
          ? ((m.isPro = !1),
            (c = await vh({
              context: m,
              sourceUrl: o,
              imgEle: document.createElement("img"),
              blob: i,
              imgHash: o,
              onStateChange: (h) => {
                Wu[g][a] = { state: h };
              },
              imageDb: p,
            })))
          : a === "pro" &&
            (c = await td({
              context: m,
              sourceUrl: o,
              blob: i,
              imgHash: o,
              imageType: "common",
              onStateChange: (h) => {
                Wu[g][a] = { state: h };
              },
            })),
          c && a === "pro" && ri({ ...p, targetUrl: c });
      } catch (h) {
        let x =
          a === "client" ? "Free translation failed" : "Pro translation failed";
        (d = h.message || x), (Wu[g][a].error = d);
      }
      return { translatedImageUrl: c, mode: a, error: d };
    } catch (i) {
      return {
        translatedImageUrl: "",
        mode: a,
        error: i.message || "Unknown error",
      };
    }
  }
  async function sL(e, t) {
    let { sourceImageUrl: n, targetLanguage: r, mode: a } = t,
      { imgHash: i } = await lL(n),
      o = ye.bind(null, e.config.interfaceLanguage),
      s = iL(a, i, r),
      l = Wu[s],
      u = { state: "extension_uploading", error: void 0 },
      c = l?.[a] || u;
    return { state: Iy(c.state, o), mode: a, error: c.error };
  }
  async function lL(e) {
    let t = await fetch(e);
    await za(t);
    let n = await t.blob(),
      r = await m0(n);
    return { blob: n, imgHash: r };
  }
  var uL = !1,
    Ut = {
      SetupDomListenersForOnce: () => {},
      handleSecurityPolicyViolation: () => {},
      showErrorByEvent: () => {},
      showToastByEvent: () => {},
      showModal: () => {},
      handleEbookLoaded: () => {},
      handleThirdPartyTell: () => {},
      eventUpload: () => {},
      handleClick: () => {},
      handlePopupInit: () => {},
      handleGlobalMessage: () => {},
      handleTokenUsageChange: () => {},
    },
    mL = [
      () => {
        $t.unbind();
      },
    ],
    cL = [...mL],
    mX = tr(async (e) => {
      await gr(!1, "", e);
    }, 50),
    pX = tr(async () => {
      await Gp();
    }, 50),
    gX = tr(async () => {
      await yw();
    }, 50),
    hX = tr(async () => {
      await bw();
    }, 50),
    pL = tr((e) => {
      dn({ method: e, data: { trigger: "userscript_menu" } });
    }, 50),
    D9 = !1,
    fX = qt(() => {
      let e = globalThis.getSelection()?.toString().trim();
      !D9 && e && e.length > 0 ? Ps({ text: e }) : Ps({});
    }, 50),
    bX = () => {
      (D9 = !0),
        setTimeout(() => {
          D9 = !1;
        }, 100);
    };
  Ut.SetupDomListenersForOnce = (e, t) => {
    let n = e.document;
    if (uL) return;
    (uL = !0),
      J() || ow(),
      n.addEventListener(
        "securitypolicyviolation",
        Ut.handleSecurityPolicyViolation
      ),
      document.addEventListener(ie + "DocumentMessageUser", bL),
      document.addEventListener(ie + "DocumentMessageUpdateUser", yL),
      document.addEventListener(ie + "ChangeSuccessService", vL.bind(null, t)),
      document.addEventListener(ie + "ChangeService", xL.bind(null, t)),
      document.addEventListener(Tn, Ut.showErrorByEvent.bind(null, t)),
      document.addEventListener(Fd, Ut.handleTokenUsageChange.bind(null, t)),
      document.addEventListener(Rd, Ut.showToastByEvent.bind(null, t)),
      document.addEventListener(cr, Ut.showModal.bind(null, t)),
      n.addEventListener(ie + "EbookLoaded", Ut.handleEbookLoaded),
      n.addEventListener(F6, Ut.handleThirdPartyTell.bind(null, t)),
      n.addEventListener(B6, Ut.eventUpload.bind(null, t)),
      n.addEventListener(_d, (a) => {
        tt() || Gb(a, t);
      }),
      n.addEventListener(N6, Rk),
      n.addEventListener("click", (a) => {
        Ut.handleClick(a, t);
      }),
      document.addEventListener("selectionchange", fX),
      document.addEventListener("contextmenu", bX),
      XP(),
      tt() && e.addEventListener("message", Ut.handleGlobalMessage, !1),
      J() && (tt() || n.addEventListener(ra, Ut.handlePopupInit)),
      tt() || Do.rootIframe(Vd).handleAsk("throttleRequest", d7);
  };
  function yd(e, t) {
    xX(),
      Ut.SetupDomListenersForOnce(t, e),
      J() && (tt() || (Vb(e.config), CX(e.config))),
      Cr(e.localConfig || {}) && zI(e, t),
      pt() && Vb(e.config);
  }
  Ut.handlePopupInit = (e) => {
    VP();
  };
  Ut.handleGlobalMessage = (e) => {
    let t = e;
    t &&
      t.data &&
      t.data.payload &&
      t.data.author === Ld &&
      qa(t.data.payload, {
        tab: { id: 1, url: "https://www.fake-iframe.com", active: !0 },
      });
  };
  Ut.handleClick = (e, t) => {
    let n = e.target;
    if (!n || !n.getAttribute) {
      bd(n);
      return;
    }
    let r = n.getAttribute(`data-${N}-event`);
    r && Te({ key: r, ctx: { ...t, sourceLanguage: "none" } });
    let a = n.getAttribute(`data-${N}-action`);
    if (a) {
      if ((e.preventDefault(), a === "retry")) {
        typeof e.stopPropagation == "function" && e.stopPropagation(),
          gL({ method: "retryFailedParagraphs" }),
          bd(n, !0);
        return;
      } else if (a == "toast-error") {
        let i = n.getAttribute(`data-${N}-tooltip-text`) || "",
          o = "",
          s = "retry",
          l = t.translationService;
        try {
          let u = JSON.parse(i);
          (o = u.title),
            (i = u.errMsg),
            (s = u.action),
            (l = u.translationService);
        } catch {}
        Ys(M9.bind(null, t, o, i, s, l));
        return;
      }
    }
  };
  async function yX() {
    let e = await jt(),
      t =
        e.enableDefaultAlwaysTranslatedUrls === void 0
          ? !0
          : e.enableDefaultAlwaysTranslatedUrls;
    if (
      ((e.enableDefaultAlwaysTranslatedUrls = !t),
      !e.enableDefaultAlwaysTranslatedUrls)
    ) {
      let n = e && e.isChangedAlwaysTranslatedUrls,
        r = [];
      e.translationUrlPattern &&
        e.translationUrlPattern.matches &&
        (r = e.translationUrlPattern.matches || []),
        !n &&
          r.length > 0 &&
          (e.translationUrlPattern = { matches: [], excludeMatches: [] });
    }
    await Yt(e);
  }
  async function vX() {
    let e = await jt(),
      t = e.enableInputTranslation;
    (e.enableInputTranslation = !t), await Yt(e);
  }
  Ut.showToastByEvent = (e, t) => {
    let n = t;
    if (n?.detail)
      try {
        let r = n.detail;
        if (!r || !r.type) return;
        let { msg: a, type: i } = r;
        nL(eL.bind(null, e, a, i));
      } catch (r) {
        I.warn("parse message error", r);
      }
  };
  Ut.showErrorByEvent = (e, t) => {
    let n = t;
    if (n?.detail)
      try {
        let r = n.detail;
        if (!r || !r.type) return;
        let { title: a, errMsg: i, action: o, translationService: s } = r;
        Ys(M9.bind(null, e, a, i, o, s));
      } catch (r) {
        I.warn("parse message error", r);
      }
  };
  Ut.showModal = (e, t) => {
    let n = t;
    if (!n?.detail) return;
    let r = n.detail.type;
    r == "mangaReport" || r == "imageReport"
      ? Ys(Nh.bind(null, r.replace("Report", ""), e))
      : r == "webReport" || r == "docReport"
      ? Ys(Nh.bind(null, r === "webReport" ? "web" : "docTranslationForWeb", e))
      : r == "clientImageNotSupport"
      ? Ys(QD.bind(null, e))
      : r == "clientImageGuide" && Ys(YD.bind(null, e));
  };
  Ut.handleThirdPartyTell = async (e, t) => {
    let n = t;
    I.debug("receive third party message", n);
    let r = id() || e;
    if (n && n.detail) {
      let a;
      try {
        let i = JSON.parse(n.detail);
        if (i && i.type) {
          if (i.type === "retryFailedParagraphs")
            gL({ method: "retryFailedParagraphs" });
          else if (i.type === "updateCommands") Vp(i.data);
          else if (i.type === "toggleEnableDefaultAlwaysTranslatedUrls") yX();
          else if (i.type === "toggleEnableInputTranslation") vX();
          else if (i.type === "syncAppSetting") hL(r, i.data);
          else if (i.type === "translatePage") SX(r, i.data);
          else if (i.type === "getAsyncTranslationServiceList")
            a = ep(r, "translationService");
          else if (i.type === "getAsyncTargetLanguageList")
            a = mg(
              i.data?.translationService,
              i.data?.targetLanguage,
              r.config
            );
          else if (i.type === "getAsyncTranslationMeta") {
            let o = r.state.translationService || r.translationService;
            a = {
              targetLanguage: r.state.targetLanguage || r.targetLanguage,
              translationService: o,
              translationMode: r.state.translationMode,
            };
          } else if (i.type === "verifyServiceConnection") {
            let o = await $e(Ie(), {}, !0),
              s = {
                ...o,
                state: { ...o.state, cache: !1 },
                translationService: i.data.service,
              },
              l = { retry: 1, ...(i.data.serviceConfig || {}) };
            try {
              a = await jh(async () => await dL(i.data, s, l), r);
            } catch (u) {
              Ku(i.type, u.uiConfig(s), i.id);
            }
          } else if (i.type == "getAsyncTranslateContent") {
            let o = { ...r, translationService: i.data.service },
              s = { ...(i.data.serviceConfig || {}) };
            try {
              a = await jh(async () => {
                let l = await jp(r, i.data.service);
                return (s.contextTerms = l), await dL(i.data, o, s);
              }, r);
            } catch (l) {
              Ku(i.type, l.uiConfig(o), i.id);
            }
          } else if (i.type == "getAsyncTranslateMultipleResults") {
            let o = { ...r, translationService: i.data.service };
            try {
              a = await TX(o, i.data);
            } catch (s) {
              Ku(i.type, { status: "error", error: s.uiConfig(o) }, i.id);
            }
          } else if (i.type === "getAsyncLanguageByText") a = await qp(i.data);
          else if (i.type === "getAsyncDownloadSubtitle") a = await sA();
          else if (i.type === "sharePage") Gb({ detail: i.data }, r);
          else if (i.type === "switchTranslationMode") I9(i.data);
          else if (i.type == "getAsyncAiAssistants") a = await S7();
          else if (i.type == "addAiAssistantAsync") a = await ng("add", i.data);
          else if (i.type == "removeAiAssistantAsync")
            a = await ng("remove", i.data);
          else if (i.type == "getMiniConfigAsync" || i.type == "getConfig")
            a = await L9(r);
          else if (i.type == "setMiniConfigAsync" || i.type == "setConfig")
            a = await P9(r, i.data);
          else if (i.type == "getDeviceInfoAsync")
            r.rule.allowInnerInvoke && (a = await jl("auto", r.targetLanguage));
          else if (i.type == "setABGroupAsync")
            r.rule.allowInnerInvoke && (a = await A8(i.data));
          else if (i.type === "getIsSupportIsOnToolbarAsync")
            r.rule.allowInnerInvoke && (a = await Cw());
          else if (i.type === "getIsOnToolbarAsync")
            r.rule.allowInnerInvoke && (a = await Sw());
          else if (i.type === "setCampaignAsync")
            r.rule.allowInnerInvoke && (a = await k8(i.data));
          else if (i.type === "openPopup") Bh({ ...i.data, open: !0 });
          else if (i.type === "closePopup") Bh({ ...i.data, open: !1 });
          else if (i.type === "togglePopup") Bh({ ...i.data });
          else if (i.type === "getPageStatusAsync") a = Ye();
          else if (i.type === "restorePage") wX();
          else if (i.type === "getPageLanguageAsync") a = rn();
          else if (i.type === "showFloatBallGuide") MP();
          else if (i.type === "showPopupModalGuide") DP();
          else if (i.type == "getAsyncDetectAndSetLanguage")
            a = await fL(i.data);
          else if (i.type == "setFloatBallActive") FP(i.data);
          else if (i.type == "requestTermsByContext") {
            let { text: o, translationService: s } = i.data;
            Is(r, s, o, !0);
          } else if (i.type == "requestTermsContextByServices") {
            let { text: o, services: s } = i.data;
            await Np(r, o, s);
          } else if (i.type == "getAsyncTranslateImageByUrl")
            a = await oL(r, i.data);
          else if (i.type == "getAsyncTranslateImageProgress")
            a = await sL(r, i.data);
          else if (i.type == "triggerTranslateImageBySrc") uI(r, i.data);
          else if (i.type == "cleanTranslateImageBySrc") cI(i.data);
          else if (i.type == "openImageTranslationFeedback")
            tt() ||
              document.dispatchEvent(
                new CustomEvent(cr, { detail: { type: "imageReport" } })
              );
          else if (i.type == "getAsyncUserInfo")
            globalThis.location.hostname.endsWith("immersivetranslate.com") &&
              (a = await et.get(gt, null));
          else if (i.type == "getAsyncAllTerms")
            a = await jh(async () => await oP(r.config), r);
          else if (i.type == "getAsyncGlossaries")
            a = await jh(
              async () => await sP(r.config, i.data.targetLanguage, i.data.ids),
              r
            );
          else if (i.type == "openWebTranslationFeedback")
            tt() ||
              document.dispatchEvent(
                new CustomEvent(cr, { detail: { type: "webReport" } })
              );
          else if (i.type == "collectAnalytics") {
            let o = i.data || {},
              s = o.ignoreGA || !1,
              l = o.params || {};
            Te({ key: o.type, ctx: r, params: l, forceDaily: !1, ignoreGA: s });
          } else pL(i.type);
          a !== void 0 && i.id && Ku(i.type, a, i.id);
        }
      } catch (i) {
        I.warn("parse message error", i);
      }
    }
  };
  Ut.handleEbookLoaded = (e) => {
    setTimeout(() => {
      zh();
    }, 10);
  };
  Ut.handleSecurityPolicyViolation = (e) => {
    Ae.HAS_CSP_ERROR = "1";
  };
  Ut.eventUpload = (e, t) => {
    let n = t.detail;
    (n.name == "open_pdf_page" || n.name === "open_html_page") &&
      (Te({ key: n.name, ctx: e }), kr("translate_pdf_1", e));
  };
  var Oh = !1;
  Ut.handleTokenUsageChange = qt(async (e, t) => {
    if (Oh) return;
    let n = t,
      r = e.user?.subscription,
      a = e.config;
    if (
      !r ||
      !e.isMax ||
      r.isTrial ||
      !n?.detail ||
      !e.config.tokenUsageTips?.enableMaxTips ||
      !r.maxAIQuota ||
      !r.maxAIUsedCountResetTime
    )
      return;
    let o = n.detail.aiUsage;
    if (!o?.maxAIUsedQuota || r.maxAIQuota * 0.5 >= o.maxAIUsedQuota) return;
    let s = new Date(r.maxAIUsedCountResetTime);
    if (
      a.tokenUsageTips?.resetTime &&
      new Date(a.tokenUsageTips?.resetTime).getTime() === s.getTime()
    ) {
      Oh = !0;
      return;
    }
    let l = await jt();
    if (
      l?.tokenUsageTips?.resetTime &&
      new Date(l.tokenUsageTips.resetTime).getTime() === s.getTime()
    ) {
      Oh = !0;
      return;
    }
    (Oh = !0),
      Yt({
        ...l,
        tokenUsageTips: {
          ...l?.tokenUsageTips,
          resetTime: r.maxAIUsedCountResetTime,
        },
      }),
      document.dispatchEvent(
        new CustomEvent(Tn, {
          detail: {
            title: ye(a.interfaceLanguage, "error.usageTips"),
            errMsg: ye(a.interfaceLanguage, "error.maxQuotaUsageTips", {
              maxAIQuota: r.maxAIQuota.toLocaleString("en-US"),
              maxAIUsed: "50%",
              1: mv,
            }),
            action: "usageTips",
            type: "info",
          },
        })
      );
  }, 1e3);
  function xX() {
    cL.forEach((e) => {
      e();
    }),
      (cL = mL);
  }
  function CX(e) {
    if (J() && typeof GM < "u" && GM && GM.registerMenuCommand) {
      let t = zm.commands,
        a = [
          ...Object.keys(t)
            .filter((i) => i === "toggleTranslatePage")
            .map((i) => {
              let o = t[i].description,
                s = o;
              return (
                o.startsWith("__MSG_") &&
                  o.endsWith("__") &&
                  (s = ye(e.interfaceLanguage, `browser.${o.slice(6, -2)}`)),
                { id: i, title: s }
              );
            }),
          {
            id: Af,
            title: ye(e.interfaceLanguage, "browser.openEbookViewer"),
            key: "e",
          },
          {
            id: kf,
            title: ye(e.interfaceLanguage, "browser.openEbookBuilder"),
            key: "m",
          },
          {
            id: wf,
            title: ye(e.interfaceLanguage, "browser.openOptionsPage"),
            key: "o",
          },
          {
            id: Ef,
            title: ye(e.interfaceLanguage, "browser.openAboutPage"),
            key: "a",
          },
        ];
      for (let i of a)
        GM.registerMenuCommand(
          i.title,
          () => {
            i.id === wf
              ? mX(e.useOnlineOptions)
              : i.id === Ef
              ? pX()
              : i.id === kf
              ? gX()
              : i.id === Af
              ? hX()
              : pL(i.id);
          },
          i.key
        );
    }
  }
  function gL(e) {
    qa(e, { tab: { id: 1, url: "https://www.fake.com", active: !0 } }).catch(
      (n) => {
        I.error("send content message request failed", e, n);
      }
    );
    let t = new CustomEvent(ra, { detail: e });
    globalThis.document.dispatchEvent(t);
  }
  async function hL(e, t) {
    let n = {};
    return (
      t?.translationMode && (n.translationMode = t.translationMode),
      t?.translationService &&
        (n.translationService = b5(e.config, t.translationService)),
      t?.targetLanguage && (n.targetLanguage = t.targetLanguage),
      t?.translationStartMode &&
        (n.translationStartMode = t.translationStartMode),
      await $e(Ie(), n)
    );
  }
  async function SX(e, t) {
    let n = await hL(e, t);
    yr(n);
  }
  function Ku(e, t, n) {
    globalThis.document.dispatchEvent(
      new CustomEvent(Jt, {
        detail: JSON.stringify({ id: n, type: e, payload: t }),
      })
    );
  }
  async function jh(e, t) {
    if (!t.rule.id?.includes("immersive"))
      throw new Y(
        "immersive",
        "not in immersive environment, can't invoke translate"
      );
    return await e();
  }
  async function dL({ textList: e, from: t, to: n, service: r }, a, i) {
    let o = e.map((l) => ({
        text: l || "",
        id: 0,
        from: t,
        to: n,
        url: a.url,
        fromByClient: t,
        force: !0,
        inArticleContext: !0,
      })),
      s = null;
    try {
      let l = await Gt(
        { sentences: o },
        { ...a, isRichTranslate: !1, translationService: r },
        (u) => (s = u),
        i
      );
      if (s) throw s;
      return l;
    } catch (l) {
      throw l;
    }
  }
  async function TX(e, t) {
    let { textList: n, from: r, to: a, service: i, sourceProgram: o } = t;
    if (!e.rule.allowInnerInvoke) return;
    let s = n.map((l, u) => ({
      text: l || "",
      id: u,
      from: r,
      to: a,
      url: e.url,
      fromByClient: r,
      force: !0,
      inArticleContext: !0,
    }));
    try {
      let l = [],
        u = { ...e, isRichTranslate: !1, translationService: i };
      return (
        o && (u.sourceProgram = o),
        await Gt({ sentences: s }, u, (c, d, m) => {
          let p = null;
          c &&
            (p = {
              name: c?.name,
              message: c?.message,
              status: c instanceof Y ? c.status : "error",
            }),
            (l[m.id] = {
              error: p,
              errorUIConfig: c instanceof Y ? c?.uiConfig(e) : null,
              sentence: d,
              sentenceRequest: m,
            });
        }),
        { status: "success", data: l.map((c) => c) }
      );
    } catch (l) {
      throw l;
    }
  }
  function wX() {
    Ye() !== "Original" && Ea();
  }
  var Zs = {};
  async function R9() {
    let t = [...document.querySelectorAll(".source-text")]
        .map((r) => r.textContent)
        .join(""),
      n = await je({ text: t, minLength: 200 });
    return Mr(n), n;
  }
  async function CL(e) {
    let t = 0,
      n = 0;
    kn("Translating");
    let r = rn(),
      a = Fp(),
      i = [...document.querySelectorAll(".source-text")];
    if (Nl(e, r)) return;
    let o = i
      .filter((l) => l.innerText?.trim())
      .map((l, u) => ({
        text: l.innerText || "",
        id: u,
        from: r,
        to: e.targetLanguage,
        url: "https://google.com",
        fromByClient: a,
        force: !0,
        inArticleContext: !0,
        node: l,
      }));
    document.dispatchEvent(
      new CustomEvent(Jt, {
        detail: JSON.stringify({
          type: "totalParagraphsCount",
          payload: { totalParagraphsCount: o.length },
        }),
      })
    );
    try {
      let l = o,
        u = a0(e, "", !0);
      for (; l.length; ) {
        let c = l.slice(0, 100);
        (l = l.slice(100)),
          c.forEach((d) => {
            let m =
              d.node.parentElement?.parentElement?.querySelector(
                ".target-text"
              );
            if ((Lt(d.node, "id", d.id + ""), m)) {
              Ol(m, d.id)?.remove();
              let p = u.cloneNode(!0);
              (p.id = d.id + ""), m.appendChild(p);
            }
          }),
          await s(c),
          await We(0);
      }
    } catch (l) {
      throw l;
    } finally {
      (t = 0), (n = 0), kn("Translated");
    }
    async function s(l) {
      await Gt(
        { sentences: l },
        { ...e, sourceProgram: "subtitle" },
        (u, c, d) => {
          n += 1;
          let m = d.id,
            p = Ol(document.body, m);
          if (!p) return;
          let g = p.parentElement;
          g &&
            (p.remove(),
            u
              ? ((t += 1),
                (g.innerHTML = st.sanitize(
                  `<span id="error-id-${m}">${u.message}</span>`
                )),
                (Zs[m] = { ok: !1, sentence: d }),
                TL(l, e, t, n, u))
              : c &&
                ((g.innerHTML = st.sanitize(c.text)),
                (Zs[m] = { ok: !0, sentence: d })),
            document.dispatchEvent(
              new CustomEvent(Jt, {
                detail: JSON.stringify({
                  type: "paragraphTranslated",
                  payload: { ok: !u },
                }),
              })
            ));
        }
      );
    }
  }
  async function SL(e) {
    let t = 0,
      n = 0;
    kn("Translating"),
      document.dispatchEvent(
        new CustomEvent(Jt, {
          detail: JSON.stringify({
            type: "retryFailedParagraphsStart",
            payload: {},
          }),
        })
      );
    let r = Object.keys(Zs),
      a = [],
      i = [],
      o = a0(e, "");
    for (let s of r) {
      let l = Zs[s];
      if (!l.ok) {
        let c = document.querySelector(`#error-id-${s}`);
        if (c) {
          let d = c.parentElement;
          if ((c.remove(), d)) {
            delete Zs[s];
            let m = o.cloneNode(!0);
            (m.id = s), d.appendChild(m), i.push(l.sentence);
          }
        }
      }
    }
    try {
      await Gt({ sentences: i }, e, (s, l, u) => {
        n += 1;
        let c = u.id,
          d = Ol(document.body, c);
        if (!d) return;
        let m = d.parentElement;
        m &&
          (d.remove(),
          s
            ? ((t += 1),
              (m.innerHTML = st.sanitize(
                `<span id="error-id-${c}">${s.message}</span>`
              )),
              (Zs[c] = { ok: !1, sentence: u }),
              TL(i, e, t, n, s))
            : l &&
              ((m.innerHTML = st.sanitize(l.text)),
              (Zs[c] = { ok: !0, sentence: u })),
          document.dispatchEvent(
            new CustomEvent(Jt, {
              detail: JSON.stringify({
                type: "paragraphTranslated",
                payload: { ok: !s },
              }),
            })
          ));
      });
    } catch (s) {
      throw s;
    } finally {
      (t = 0), (n = 0), kn("Translated");
    }
  }
  function F9() {
    kn("Original"),
      document.querySelectorAll(".target-text").forEach((e) => {
        e.innerHTML = "";
      });
  }
  function TL(e, t, n, r, a) {
    if (!(a instanceof Y)) return;
    let i = a.uiConfig(t);
    i.action == "retry" && (i.action = "none"),
      (n === t.rule.toastErrorMinTimes || r === e.length) &&
        document.dispatchEvent(new CustomEvent(Tn, { detail: i }));
  }
  var Uh = {};
  async function wL() {
    let e = [...document.querySelectorAll("iframe")],
      t = "";
    for (let r of e) {
      let a = r.contentDocument;
      if (!a) continue;
      let i = a.body;
      if (i && ((t = t + i.innerText || ""), t.length > 1e3)) break;
    }
    let n = await je({ text: t });
    return Mr(n), n;
  }
  async function EL(e) {
    kn("Translating"),
      document.dispatchEvent(
        new CustomEvent(Jt, {
          detail: JSON.stringify({
            type: "retryFailedParagraphsStart",
            payload: {},
          }),
        })
      );
    let t = Object.keys(Uh),
      n = [];
    for (let r of t) {
      let a = Uh[r];
      if (a.error) {
        let o = a.commonAncestorContainer.querySelector(`[${Ud}='${r}']`);
        o && o.remove(), n.push(a);
      }
    }
    try {
      R0(),
        await I0({
          paragraphs: n,
          ctx: e,
          translationMode: e.state.translationMode || e.config.translationMode,
          isLongImmediateHtml: !1,
          inArticleContext: !0,
          userDiffContext: !1,
        });
    } catch {
    } finally {
      kn("Translated");
    }
  }
  function AL(e) {
    Object.values(Uh).forEach((t) => {
      let n = t.state,
        r = t.commonAncestorContainer,
        { targetNodes: a } = t;
      if (a)
        if (n === "translation") {
          let i = hu(e, t.targetNodes);
          Fo(e, i, r, t.rootNodes || []), Bs(t.targetNodes);
        } else n === "dual" && Bs(t.targetNodes);
    }),
      kn("Original");
  }
  async function kL(e) {
    So(!1), kn("Translating");
    let t = Zc(),
      n = Vr(e.config.translationServices[e.translationService]),
      r = [...document.querySelectorAll("iframe")],
      a = 0,
      i = 1;
    for (let l of r) {
      let u = l.contentDocument;
      if (!u) continue;
      gu(u, e);
      let c = u.body;
      if (!c) continue;
      Ua(u.documentElement, e.state.translationMode);
      let d = vn(e);
      d.isModifyImage = !1;
      let m = [];
      await Oi({
        id: t,
        container: c,
        filterRule: d,
        onParagraph: (g) => {
          m.push(g);
        },
      });
      let p = await s(m);
      (a += p), o(Math.max(a, 1e4)), await We(0);
    }
    o(a), kn("Translated");
    function o(l) {
      document.dispatchEvent(
        new CustomEvent(Jt, {
          detail: JSON.stringify({
            type: "totalParagraphsCount",
            payload: { totalParagraphsCount: l },
          }),
        })
      );
    }
    async function s(l) {
      let u = [],
        c = Rs(
          e,
          e.translationService,
          e.config.translationServices,
          e.specialAiAssistant
        );
      try {
        for (let d of l) {
          let m = hg(
            d.commonAncestorContainer,
            d.flatNodes,
            {
              isPreWhitespace: d.isPreWhitespace || !1,
              delimiters: n,
              stayOriginalSelectors: fg(e),
              ignoreRichSelectors: e.rule.ignoreRichSelectors,
              smallCodeLength: e.rule.smallCodeLength,
              domPurifyAddTags: e.rule.domPurifyAddTags,
              richMoreWordPercent: e.rule.richMoreWordPercent || 0,
              purifyRichHtml: e.rule.purifyRichHtml || !1,
            },
            e.config.translationServices?.[e.translationService],
            c
          );
          if (!m) continue;
          d.id = (i++).toString();
          let p = await K2(e, d, m, {
            excludeLanguages:
              e?.config?.translationLanguagePattern?.excludeMatches || [],
            isDetectParagraphLanguage:
              e.rule.detectParagraphLanguage ||
              e.state.isDetectParagraphLanguage,
            targetLanguage: e.targetLanguage,
            delimiters: n,
            noTranslateRegexp: e.rule.noTranslateRegexp,
            minTextCount: 2,
            minWordCount: 1,
            ignoreZhCNandZhTW: e.rule.ignoreZhCNandZhTW,
          });
          p && ((p.inArticleContext = !0), u.push(p), (Uh[p.id] = p));
        }
        I0({
          paragraphs: u,
          ctx: { ...e, isRichTranslate: c },
          translationMode: e.state.translationMode || e.config.translationMode,
          isLongImmediateHtml: !1,
          inArticleContext: !0,
          userDiffContext: !1,
        });
      } catch {}
      return u.length;
    }
  }
  function Hh() {
    return C("span", {
      class: "logo",
      dangerouslySetInnerHTML: { __html: js },
    });
  }
  async function qh({ text: e, ctx: t }) {
    let n = "en",
      r = rn();
    if (
      (t.sourceLanguage && t.sourceLanguage !== "auto"
        ? (n = t.sourceLanguage)
        : r !== "auto" && (n = r),
      !t.rule.enableServerDetectLanguage)
    )
      return n;
    let a = await je({ text: e });
    if (a && a !== "auto") return a;
    let i = await mu([e]);
    return !i || !i.length ? n : i[0].lang;
  }
  var Gh = async (e) => {
      if (navigator.clipboard && navigator.permissions)
        try {
          await navigator.clipboard.writeText(e);
        } catch {
          ML(e);
        }
      else ML(e);
    },
    ML = (e) => {
      let t = document.createElement("textArea");
      (t.value = e),
        (t.style.width = "0px"),
        (t.style.position = "fixed"),
        (t.style.left = "-999px"),
        (t.style.top = "10px"),
        t.setAttribute("readonly", "readonly"),
        document.body.appendChild(t),
        t.select(),
        document.execCommand("copy"),
        document.body.removeChild(t);
    },
    EX = { service: "gemini", model: "gemini-2.0-flash" },
    Vh = { service: "zhipu", model: "glm-4-flash" },
    Qu = (e) =>
      xt(e.user)
        ? e.rule.selectionTranslation?.proService || EX
        : e.rule.selectionTranslation?.commonService || Vh;
  function Yu() {
    let e = document.activeElement;
    return (
      e &&
      (e.tagName === "INPUT" || e.tagName === "TEXTAREA") &&
      "selectionStart" in e &&
      "selectionEnd" in e
    );
  }
  function DL(e) {
    if (!e) return !1;
    let t = e.commonAncestorContainer;
    return (
      t.nodeType === Node.TEXT_NODE && (t = t.parentElement),
      !!(
        t instanceof Element &&
        t.closest('[contenteditable="true"], [contenteditable=""]')
      )
    );
  }
  function IL({
    selectionRect: e,
    gap: t = 10,
    modalWidth: n = 450,
    modalHeight: r = 480,
  }) {
    let [a, i] = V(null),
      o = Me(() => {
        if (!e) return null;
        let s = globalThis.innerWidth,
          l = globalThis.innerHeight,
          u,
          c;
        (u = e.left), Yu() && (u = u + Math.max(0, (e.width - n) / 2));
        let m = PL();
        if (m?.x) u = m.x - n / 2;
        else {
          let x = LL();
          x?.x && (u = x.x - n / 2);
        }
        let p = u + n;
        p > s && (u -= p - s - t);
        let g = e.bottom + t;
        return (
          g + r <= l ? (c = g) : (c = l - r),
          (c = Math.max(0, c)),
          (u = Math.max(0, u)),
          { top: c, left: u }
        );
      }, [e, t, n, r]);
    return (
      Q(() => {
        if (a) return;
        let s = o();
        i(
          s
            ? {
                position: "fixed",
                top: `${s.top}px`,
                left: `${s.left}px`,
                display: "block",
              }
            : { display: "none" }
        );
      }, []),
      { style: a }
    );
  }
  var RL = `
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

  .${N}-modal {
    position: absolute;
    z-index: ${Ia - 1};
    display: block;
    width: 450px;
    box-sizing: border-box;
    background-color: white;
    border-radius: 16px;
    padding: 16px 20px;
    padding-top: 0;
    display: none;
    border-radius: 16px;
    box-shadow: 0px 10px 32px 0px var(--ty-8, rgba(0, 0, 0, 0.08));
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif, "PingFang SC", "Microsoft YaHei", "Apple Color Emoji", "Segoe UI Emoji";
  }

  .${N}-modal.dark {
    background-color: #111;
    box-shadow: 0px 10px 32px 0px rgba(255, 255, 255, 0.10);
  }

  .${N}-modal-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
    cursor: move;
  }

  .${N}-modal-body {
    box-sizing: border-box;
  }

  .translation-result-container {
    box-sizing: border-box;
    overflow-y: auto;
    max-height: 400px;
  }

  .${N}-close {
    cursor: pointer;
    font-size: 20px;
  }

  .${N}-modal-title-left {
    display: flex;
    align-items: center;
  }

  .${N}-modal-title-right {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .logo {
    width: 22px;
    height: 22px;
  }

  .word-dictionary {
    padding: 0 8px;
  }

  .word-original {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
  }

  .word-original-text {
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5;
    color: #222;
    user-select: none;
  }

  .dark .word-original-text {
    color: #DBDBDB;
  }

  .word-phonetic {
    color: #999;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    margin-left: 12px;
  }

  .dark .word-phonetic {
    color: #777;
  }

  .speaker-icon {
    margin-left: 6px;
  }

  .word-dictionary-item:not(:last-child) {
    margin-bottom: 16px;
  }

  .word-dictionary-definition {
    display: flex;
    align-items: self-start;
    gap: 8px;
  }

  .word-dictionary-pos {
    min-width: 24px;
    text-align: right;
    color: #666;
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 21px;
    word-wrap: break-word;
  }


  .word-dictionary-meaning {
    color: #333;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 1.5;
  }

  .word-example {
    padding-left: 32px;
    margin-top: 8px;
    color: #333;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
  }

  .dark .word-dictionary-pos, .dark .word-dictionary-meaning, .dark .word-example {
    color: #DBDBDB;
  }

  .word-example-target {
    margin-left: 8px;
  }

  .word-translation {
    padding: 12px;
    border-radius: 12px;
    background: #FAFBFB;
    margin: 8px 0;
  }

  .dark .word-translation {
    background-color: #1D1E1E;
  }

  .word-translation-text {
    color: #222;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5;
    display: flex;
    gap: 16px;
  }


  .sentences-translation {
    padding-bottom: 8px;
  }

  .sentences-translation-text {
    min-height: 35px;
    color: #333;
    font-size: 16px;
    font-weight: 700;
    line-height: 1.5;
    margin-bottom: 8px;
    white-space: pre-wrap;
    user-select: none;
  }

  .translation-action {
    display: flex;
    gap: 8px;
  }

  .word-contextual-meaning {
    color: #333;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    margin-top: 8px;
  }

  .dark .word-translation-text, .dark .sentences-translation-text, .dark .word-contextual-meaning {
    color: #DBDBDB;
  }


  .icon-wrapper {
    width: 21px;
    height: 21px;
    background-color: #F3F5F6;
    cursor: pointer;
    border-radius: 4px;
  }

  .dark .icon-wrapper {
    background-color: #252626;
  }

  .icon-wrapper-no-bg {
    width: 21px;
    height: 21px;
    cursor: pointer;
    border-radius: 4px;
  }

  .close-icon, .menu-icon, .text-translate-icon, .header-icon {
    width: 28px;
    height: 28px;
  }

  .toggle-container {
    display: flex;
    align-items: center;
  }

  .toggle-text {
    font-size: 12px;
    color: #333;
    user-select: none;
  }

  .dark .toggle-text {
    color: #B3B3B3;
  }

  .toggle-icon{
    justify-content: center;
    align-items: center;
    display: flex;
  }

  .left-icon {
    margin-left: 12px;
    margin-right: 8px;
  }

  .right-icon {
    margin-left: 8px;
    margin-right: 12px;
  }

  .feedback-action {
    display: flex;
    gap: 12px;
    justify-content: end;
  }

  .feedback-action-icon {
    width: 24px;
    height: 24px;
  }

  .active {
    color: #EA4C89;
    background-color: #EA4C89;
  }

  .highlight {
    color: #EC5E95;
  }

  .icon-wrapper:hover, .icon-wrapper-no-bg:hover {
    background-color: #F6F8F9
  }

  .dark .icon-wrapper:hover, .dark .icon-wrapper-no-bg:hover {
    background-color: #2D2E2E;
  }

  .icon-wrapper:active, .icon-wrapper-no-bg:active {
    background-color: #EDF1F2;
  }

  .dark .icon-wrapper:active, .dark .icon-wrapper-no-bg:active {
    background-color: #202121;
  }

  .${N}-loading {
    --loading-color: #f78fb6;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: block;
    margin: 12px auto;
    position: relative;
    color: white;
    left: -100px;
    box-sizing: border-box;
    animation: ${ie}ShadowRolling 1.5s linear infinite;
  }

  @keyframes ${ie}ShadowRolling {
    0%, 100% {
      box-shadow: 0px 0 rgba(255, 255, 255, 0);
    }
    12% {
      box-shadow: 100px 0 var(--loading-color);
    }
    25% {
      box-shadow: 110px 0 var(--loading-color), 100px 0 var(--loading-color);
    }
    36% {
      box-shadow: 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
    }
    50% {
      box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color), 100px 0 var(--loading-color);
    }
    62% {
      box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color), 110px 0 var(--loading-color);
    }
    75% {
      box-shadow: 130px 0 var(--loading-color), 120px 0 var(--loading-color);
    }
    87% {
      box-shadow: 130px 0 var(--loading-color);
    }
  }

   .${N}-dropdown {
    position: relative;
    display: inline-block;
  }

  .${N}-dropdown-menu {
    position: absolute;
    min-width: 200px;
    background: white;
    border-radius: 12px;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    padding: 12px 8px;
    z-index: 1000;
  }

  .dark .${N}-dropdown-menu {
    background: #111;
    box-shadow: 0px 4px 16px 0px rgba(255, 255, 255, 0.10);
  }

  .${N}-dropdown-menu.bottomLeft {
    top: 100%;
    left: 0;
    margin-top: 4px;
  }

  .${N}-dropdown-menu.bottomRight {
    top: 100%;
    right: 0;
    margin-top: 4px;
  }

  .${N}-dropdown-menu.topLeft {
    bottom: 100%;
    left: 0;
    margin-bottom: 4px;
  }

  .${N}-dropdown-menu.topRight {
    bottom: 100%;
    right: 0;
    margin-bottom: 4px;
  }

  .${N}-dropdown-group {
    border-bottom: 1px solid #E6E6E6;
    padding-bottom: 8px;
    margin-bottom: 8px;
  }

  .dark .${N}-dropdown-group {
    border-bottom: 1px solid #2D2E2E;
  }

  .${N}-dropdown-group-title {
    padding: 8px 16px;
    color: #333;
    font-size: 12px;
  }

  .${N}-dropdown-item {
    position: relative;
    padding: 8px 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    font-size: 14px;
    color: #333;
  }

  .dark .${N}-dropdown-group-title {
    color: #DBDBDB;
  }

  .dark .${N}-dropdown-item {
    color: #DBDBDB;
  }

  .label-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 6px;
  }

  .label-item-left {
    display: flex;
    align-items: center;
    gap: 6px;
  }

  .label-item-right {
    display: flex;
    align-items: center;
  }

  .${N}-dropdown-item:hover {
    background: #f6f8f9;
  }

  .dark .${N}-dropdown-item:hover {
    background: #2D2E2E;
  }

  .${N}-dropdown-item.disabled {
    color: #999;
    cursor: not-allowed;
  }

  .dark .${N}-dropdown-item.disabled {
    color: #777;
  }

  .${N}-dropdown-item.disabled:hover {
    background: none;
  }

  .${N}-dropdown-submenu {
    position: absolute;
    top: 0;
    min-width: 200px;
    background: white;
    border-radius: 12px;
    box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.08);
    padding: 12px 8px;
    display: none;
    font-size: 14px;
    color: #333;
  }

  .dark .${N}-dropdown-submenu {
    background: #111;
    box-shadow: 0px 4px 16px 0px rgba(255, 255, 255, 0.10);
    color: #DBDBDB;
  }

  .${N}-dropdown-submenu.bottomLeft {
    left: 100%;
  }

  .${N}-dropdown-submenu.bottomRight {
    right: 100%;
  }

  .${N}-dropdown-item:hover .${N}-dropdown-submenu {
    display: block;
  }

  .${N}-dropdown-item-label {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .label-tip {
    color: #999;
    font-size: 14px;
  }

  .dark .label-tip {
    color: #777;
  }

  .label-tip span {
    color: #EA4C89;
    text-decoration-line: underline;
    text-decoration-style: solid;
    text-decoration-skip-ink: none;
    text-decoration-thickness: auto;
    text-underline-offset: auto;
    text-underline-position: from-font;
    cursor: pointer;
  }

  .error-container {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    padding: 16px;
    word-break: break-all;
  }
  .error-text {
    color: #e74c3c;
    font-size: 14px;
    font-weight: 400;
    line-height: 1.5;
    margin-bottom: 12px;
    overflow-y: auto;
    max-height: 400px;
  }

  .error-button {
    padding: 5px 10px;
    border: none;
    background-color: #e74c3c;
    color: white;
    border-radius: 4px;
    cursor: pointer;
  }

  /* RTL Overrides Start */

  .modal-body-rtl {
    direction: rtl;
    text-align: right;
  }

  .modal-body-rtl .feedback-action {
    text-align: right;
  }

  modal-body-rtl .word-phonetic {
    margin-left: 0;
    margin-right: 12px;
  }

  .modal-body-rtl .speaker-icon {
    margin-left: 0;
    margin-right: 6px;
  }

  .modal-body-rtl .word-dictionary-pos {
     text-align: left;
  }

  .modal-body-rtl .word-example {
    padding-left: 0;
    padding-right: 32px;
  }

  .modal-body-rtl .word-example-target {
    margin-left: 0;
    margin-right: 8px;
  }


  [dir="rtl"] .${N}-modal-body .translation-action {
     flex-direction: row-reverse;
  }

  [dir="rtl"] .${N}-modal-body .feedback-action {
    justify-content: start;
  }

  .modal-body-rtl .error-container {
    align-items: flex-end;
  }


  [dir="rtl"] .${N}-dropdown-menu.bottomLeft {
    left: auto;
    right: 0;
  }

  [dir="rtl"] .${N}-dropdown-menu.bottomRight {
    right: auto;
    left: 0;
  }

  [dir="rtl"] .${N}-dropdown-menu.topLeft {
    left: auto;
    right: 0;
  }

  [dir="rtl"] .${N}-dropdown-menu.topRight {
    right: auto;
    left: 0;
  }

  /* Submenu positioning adjustments */
  [dir="rtl"] .${N}-dropdown-submenu.bottomLeft {
    left: auto;
    right: 100%;
  }

  [dir="rtl"] .${N}-dropdown-submenu.bottomRight {
    right: auto;
    left: 100%;
  }

  /* Dropdown item layout adjustments */
  [dir="rtl"] .label-item,
  [dir="rtl"] .label-item-left,
  [dir="rtl"] .label-item-right {
     /* Reversing flex items if needed */
     /* flex-direction: row-reverse; */
  }

  [dir="rtl"] .arrow-icon-wrapper {
    transform: rotate(180deg);
  }

  /* RTL Overrides End */
`;
  var FL = {
    en: "en-US",
    "zh-CN": "zh-CN",
    "zh-TW": "zh-TW",
    yue: "zh-HK",
    af: "af-ZA",
    az: "az-AZ",
    be: "be-BY",
    bn: "bn-IN",
    bs: "bs-BA",
    ja: "ja-JP",
    ko: "ko-KR",
    fr: "fr-FR",
    de: "de-DE",
    es: "es-ES",
    it: "it-IT",
    ru: "ru-RU",
    pt: "pt-PT",
    "pt-br": "pt-BR",
    "pt-BR": "pt-BR",
    "pt-PT": "pt-PT",
    nl: "nl-NL",
    pl: "pl-PL",
    ar: "ar-001",
    bg: "bg-BG",
    ca: "ca-ES",
    cs: "cs-CZ",
    da: "da-DK",
    el: "el-GR",
    fi: "fi-FI",
    he: "he-IL",
    hi: "hi-IN",
    hr: "hr-HR",
    id: "id-ID",
    vi: "vi-VN",
    sv: "sv-SE",
    ms: "ms-MY",
    th: "th-TH",
    ur: "ur-PK",
    ta: "ta-IN",
    te: "te-IN",
    ml: "ml-IN",
    kn: "kn-IN",
    gu: "gu-IN",
    mr: "mr-IN",
    pa: "pa-IN",
    si: "si-LK",
    km: "km-KH",
    lo: "lo-LA",
    my: "my-MM",
    ne: "ne-NP",
    mn: "mn-MN",
    sd: "sd-PK",
    fa: "fa-IR",
    ps: "ps-AF",
    ha: "ha-NG",
    ig: "ig-NG",
    yo: "yo-NG",
    zu: "zu-ZA",
    xh: "xh-ZA",
    st: "st-ZA",
    tn: "tn-ZA",
    sn: "sn-ZW",
    ny: "ny-MW",
    so: "so-SO",
    am: "am-ET",
    bo: "bo-CN",
    ceb: "ceb-PH",
    co: "co-FR",
    cy: "cy-GB",
    eo: "eo-EO",
    et: "et-EE",
    eu: "eu-ES",
    fil: "fil-PH",
    fj: "fj-FJ",
    fy: "fy-NL",
    ga: "ga-IE",
    gd: "gd-GB",
    gl: "gl-ES",
    haw: "haw-US",
    hmn: "hmn-US",
    ht: "ht-HT",
    hu: "hu-HU",
    hy: "hy-AM",
    is: "is-IS",
    jw: "jw-ID",
    ka: "ka-GE",
    kk: "kk-KZ",
    ku: "ku-TR",
    ky: "ky-KG",
    la: "la-VA",
    lb: "lb-LU",
    lt: "lt-LT",
    lv: "lv-LV",
    mg: "mg-MG",
    mi: "mi-NZ",
    mk: "mk-MK",
    mt: "mt-MT",
    mww: "mww-US",
    no: "no-NO",
    otq: "otq-MX",
    ro: "ro-RO",
    sa: "sa-IN",
    sk: "sk-SK",
    sl: "sl-SI",
    sm: "sm-WS",
    sq: "sq-AL",
    "sr-Cyrl": "sr-CS",
    "sr-Latn": "sr-CS",
    su: "su-ID",
    sw: "sw-TZ",
    tlh: "tlh-TLH",
    "tlh-Qaak": "tlh-TLH",
    to: "to-TO",
    tr: "tr-TR",
    ty: "ty-PF",
    ug: "ug-CN",
    uk: "uk-UA",
    uz: "uz-UZ",
    yi: "yi-IL",
    yua: "yua-MX",
    "zh-CN-NE": "zh-CN",
    "ur-roman": "ur-PK",
  };
  var BL = { rate: 10, volume: 100 },
    Wh = [];
  globalThis.speechSynthesis &&
    ((Wh = speechSynthesis.getVoices()),
    (globalThis.speechSynthesis.onvoiceschanged = () => {
      Wh = speechSynthesis.getVoices();
    }));
  var AX = { WebSpeech: B9, YouDao: kX };
  function Kh({
    provider: e,
    text: t,
    lang: n,
    onFinish: r,
    signal: a,
    onStart: i,
  }) {
    let o = BL.rate,
      s = BL.volume,
      l = AX[e ?? "WebSpeech"];
    return (
      I.debug("[tts] call tts provider", e),
      I.debug("[tts] call tts text", t),
      I.debug("[tts] call tts lang", n),
      l({
        text: t,
        lang: n ?? "en",
        rate: o,
        volume: s,
        signal: a,
        onFinish: r,
        onStart: i,
      })
    );
  }
  function B9({
    text: e,
    lang: t,
    rate: n,
    volume: r,
    signal: a,
    onFinish: i,
    onStart: o,
  }) {
    if (!globalThis.speechSynthesis) {
      I.warn("[tts] Web Speech API is not supported in this browser.");
      return;
    }
    let s = (n ?? 10) / 10,
      l = r ? r / 100 : 1,
      u = FL[t] ?? "en-US",
      c = new SpeechSynthesisUtterance();
    i && c.addEventListener("end", i, { once: !0 }),
      (c.text = e),
      (c.lang = t),
      (c.rate = s),
      (c.volume = l);
    let d = Wh.find((m) => m.lang === u) ?? null;
    zn() &&
      u === "en-US" &&
      (d = Wh.find((m) => m.lang === u && m.name === "Fred") ?? null),
      (c.voice = d),
      a.addEventListener(
        "abort",
        () => {
          speechSynthesis.cancel();
        },
        { once: !0 }
      ),
      o?.(),
      speechSynthesis.speak(c);
  }
  async function kX({
    text: e,
    lang: t,
    rate: n,
    volume: r,
    signal: a,
    onFinish: i,
    onStart: o,
  }) {
    let l = `https://dict.youdao.com/dictvoice?audio=${encodeURIComponent(
      e
    )}&type=2`;
    try {
      let u = await Se({
          url: l,
          method: "GET",
          responseType: "base64",
          timeout: 3e3,
        }),
        c = gi(u),
        d = URL.createObjectURL(c),
        m = new Audio(d);
      (m.volume = r ? r / 100 : 1), (m.playbackRate = (n ?? 10) / 10), m.play();
      let p = !1;
      m.addEventListener("canplay", () => {
        p || (o?.(), (p = !0));
      }),
        m.addEventListener("ended", () => {
          i?.(), (p = !1);
        }),
        m.addEventListener("error", (g) => {
          if ((I.error("[tts]Audio error", g), !p))
            return B9({
              text: e,
              lang: t ?? "en",
              rate: n,
              volume: r,
              onFinish: i,
              onStart: o,
              signal: a,
            });
          p = !1;
        }),
        a.addEventListener(
          "abort",
          () => {
            m.pause(),
              (m.currentTime = 0),
              (m.src = ""),
              URL.revokeObjectURL(d);
          },
          { once: !0 }
        ),
        o?.();
    } catch (u) {
      return (
        I.error("[tts]Youdao TTS API error", u),
        B9({
          text: e,
          lang: t ?? "en",
          rate: n,
          volume: r,
          onFinish: i,
          onStart: o,
          signal: a,
        })
      );
    }
  }
  function Qh({
    text: e,
    lang: t,
    className: n,
    immediate: r = !1,
    ctx: a,
    onStopRefChange: i,
  }) {
    let [o, s] = V(!1),
      [l, u] = V(t),
      c = ge();
    if (!e) return null;
    Q(() => {
      (!t || t === "auto") &&
        qh({ text: e, ctx: a }).then((m) => {
          I.debug("[tts] detectedLang", m), u(m);
        });
    }, []),
      Q(() => {
        r && l && d();
      }, [r, e, l]),
      Q(() => {
        i?.(c.current);
      }, [c.current, i]);
    let d = (m) => {
      if ((m?.preventDefault(), m?.stopPropagation(), o)) {
        c.current?.();
        return;
      }
      let p = new AbortController(),
        { signal: g } = p;
      c.current = () => {
        p.abort(), s(!1);
      };
      let h = "YouDao";
      Kh({
        text: e.toLowerCase(),
        lang: l,
        signal: g,
        provider: h,
        onStart: () => {
          s(!0);
        },
        onFinish: () => {
          s(!1);
        },
      });
    };
    return C("span", {
      class: `icon-wrapper speaker-icon ${n ?? ""}`,
      onClick: (m) => d(m),
      children: C(Mu, {}),
    });
  }
  function _L({
    ctx: e,
    translationResult: t,
    text: n,
    currentIndex: r,
    visible: a,
  }) {
    let [i, o] = V({}),
      s = (u) => {
        if (r === void 0 || i[r] === u) return;
        o((m) => ({ ...m, [r]: u }));
        let d = `selection_translate_${u.toLowerCase()}`;
        Te({
          key: d,
          ctx: e,
          params: {
            trigger: e.rule.selectionTranslation?.triggerMode || "",
            translation_service: Qu(e).service,
            selection_text: n,
            translation_result: JSON.stringify(t ?? {}),
          },
          forceDaily: !1,
          ignoreGA: !0,
        });
      },
      l = r !== void 0 ? i[r] : void 0;
    return a
      ? C("div", {
          class: "feedback-action",
          children: [
            C("div", {
              className: "icon-wrapper-no-bg feedback-action-icon",
              onClick: () => {
                s("ThumbUp");
              },
              children: l === "ThumbUp" ? C(Zk, {}) : C(Yk, {}),
            }),
            C("div", {
              className: "icon-wrapper-no-bg feedback-action-icon",
              onClick: () => {
                s("ThumbDown");
              },
              children: l === "ThumbDown" ? C(Xk, {}) : C(Jk, {}),
            }),
          ],
        })
      : null;
  }
  function _9({ text: e, ctx: t }) {
    let [n, r] = V(!1),
      a = ye.bind(null, t.config.interfaceLanguage);
    async function i(o) {
      await Gh(o),
        r(!0),
        setTimeout(() => {
          r(!1);
        }, 2e3);
    }
    return C(Tt, {
      text: a(
        n ? "selectionTranslationCopySuccess" : "selectionTranslationCopy"
      ),
      children: C("div", {
        class: "icon-wrapper",
        onClick: () => i(e),
        children: C(Zg, {}),
      }),
    });
  }
  function NL({
    ctx: e,
    params: t,
    signal: n,
    currentIndex: r,
    paramsRecord: a,
    onStopRefChange: i,
  }) {
    let o = ye.bind(null, e.config.interfaceLanguage),
      [s, l] = V(null),
      [u, c] = V(!0),
      [d, m] = V(!1),
      [p, g] = V(),
      [h, x] = V([]),
      [f, y] = V(!1),
      [b, v] = V(),
      S = cn(e.config.rtlLanguages, e.targetLanguage),
      [T, E] = V(t);
    Q(() => {
      D(Qu(e)), E(t);
    }, [t.originalText]),
      Q(() => {
        r !== void 0 && r <= h.length - 1 && (y(!1), g(h[r]), E(a?.[r] || t));
      }, [r]);
    let A = (R) => {
        if (!R || !T.originalText) return R;
        let M = T.originalText.toLowerCase(),
          P = "",
          w = 0;
        for (; w < R.length; ) {
          let L = R.toLowerCase().indexOf(M, w);
          if (L === -1) {
            P += R.slice(w);
            break;
          }
          (P += R.slice(w, L)),
            (P += `<span class="highlight">${R.slice(L, L + M.length)}</span>`),
            (w = L + M.length);
        }
        return P;
      },
      D = async ({ service: R, model: M }) => {
        l(null), c(!0), m(!1), g(void 0);
        let P = "",
          w = null;
        try {
          w = await qh({ text: t.contextText, ctx: e });
        } catch (L) {
          c(!1), l(L.message ?? o("translateFail"));
          return;
        }
        v(w),
          kE(
            {
              text: t.originalText,
              contextText: t.contextText,
              from: w,
              to: e.targetLanguage,
              url: e.url,
              signal: n,
              translationService: R,
              model: M,
              onMessage: (L) => {
                I.debug("[selection-translation] stream message", L),
                  (P = P + L);
                try {
                  P.startsWith("```json\n") && (P = P.substring(8)),
                    P.endsWith("\n```") && (P = P.substring(0, P.length - 4));
                  let U = wc(P);
                  I.debug("[selection-translation] stream parsedMsg", U),
                    c(!1),
                    g(U),
                    F(U);
                } catch {}
              },
              onFinish: (L) => {
                I.debug("[selection-translation] stream onFinish", L),
                  m(!0),
                  u && c(!1),
                  wo(!1);
                try {
                  let U = wc(P);
                  U && x((z) => [...z, U]);
                } catch {}
              },
              onError: (L) => {
                if (
                  (I.debug("[selection-translation] stream onError", L),
                  xt(e.user))
                ) {
                  O();
                  return;
                }
                u && c(!1), wo(!1), l(L.message ?? o("translateFail"));
              },
            },
            e
          );
      },
      F = (R) => {
        e.rule.selectionTranslation?.enableAutoRead && R?.phonetic && y(!0);
      },
      O = () => {
        I.debug(`[selection-translation] fallback to ${Vh.service}`), D(Vh);
      },
      _ = () =>
        C("div", {
          className: "error-container",
          children: [
            C("div", { className: "error-text", children: s }),
            C("div", {
              style: { marginTop: "10px" },
              children: C("button", {
                onClick: () => window.location.reload(),
                className: "error-button",
                children: o("error.retry"),
              }),
            }),
          ],
        }),
      B = () => C("div", { class: `${N}-loading` }),
      j = () =>
        C("div", {
          class: "translation-result-container",
          children: [
            p?.phonetic &&
              C("div", {
                className: "word-dictionary",
                children: [
                  C("div", {
                    class: "word-original",
                    children: [
                      C("div", {
                        className: "word-original-text",
                        children: T.originalText,
                      }),
                      p?.phonetic &&
                        C("span", {
                          className: "word-phonetic",
                          children: p?.phonetic,
                        }),
                      C(Qh, {
                        text: T.originalText,
                        ctx: e,
                        immediate: f,
                        onStopRefChange: i,
                      }),
                    ],
                  }),
                  C("div", {
                    children: p?.definitions?.map((R) =>
                      C("div", {
                        className: "word-dictionary-item",
                        children: [
                          C("div", {
                            className: "word-dictionary-definition",
                            children: [
                              C("div", {
                                className: "word-dictionary-pos",
                                children: R?.pos,
                              }),
                              C("div", {
                                className: "word-dictionary-meaning",
                                children: R?.meaning,
                              }),
                            ],
                          }),
                          R?.example &&
                            C("div", {
                              className: "word-example",
                              children: [
                                C("span", {
                                  dangerouslySetInnerHTML: {
                                    __html: A(R?.example?.source),
                                  },
                                }),
                                C("span", {
                                  className: "word-example-target",
                                  children: R?.example?.target,
                                }),
                              ],
                            }),
                        ],
                      })
                    ),
                  }),
                ],
              }),
            p?.definitions && p?.definitions.length > 0
              ? p?.translation &&
                C(it, {
                  children: C("div", {
                    class: "word-translation",
                    children: [
                      C("div", {
                        className: "word-translation-text",
                        children: [
                          p?.translation,
                          C("div", {
                            class: "translation-action",
                            children: [
                              C(Qh, {
                                text: p?.translation,
                                lang: e.targetLanguage,
                                ctx: e,
                                onStopRefChange: i,
                              }),
                              C(_9, { text: p?.translation, ctx: e }),
                            ],
                          }),
                        ],
                      }),
                      p?.contextual_analysis &&
                        C("div", {
                          className: "word-contextual-meaning",
                          children: p?.contextual_analysis,
                        }),
                    ],
                  }),
                })
              : C("div", {
                  class: "sentences-translation",
                  children: [
                    C("div", {
                      className: "sentences-translation-text",
                      children: p?.translation,
                    }),
                    p?.translation &&
                      C("div", {
                        class: "translation-action",
                        children: [
                          C(Qh, {
                            text: p?.translation,
                            lang: e.targetLanguage,
                            ctx: e,
                            onStopRefChange: i,
                          }),
                          C(_9, { text: p?.translation, ctx: e }),
                        ],
                      }),
                    p?.contextual_analysis &&
                      C("div", {
                        className: "word-contextual-meaning",
                        children: p?.contextual_analysis,
                      }),
                  ],
                }),
          ],
        });
    return C(it, {
      children: [
        C("div", {
          className: `${N}-modal-body notranslate ${S ? "modal-body-rtl" : ""}`,
          children: [u && B(), s && _(), !s && !u && j()],
        }),
        C(_L, {
          visible: d,
          ctx: e,
          translationResult: p,
          text: T.originalText,
          currentIndex: r,
        }),
      ],
    });
  }
  function OL({ menu: e, children: t, onClick: n }) {
    let [r, a] = V(!1),
      [i, o] = V(null),
      s = ge(null),
      l = ge(null),
      [u, c] = V("bottomLeft"),
      [d, m] = V("bottomLeft");
    Q(() => {
      let x = `${N}-modal`,
        f = (y) => {
          s.current && !s.current.contains(y.target) && (a(!1), o(null));
        };
      return (
        St(`#${Xr} -> .${x}`)?.addEventListener("mousedown", f),
        () => St(`#${Xr} -> .${x}`)?.removeEventListener("mousedown", f)
      );
    }, []),
      Q(() => {
        p();
      }, [r]);
    let p = () => {
        if (r && s.current) {
          let x = s.current.getBoundingClientRect(),
            y = globalThis.innerWidth - x.right;
          y < 200
            ? (c("bottomRight"), m("bottomRight"))
            : y >= 200 && y < 400
            ? (c("bottomLeft"), m("bottomRight"))
            : (c("bottomLeft"), m("bottomLeft"));
        }
      },
      g = (x) => {
        x.preventDefault(), a(!r);
      },
      h = (x) =>
        x.type === "group"
          ? C("div", {
              class: `${N}-dropdown-group`,
              children: x.children?.map((f) => h(f)),
            })
          : C("div", {
              className: `${N}-dropdown-item ${x.disabled ? "disabled" : ""} ${
                x.children ? "has-children" : ""
              }`,
              onMouseEnter: () => x.children && o(x.key),
              onClick: (f) => {
                f.stopPropagation(), !x.disabled && n?.(x);
              },
              children: [
                C("span", {
                  className: `${N}-dropdown-item-label`,
                  children: typeof x.label == "function" ? x.label() : x.label,
                }),
                x.children &&
                  i === x.key &&
                  C("div", {
                    className: `${N}-dropdown-submenu ${d}`,
                    children: x.children.map((f) => h(f)),
                  }),
              ],
            });
    return C("div", {
      ref: s,
      className: `${N}-dropdown`,
      children: [
        C("div", { onClick: g, children: t }),
        r &&
          C("div", {
            ref: l,
            className: `${N}-dropdown-menu ${u}`,
            children: e.map((x) => h(x)),
          }),
      ],
    });
  }
  function jL({ onClose: e, ctx: t }) {
    let n = Gi(),
      [r, a] = n,
      i = ye.bind(null, t.config.interfaceLanguage),
      o = [
        {
          key: "toggleAutoRead",
          label: () =>
            C("div", {
              class: "label-item",
              children: C("div", {
                class: "label-item-left",
                children: [
                  C("div", {
                    class: "icon-wrapper-no-bg",
                    children: r.generalRule?.["selectionTranslation.add"]
                      ?.enableAutoRead
                      ? C(Mu, {})
                      : C(qk, {}),
                  }),
                  C("div", {
                    children: r.generalRule?.["selectionTranslation.add"]
                      ?.enableAutoRead
                      ? i("enableAutoRead")
                      : i("disableAutoRead"),
                  }),
                ],
              }),
            }),
        },
        {
          key: "ban-selection-translation",
          label: () =>
            C("div", {
              class: "label-item",
              children: [
                C("div", {
                  class: "label-item-left",
                  children: [
                    C("div", {
                      class: "icon-wrapper-no-bg",
                      children: C(Vk, {}),
                    }),
                    C("div", { children: i("banSelectionTranslation") }),
                  ],
                }),
                C("div", {
                  class: "label-item-right arrow-icon-wrapper",
                  children: C(Jg, {}),
                }),
              ],
            }),
          children: [
            {
              key: "ban",
              label: "Ban",
              type: "group",
              children: [
                { key: "ban-once", label: i("banSelectionTranslationOnce") },
                {
                  key: "ban-this-site",
                  label: i("banSelectionTranslationInThisSite"),
                },
                {
                  key: "ban-forever",
                  label: i("banSelectionTranslationForever"),
                },
              ],
            },
            {
              key: "tip",
              label: () =>
                C("div", {
                  class: "label-tip",
                  onClick: l,
                  dangerouslySetInnerHTML: {
                    __html: i("reEnableSelectionTranslation", {
                      settings: `<span class="link" id="open-options">${i(
                        "setting"
                      )}</span>`,
                    }),
                  },
                }),
            },
          ],
        },
      ],
      s = () => {
        gr(!0, "#selection_translation", t.config.useOnlineOptions);
      },
      l = (c) => {
        c.target.id === "open-options" && s();
      };
    return C(OL, {
      menu: o,
      onClick: async (c) => {
        if (c.key === "toggleAutoRead") {
          let d = r.generalRule?.["selectionTranslation.add"]?.enableAutoRead;
          a((m) => ({
            ...m,
            generalRule: {
              ...m.generalRule,
              "selectionTranslation.add": {
                ...m.generalRule?.["selectionTranslation.add"],
                enableAutoRead: !d,
              },
            },
          })),
            await Vi();
        }
        if (
          (c.key === "ban-once" && (HT(!0), e()), c.key === "ban-this-site")
        ) {
          let d = r.generalRule?.["selectionTranslation.add"]?.urlPattern,
            m = d?.excludeMatches || [],
            p = window.location.href,
            g = ql(p, m);
          a((h) => ({
            ...h,
            generalRule: {
              ...h.generalRule,
              "selectionTranslation.add": {
                ...h.generalRule?.["selectionTranslation.add"],
                urlPattern: { ...d, excludeMatches: g },
              },
            },
          })),
            await Vi(),
            e();
        }
        c.key === "ban-forever" &&
          (a((d) => ({
            ...d,
            generalRule: {
              ...d.generalRule,
              "selectionTranslation.add": {
                ...d.generalRule?.["selectionTranslation.add"],
                enable: !1,
              },
            },
          })),
          await Vi(),
          e());
      },
      children: C("div", {
        class: "icon-wrapper-no-bg menu-icon",
        children: C(Gk, {}),
      }),
    });
  }
  function Yh(e) {
    let t = e.rule.darkModeRule,
      { element: n, selectors: r } = t || {};
    if (!n || !r) return !1;
    let a = document.querySelectorAll(n);
    if (!a || a.length === 0) return !1;
    for (let i of a) if (ir(i, r)) return !0;
    return !1;
  }
  function zL(e, t) {
    let n = Yh(e),
      r = e.rule.darkModeRule,
      { element: a, selectors: i } = r || {};
    if (!a || !i) return () => {};
    let o = document.querySelector(a);
    if (!o) return () => {};
    let s = new MutationObserver(() => {
      let l = Yh(e);
      l !== n && ((n = l), t(l));
    });
    return (
      s.observe(o, { attributes: !0, childList: !1, subtree: !1 }),
      () => {
        s.disconnect();
      }
    );
  }
  var Xr = `${N}-modal-selection-root`;
  function MX({ ctx: e, params: t, signal: n, onClose: r }) {
    let a = ye.bind(null, e.config.interfaceLanguage),
      [i, o] = V(!1),
      [s, l] = V(t),
      [u, c] = V([t]),
      d = ge(null),
      [m, p] = V(null),
      [g, h] = V(0),
      [x, f] = V(!1),
      [y, b] = V({ x: 0, y: 0 }),
      v = ge({ x: 0, y: 0 }),
      S = ge({ x: 0, y: 0 }),
      [T, E] = V(Yh(e)),
      A = ge(),
      { style: D } = IL({ selectionRect: s.selectionRect, gap: 10 });
    Q(() => {
      D && m === null && p(D);
    }, [D]),
      Q(() => {
        let M = zL(e, (w) => {
            E(w);
          }),
          P = () => {
            A.current && A.current(), M();
          };
        return (
          n.addEventListener("abort", P),
          () => {
            M(), n.removeEventListener("abort", P);
          }
        );
      }, [e, n]),
      Q(() => {
        if (i) return;
        let M = (w) => {
          w.key === "Escape" && r();
        };
        document.addEventListener("keydown", M);
        let P = (w) => {
          w.detail &&
            (l((L) =>
              L.originalText.toLowerCase() ===
              w.detail.originalText.toLowerCase()
                ? L
                : w.detail
            ),
            c((L) =>
              L.length > 0 &&
              L[L.length - 1]?.originalText?.toLowerCase() ===
                w.detail?.originalText?.toLowerCase()
                ? (wo(!1), L)
                : [...L, w.detail]
            ));
        };
        return (
          document.getElementById(Xr)?.removeEventListener(sl, P),
          document.getElementById(Xr)?.addEventListener(sl, P),
          () => {
            document.removeEventListener("keydown", M),
              document.getElementById(Xr)?.removeEventListener(sl, P);
          }
        );
      }, []),
      Q(() => {
        h(u.length - 1);
      }, [u]);
    let F = cn(e.config.rtlLanguages, e.config.interfaceLanguage),
      O = () => {
        o(!i), b3(!i);
      },
      _ = () => {
        o(!1), b3(!1), r();
      },
      B = (M) => {
        A.current = M;
      },
      j = (M) => {
        d0() ||
          (M === "next"
            ? g < u.length - 1 && h((P) => P + 1)
            : g > 0 && h((P) => P - 1));
      };
    Q(() => {
      let M = d.current?.querySelector(`.${N}-modal-title`);
      if (!M) return;
      let P = (U) => {
          f(!0),
            (v.current = { x: U.clientX, y: U.clientY }),
            (S.current = { ...y }),
            U.preventDefault();
        },
        w = tr((U) => {
          if (!x) return;
          let z = U.clientX - v.current.x,
            G = U.clientY - v.current.y;
          b({ x: S.current.x + z, y: S.current.y + G });
        }, 16),
        L = () => {
          if (!x) return;
          f(!1);
          let U = 450,
            z = d.current?.offsetHeight || 40,
            G = globalThis.innerWidth,
            q = globalThis.innerHeight,
            W = d.current;
          if (!W) return;
          let Z = W.getBoundingClientRect(),
            ne = y.x,
            fe = y.y,
            Ee = !1,
            $ = 20,
            ee = Z.left - y.x,
            pe = Z.top - y.y;
          Z.left < 0 && ((ne = -ee + $), (Ee = !0)),
            Z.right > G && ((ne = G - ee - U - $), (Ee = !0)),
            Z.top < 0 && ((fe = -pe + $), (Ee = !0)),
            Z.bottom > q && ((fe = q - pe - z - $), (Ee = !0)),
            Ee &&
              W &&
              (requestAnimationFrame(() => {
                (W.style.transition =
                  "transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)"),
                  b({ x: ne, y: fe });
              }),
              setTimeout(() => {
                W.style.transition = "";
              }, 300));
        };
      return (
        M.addEventListener("mousedown", P),
        document.addEventListener("mousemove", w),
        document.addEventListener("mouseup", L),
        () => {
          M.removeEventListener("mousedown", P),
            document.removeEventListener("mousemove", w),
            document.removeEventListener("mouseup", L);
        }
      );
    }, [x, y]);
    let R = { transform: `translate(${y.x}px, ${y.y}px)` };
    return C("div", {
      ref: d,
      className: `${N}-modal ${F ? "modal-rtl" : ""} notranslate ${
        T ? "dark" : ""
      }`,
      dir: F ? "rtl" : "ltr",
      style: { ...m, ...R },
      children: [
        C("div", {
          className: `${N}-modal-title notranslate`,
          style: x ? { cursor: "grabbing" } : { cursor: "move" },
          children: [
            C("div", {
              className: `${N}-modal-title-left`,
              children: [
                C(Hh, {}),
                u.length > 1 &&
                  C("div", {
                    class: "toggle-container",
                    children: [
                      C("span", {
                        class: "icon-wrapper toggle-icon left-icon",
                        onClick: () => j("prev"),
                        children: C(Qk, {}),
                      }),
                      C("span", {
                        class: "toggle-text",
                        children: [g + 1, "/", u.length],
                      }),
                      C("span", {
                        class: "icon-wrapper toggle-icon right-icon",
                        onClick: () => j("next"),
                        children: C(Jg, {}),
                      }),
                    ],
                  }),
              ],
            }),
            C("div", {
              class: `${N}-modal-title-right`,
              children: [
                C("span", {
                  class: "icon-wrapper-no-bg text-translate-icon header-icon",
                  onClick: () => DX({ text: u[g].originalText, ctx: e }),
                  children: C(Tt, {
                    text: a("compareWithTextTranslate"),
                    children: C($k, {}),
                  }),
                }),
                C("span", {
                  class: "icon-wrapper-no-bg header-icon",
                  onClick: O,
                  children: i ? C(Kk, {}) : C(Wk, {}),
                }),
                C(jL, { onClose: _, ctx: e }),
                C("span", {
                  class: "icon-wrapper-no-bg close-icon header-icon",
                  onClick: _,
                  children: C(Us, {}),
                }),
              ],
            }),
          ],
        }),
        C(NL, {
          ctx: e,
          params: s,
          signal: n,
          currentIndex: g,
          paramsRecord: u,
          onStopRefChange: B,
        }),
      ],
    });
  }
  function UL(e) {
    let t = document.querySelector(`#${e}`);
    t && t.remove();
  }
  function HL(e, t) {
    UL(Xr);
    let n = new AbortController(),
      { signal: r } = n,
      a = () => {
        f3() || (wo(!1), n.abort(), UL(Xr));
      };
    return (
      Yr({
        id: Xr,
        parent: document.documentElement,
        ctx: e,
        Component: MX,
        props: { ctx: e, params: t, signal: r, onClose: a },
        style: RL,
      }),
      a
    );
  }
  var DX = ({ text: e, ctx: t }) => {
    let n = Le(),
      r = encodeURIComponent(e),
      a = `${n.TEXT_TRANSLATE_URL}#auto/${t.targetLanguage}/${r}`;
    mr(a),
      Te({
        key: "translate_select_text",
        ctx: t,
        params: {
          trigger: "selection_translation_modal",
          targetLanguage: t.targetLanguage,
          has_pined: f3() ? "1" : "0",
        },
      });
  };
  var Zu = N + "-selection-translation-button";
  function IX(e) {
    let t = document.querySelector(`#${e}`);
    t && t.remove();
  }
  function qL(e) {
    IX(Zu),
      Yr({
        id: Zu,
        parent: document.documentElement,
        ctx: e,
        Component: PX,
        props: { ctx: e },
        style: LX,
      });
  }
  function PX(e) {
    return e.ctx.rule.selectionTranslation?.triggerMode === "icon"
      ? C("div", { class: `${Js}-button icon-btn`, children: C(Hh, {}) })
      : C("div", { class: `${Js}-button mini-btn` });
  }
  var Js = "imt-selection-translation",
    LX = `

  .${Js}-button {
    cursor: pointer;
    position: absolute;
    height: 100%;
    z-index: 2147483647;
    transform: translate(-50%, -50%);
  }

  .${Js}-button.mini-btn {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    border: 1px solid #FFF;
    background: #EA4C89;
  }

  .${Js}-button.mini-btn:hover {
    scale: 1.1;
  }

  .${Js}-button.icon-btn {
    width: 24px;
    height: 24px;
  }

  .${Js}-button .logo {
    width: 24px;
    height: 24px;
  }
`;
  var $r = null,
    GL = null,
    Ju = null,
    Zh = [],
    N9 = null,
    Vo = [];
  function O9(e) {
    FX(),
      xu().forEach((n) => {
        RX(e, n);
      });
  }
  function RX(e, t) {
    try {
      if (Be().any) {
        I.debug("[selection-translation] mobile is not supported");
        return;
      }
      if (J()) {
        I.debug("[selection-translation] monkey is not supported");
        return;
      }
      if (
        !e.rule.selectionTranslation?.enable ||
        e.isSelectionTranslationExcludeUrl
      ) {
        I.debug("[selection-translation] selectionTranslation is disabled");
        return;
      }
      let n = (i) => NX(i, e, t),
        r = () => BX(t, e),
        a = VL;
      t.document.addEventListener("selectionchange", r),
        Vo.push(() => {
          t.document.removeEventListener("selectionchange", r);
        }),
        t.document.addEventListener("mouseup", n),
        Vo.push(() => {
          t.document.removeEventListener("mouseup", n);
        }),
        t.document.addEventListener("mousedown", a),
        Vo.push(() => {
          t.document.removeEventListener("mousedown", a);
        }),
        OX(e);
    } catch (n) {
      I.error(n);
    }
  }
  function FX() {
    N9 = null;
    try {
      Vo.forEach((e) => e());
    } catch {}
    (Vo = []), Jh();
  }
  function BX(e, t) {
    let n = e.document.activeElement;
    if (Yu()) {
      let i = n,
        o = i.value,
        s = i.selectionStart || 0,
        l = i.selectionEnd || 0;
      if (s !== l) {
        let u = o.substring(s, l);
        if (u.trim() !== "") {
          let c = i.getBoundingClientRect();
          $r = { text: u, range: null, rect: c, contextText: o };
          return;
        }
      }
    }
    let a = e.getSelection();
    if (a && !a?.isCollapsed) {
      let i = a.toString();
      if (i.trim() === "") {
        vd(), ($r = null);
        return;
      }
      let o = a.rangeCount > 0 ? a.getRangeAt(0) : null;
      if (!o) {
        vd(), ($r = null);
        return;
      }
      if (i !== $r?.text) {
        let s = j9(a);
        $r = {
          text: i,
          range: o,
          rect: o.getBoundingClientRect(),
          contextText: s,
        };
      }
    } else vd(), ($r = null);
  }
  var _X = qt((e) => {
    Jh();
    let t = $r,
      n = e.rule.selectionTranslation?.triggerMode;
    if (n === "direct") {
      t && t.text && (z9({ lastSelection: t, ctx: e }), ($r = null));
      return;
    }
    if (n !== "icon" && n !== "mini") {
      let r = DL(t?.range),
        a = Yu();
      if (r || a) {
        t &&
          t.text &&
          ($t.filter = function () {
            return $t.setScope("selectionTranslation"), !0;
          });
        return;
      }
      t && t.text ? $t.setScope("selectionTranslation") : vd();
      return;
    }
    t && (zX({ lastSelection: t, ctx: e }), ($r = null));
  }, 300);
  function NX(e, t, n) {
    if ((vd(), UT())) {
      I.debug(
        "[selection-translation] disable selectionTranslation for this time"
      );
      return;
    }
    d0() || ((Ju = { x: e.clientX, y: e.clientY }), _X(t));
  }
  function j9(e) {
    if (!e) return "";
    let n = e.baseNode;
    if (!n) return "";
    let r = n.textContent,
      a = n.parentElement;
    if (!a) return r;
    let i = (d) => {
        if (!d) return "";
        let m = d.cloneNode(!0);
        return (
          m.querySelectorAll(`font.${vt}`).forEach((p) => {
            p.remove();
          }),
          m?.textContent?.slice(0, 200) || ""
        );
      },
      o = a.previousElementSibling,
      s = a.nextElementSibling,
      l = i(o),
      u = i(s),
      c = i(a) || r;
    return `${l}
${c}
${u}`;
  }
  function OX(e) {
    let t = e.rule.selectionTranslation?.triggerMode;
    if (!t || !Kd.includes(t)) return;
    let n = t.toLowerCase();
    $t(
      "*",
      {
        scope: "selectionTranslation",
        element: globalThis.document,
        keyup: !0,
      },
      (r) => {
        if (
          (r.type === "keydown" && (Zh = $t.getPressedKeyCodes()),
          r.type === "keyup")
        ) {
          let a = Zh;
          if (
            (I.debug(`[selection-translation] hotkeys: press keycodes: ${a}`),
            a.length > 1)
          ) {
            I.debug("[selection-translation] hotkeys: more than one keycodes"),
              (Zh = []);
            return;
          }
          if (a.length === 0) {
            I.debug("[selection-translation] hotkeys: no keycodes");
            return;
          }
          let i = Wd[n] === a[0];
          I.debug(
            `[selection-translation] hotkeys: Whether the shortcut key pressed is ${n}: ${i}`
          ),
            i &&
              ((Zh = []), $r && $r.text && z9({ lastSelection: $r, ctx: e }));
        }
      }
    ),
      Vo.push(() => {
        $t.deleteScope("selectionTranslation");
      });
  }
  function jX(e, t, n) {
    let r = globalThis.getSelection();
    if (!r || !r.rangeCount) return null;
    let a = n === "icon" ? 20 : 10,
      i = document.activeElement;
    if (Yu()) {
      let g = Ju?.x,
        h = Ju?.y;
      if (g && h)
        return {
          x: g + globalThis.scrollX + 20,
          y: h + globalThis.scrollY + 30,
        };
      let f = i.getBoundingClientRect();
      return {
        x: f.right + globalThis.scrollX,
        y: f.bottom + globalThis.scrollY + a,
      };
    }
    let s = null,
      l = [];
    for (let g = 0; g < r.rangeCount; g++) {
      let h = r.getRangeAt(g),
        x = Array.from(h.getClientRects()).filter((f) => {
          let y = document.elementFromPoint(
            f.left + f.width / 2,
            f.top + f.height / 2
          );
          if (!y) return !1;
          let b = globalThis.getComputedStyle(y);
          return (
            b.userSelect !== "none" &&
            b.getPropertyValue("-webkit-user-select") !== "none"
          );
        });
      l.push(...x);
    }
    if (l.length === 0) return null;
    let u = -1 / 0,
      c = -1 / 0,
      d = 1 / 0,
      m = 1 / 0;
    for (let g of l)
      (g.bottom > u || (g.bottom === u && g.right > c)) &&
        ((u = g.bottom), (c = g.right), (s = g)),
        (g.top < d || (g.top === d && g.left < m)) &&
          ((d = g.top), (m = g.left));
    if (!s) return null;
    if (c - m < 150)
      return { x: c + globalThis.scrollX, y: u + globalThis.scrollY + a };
    if (e !== void 0 && t !== void 0) {
      let g = (m + c) / 2;
      return e < g
        ? { x: m + globalThis.scrollX, y: d + globalThis.scrollY - a }
        : { x: c + globalThis.scrollX, y: u + globalThis.scrollY + a };
    }
    return { x: c + globalThis.scrollX, y: u + globalThis.scrollY + a };
  }
  function zX({ lastSelection: e, ctx: t }) {
    qL(t);
    let n = St(`#${Zu} -> .imt-selection-translation-button`);
    if (!n)
      throw new Error(
        "\u5212\u8BCD\u7FFB\u8BD1\u56FE\u6807\u521B\u5EFA\u5931\u8D25"
      );
    let r = Ju?.x,
      a = Ju?.y,
      i = t.rule.selectionTranslation?.triggerMode,
      o = jX(r, a, i);
    if (!o) return;
    let { x: s, y: l } = o;
    (N9 = o),
      (n.style.top = `${l}px`),
      (n.style.left = `${s}px`),
      (n.style.display = "block");
    let u = () => {
      z9({ lastSelection: e, ctx: t });
    };
    t.rule.selectionTranslation?.triggerModeForIcon === "click"
      ? (n.addEventListener("click", u),
        Vo.push(() => {
          n?.removeEventListener("click", u);
        }))
      : (n.addEventListener("mouseover", u),
        Vo.push(() => {
          n?.removeEventListener("mouseover", u);
        }));
  }
  function Jh() {
    let e = St(`#${Zu}`);
    e && e.remove();
  }
  function VL(e) {
    let t = document.querySelector(`#${Zu}`);
    e.target !== t && Jh();
    let n = document.querySelector(`#${Xr}`),
      r = document.querySelector(`#${N}-popup`),
      a = document.querySelector(`#${N}-modal-root`);
    n && e.target !== n && e.target !== r && e.target !== a && GL?.();
  }
  function z9({ lastSelection: e, ctx: t }) {
    if (!d0())
      try {
        Jh(), U9({ selection: e, ctx: t });
      } catch (n) {
        throw n;
      }
  }
  function U9({ selection: e, ctx: t, trigger: n }) {
    wo(!0),
      Te({
        key: "selection_translate",
        ctx: t,
        params: {
          trigger: n || t.rule.selectionTranslation?.triggerMode || "",
          translation_service: Qu(t).service,
        },
      }),
      kr("translate_line_1", t);
    let r = document.querySelector(`#${Xr}`);
    if (r) {
      let i = {
          originalText: e.text.trim(),
          contextText: e.contextText,
          selectionRect: e.rect,
        },
        o = new CustomEvent(sl, { detail: i });
      r.dispatchEvent(o);
      return;
    }
    GL = HL(t, {
      originalText: e.text.trim(),
      contextText: e.contextText,
      selectionRect: e.rect,
    });
    let a = VL;
    document.removeEventListener("mousedown", a),
      document.addEventListener("mousedown", a);
  }
  function vd() {
    $t.setScope("mouseHover"), UX();
  }
  function UX() {
    $t.filter = function (e) {
      let t = e.target || e.srcElement,
        { tagName: n } = t,
        r = !0;
      return (
        (t.isContentEditable ||
          ((n === "INPUT" || n === "TEXTAREA" || n === "SELECT") &&
            !t.readOnly)) &&
          (r = !1),
        r
      );
    };
  }
  function PL() {
    return N9;
  }
  function LL() {
    return Ju;
  }
  async function H9({
    ctx: e,
    text: t,
    sourceLang: n,
    services: r,
    targetLangs: a,
    callback: i,
  }) {
    if (!t.trim()) return;
    let o = [],
      s = {
        id: 0,
        url: "side-panel",
        text: t,
        to: "zh-CN",
        from: n,
        force: !0,
        inArticleContext: !0,
      };
    return (
      Np(e, t, r),
      r.forEach(async (u) => {
        for (let c of a) {
          i({
            text: t,
            translateText: "",
            service: u,
            targetLang: c,
            loading: !0,
          });
          let d = await jp(e, u);
          o.push(
            Ni(
              { ...s, to: c },
              { ...e, translationService: u },
              { retry: 1, contextTerms: d }
            )
              .then((m) => {
                i({
                  text: t,
                  translateText: m.text,
                  service: u,
                  targetLang: c,
                  loading: !1,
                });
              })
              .catch((m) => {
                i({
                  text: t,
                  translateText: "",
                  service: u,
                  targetLang: c,
                  loading: !1,
                  error: m instanceof Y ? m : new Y(m.code, m.message),
                });
              })
          );
        }
      }),
      await Promise.all(o)
    );
  }
  function WL() {
    let e = "side-footer-expand",
      [t, n] = V(() => (localStorage.getItem(e) || "1") == "1");
    return (
      Q(() => {
        localStorage.setItem(e, t ? "1" : "0");
      }, [t]),
      { expand: t, setExpand: n }
    );
  }
  function q9() {
    let [e, t] = V(!1);
    return (
      Q(() => {
        let n = !1;
        return (
          e &&
            (async () => {
              (await co()) && (await uw());
              for (let i = 0; i < 60 * 10; i++) {
                if (n) return;
                if ((await We(1e3), await co())) {
                  location.reload();
                  return;
                }
              }
            })(),
          () => {
            n = !0;
          }
        );
      }, [e]),
      [e, t]
    );
  }
  function Xh({ count: e, maxCount: t = 1, style: n }) {
    return e <= t
      ? null
      : C("div", {
          className: "langs-tag",
          style: n,
          children: C("div", { className: "langs-tag-inner", children: e - t }),
        });
  }
  function $h({
    show: e,
    className: t,
    values: n,
    options: r,
    style: a,
    multiple: i = !1,
    hideSearch: o = !1,
    position: s,
    onClose: l,
    onChange: u,
    renderOption: c,
  }) {
    let { t: d } = ae(),
      [m, p] = V({}),
      g = ge(null),
      h = ge(null),
      x = le(
        () =>
          r.filter((y) => {
            let b = g.current?.value.trim();
            return b ? y.label?.includes(b) || y.value.includes(b) : !0;
          }),
        [r, g, m]
      ),
      f = le(() => {
        let y = r[0]?.label || "";
        for (let b of r) b.label && b.label.length > y.length && (y = b.label);
        return y;
      }, [r]);
    return (
      Q(() => {
        if (e) {
          let S = h.current?.getRootNode();
          S &&
            S.querySelectorAll(".imt-dropdown").forEach((T) => {
              T.style.display = "none";
            });
        }
        if (h.current) {
          let S = h.current.parentElement;
          if (S) {
            let T = S.getBoundingClientRect(),
              E = T.left,
              A = T.right,
              D = 100,
              F = globalThis.innerWidth,
              O = s;
            !O && E - D < 0 ? (O = "left") : !O && A + D > F && (O = "right"),
              O === "left"
                ? (h.current.style.left = "0px")
                : O === "right" && (h.current.style.right = "0px");
          }
          h.current.style.display = e ? "flex" : "none";
        }
        let y = () => {
            l?.();
          },
          v = h.current?.getRootNode() || document;
        return (
          v.addEventListener("click", y),
          () => {
            v.removeEventListener("click", y);
          }
        );
      }, [e, l]),
      C("div", {
        ref: h,
        className: `imt-dropdown ${t ?? ""}`,
        style: { zIndex: Ia - 10, ...a },
        children: [
          !o &&
            C("div", {
              className: "imt-search-box",
              children: [
                C(zk, { class: "imt-search-icon" }),
                C("input", {
                  ref: g,
                  type: "text",
                  placeholder: d("searchPlaceholder"),
                  onClick: (y) => {
                    y.stopPropagation();
                  },
                  onInput: (y) => {
                    y.stopPropagation(), p({});
                  },
                }),
              ],
            }),
          C("ul", {
            children: [
              x.map((y) =>
                C(
                  "li",
                  {
                    className: `${n.includes(y.value) ? "active" : ""} ${
                      y.disabled ? "disabled" : ""
                    }`,
                    onClick: (b) => {
                      if ((b.stopPropagation(), y.disabled)) return;
                      let v = [...n];
                      if (!v.includes(y.value))
                        i ? v.push(y.value) : v.unshift(y.value);
                      else {
                        if (i && v.length <= 1) return;
                        v.splice(v.indexOf(y.value), 1);
                      }
                      g.current && (g.current.value = ""),
                        u([...new Set(v)]),
                        i || l?.();
                    },
                    children: C("label", {
                      className: "checkbox-label",
                      value: y.value,
                      style: {
                        opacity:
                          i && n.length <= 1 && n.includes(y.value) ? 0.6 : 1,
                        cursor:
                          i && n.length <= 1 && n.includes(y.value)
                            ? "not-allowed"
                            : "pointer",
                      },
                      children: (() => {
                        let b =
                            i &&
                            !y.disableChecked &&
                            C(it, {
                              children: [
                                C("input", {
                                  type: "checkbox",
                                  checked: n.includes(y.value),
                                  disabled:
                                    i && n.length <= 1 && n.includes(y.value),
                                }),
                                C("span", { className: "checkbox-mark" }),
                              ],
                            }),
                          v = y.label;
                        return c ? c(y, b, v) : C(it, { children: [b, v] });
                      })(),
                    }),
                  },
                  y.value
                )
              ),
              C("li", {
                style: { opacity: 0, height: 1 },
                children: C("label", {
                  className: "checkbox-label",
                  children: [
                    i &&
                      C(it, {
                        children: [
                          C("input", { type: "checkbox" }),
                          C("span", { className: "checkbox-mark" }),
                        ],
                      }),
                    f,
                  ],
                }),
              }),
            ],
          }),
        ],
      })
    );
  }
  function G9({
    ctx: e,
    autoDetectLang: t,
    className: n,
    checkedLangs: r,
    popPosition: a,
    multiple: i = !1,
    onChange: o,
  }) {
    if (!e) return null;
    let [s, l] = V(!1),
      u = le(() => {
        let m = D0(dr, 12, e.config?.interfaceLanguage)
          .filter((p) => !r.includes(p))
          .filter((p) => p !== "auto");
        return (
          (m = [...r, ...m]),
          t && !m.find((p) => p === "auto") && m.unshift("auto"),
          m.map((p) => ({ code: p, name: Pr(p, e.config?.interfaceLanguage) }))
        );
      }, [e.config?.interfaceLanguage, t, r]),
      c = u.find((m) => r[0] === m.code)?.name,
      d = le(
        () => (!t || t === "auto" ? "" : Pr(t, e.config?.interfaceLanguage)),
        [t, e.config?.interfaceLanguage]
      );
    return C("div", {
      className: "select-languages",
      children: [
        C("div", {
          className: `select-languages-box ${n ?? ""}`,
          onClick: (m) => {
            m.stopPropagation(), l(!s);
          },
          children: [
            C("div", {
              className: "select-languages-text",
              children: (() => (d ? `${d} ${d && `- ${c}`}` : c))(),
            }),
            C(Xh, { count: r.length, maxCount: 1 }),
            C(ei, { class: "arrow-down" }),
          ],
        }),
        C($h, {
          show: s,
          values: r,
          multiple: i,
          position: a,
          onClose: () => l(!1),
          options: u.map((m) => ({ label: m.name, value: m.code })),
          onChange: (m) => {
            o(m);
          },
        }),
      ],
    });
  }
  function KL({
    ctx: e,
    popPosition: t,
    className: n,
    serviceItems: r,
    groupServiceItems: a,
    checkedServices: i,
    onChange: o,
  }) {
    if (!e) return null;
    let { t: s } = ae(),
      [l, u] = V(!1),
      c = r.filter((m) => i.includes(m.id)),
      d = le(() => {
        let m = a.flatMap((p) => {
          let g = p.services.map((h) => ({
            label: h.name,
            value: h.id,
            icon: h.icon,
            disabled: !1,
            group: h.config.group,
          }));
          return (
            g.length > 0 &&
              g.unshift({ label: p.name, value: p.id, icon: "", disabled: !0 }),
            g
          );
        });
        return (
          m.push({
            label: s("moreTranslationServices"),
            value: "more",
            icon: Hr("more"),
            disabled: !1,
            disableChecked: !0,
            onClick: () => {
              gr(!0, "#services", !1);
            },
          }),
          m
        );
      }, [a]);
    return C("div", {
      className: `select-services ${n ?? ""}`,
      children: [
        C("div", {
          className: "select-services-box",
          onClick: (m) => {
            m.stopPropagation(), u(!l);
          },
          children: [
            c
              .slice(0, 3)
              .map((m, p) =>
                C("div", {
                  className: "service-icon",
                  style: { zIndex: p, marginLeft: p > 0 ? -6 : 0 },
                  children: C("img", { src: m.icon, className: "icon" }),
                })
              ),
            C(Xh, {
              count: i.length,
              maxCount: 3,
              style: { zIndex: 4, marginLeft: -6 },
            }),
            C(ei, { class: "arrow-down" }),
          ],
        }),
        C($h, {
          show: l,
          hideSearch: !0,
          position: t,
          values: i,
          onClose: () => u(!1),
          options: d,
          multiple: !0,
          renderOption: (m, p, g) =>
            m.disabled
              ? C("div", {
                  style: {
                    width: "max-content",
                    display: "flex",
                    alignItems: "center",
                  },
                  children: g,
                })
              : C("div", {
                  style: {
                    width: "max-content",
                    display: "flex",
                    alignItems: "center",
                  },
                  onClick: (h) => {
                    m.onClick && (m.onClick?.(), u(!1));
                  },
                  children: [
                    p,
                    C("div", {
                      className: "service-icon",
                      style: {
                        display: m.icon ? "block" : "none",
                        marginRight: 8,
                      },
                      children: C("img", { src: m.icon }),
                    }),
                    g,
                    m.group === "pro" &&
                      C("img", {
                        src: Xa,
                        style: {
                          marginBottom: -2,
                          marginLeft: 6,
                          width: 20,
                          height: 20,
                        },
                      }),
                    m.group === "max" &&
                      C("img", {
                        src: Cu,
                        style: {
                          marginBottom: -2,
                          marginLeft: 6,
                          width: 20,
                          height: 20,
                        },
                      }),
                  ],
                }),
          onChange: (m) => {
            o(m);
          },
        }),
      ],
    });
  }
  function QL({
    ctx: e,
    selectServices: t,
    groupServiceItems: n,
    serviceItems: r,
    resultsRef: a,
    onTranslate: i,
  }) {
    let { t: o } = ae(),
      s = ge([]),
      l = ge([]),
      u = ge("auto"),
      c = ge("auto"),
      d = ge(null),
      [m, p] = V({}),
      g = Me(() => {
        let b = d.current?.value ?? "";
        te.storage.local.set({
          lastTranslate: {
            services: l.current,
            langs: s.current,
            sourceLang: u.current,
            autoDetectLang: c.current,
            text: b,
          },
        }),
          p({});
      }, [p]);
    Q(() => {
      if (!e) return;
      async function b() {
        let v = await te.storage.local.get("lastTranslate"),
          { lastTranslate: S } = v || {};
        if (S)
          (s.current = S.langs),
            (l.current = pi(e.config, S.services)),
            (u.current = S.sourceLang),
            (c.current = S.autoDetectLang),
            (d.current.value = S.text);
        else {
          (s.current = [e.targetLanguage]),
            (l.current = [e.translationService]);
          let T = pi(e.config, tm);
          T.includes(e.translationService) &&
            um(
              e,
              T.filter((E) => e.translationService != E)
            ).forEach(async (E) => {
              (await pg([E], e)) && (l.current.push(E), g());
            });
        }
        g();
      }
      b();
    }, [e]);
    let h = Me(() => {
      let b = u.current === "auto" ? c.current : u.current;
      Te({
        key: "translateTextInSidePanel",
        ctx: e,
        params: {
          target_language: s.current.length > 1 ? "multiple" : s.current[0],
          translation_service: l.current.length > 1 ? "multiple" : l.current[0],
        },
      }),
        i(l.current, b, s.current, d.current?.value ?? ""),
        g();
    }, [i]);
    Q(() => {
      e &&
        l.current.length > t.length &&
        t.length &&
        ((l.current = r.filter((b) => t.includes(b.id)).map((b) => b.id)), g());
    }, [e, r, t, l]);
    let x = (b, v = !1) => {
        let S = u.current == "auto" ? c.current : u.current,
          T = s.current[0];
        (u.current = T),
          (c.current = "auto"),
          (s.current = [S == "auto" ? e.config.interfaceLanguage : S]);
        let E = a.current?.find(
          (A) => A.targetLang === u.current && A.service === l.current[0]
        );
        E && !v && (d.current.value = E.translateText),
          Te({
            key: "exchangeLangInSidePanel",
            ctx: e,
            params: { trigger: "manual" },
          }),
          g();
      },
      f = Me(
        qt(async (b) => {
          if (u.current != "auto" && !b) return;
          let v = u.current == "auto" ? c.current : u.current;
          v = v == "auto" ? e.config.targetLanguage : v;
          let S = d.current?.value ?? "";
          if (!S.trim()) {
            (u.current = "auto"), (c.current = "auto"), g();
            return;
          }
          let T = await je({ text: S });
          if (T == "auto") {
            let E = await mu([S], !0);
            E && E.length && (T = E[0].lang);
          }
          if (
            ((c.current = T),
            b && (u.current = "auto"),
            s.current.includes(T) &&
              (!s.current.includes(v) || T == v) &&
              (s.current.splice(
                s.current.findIndex((E) => E == T),
                1
              ),
              T != v && s.current.unshift(v),
              Te({
                key: "exchangeLangInSidePanel",
                ctx: e,
                params: { trigger: "auto" },
              })),
            s.current.length == 0)
          ) {
            let E = "auto";
            T != e.config.interfaceLanguage
              ? (E = e.config.interfaceLanguage)
              : T == "en"
              ? (E = "zh-CN")
              : (E = "en"),
              (s.current = [E]);
          }
          g(), b && h();
        }, 200),
        [u, e.config.interfaceLanguage]
      );
    Q(() => {
      f();
      let b = () => {
          setTimeout(() => f(!0), 0);
        },
        v = (D) => {
          D.code === "Enter" && (D.shiftKey || (D.preventDefault(), h()));
        },
        S = !1,
        T = () => {
          S = !0;
        },
        E = (D) => {
          (S = !1), f();
        },
        A = (D) => {
          S || f();
        };
      return (
        d.current?.addEventListener("compositionstart", T),
        d.current?.addEventListener("compositionend", E),
        d.current?.addEventListener("keydown", v),
        d.current?.addEventListener("paste", b),
        d.current?.addEventListener("input", A),
        () => {
          d.current?.removeEventListener("compositionstart", T),
            d.current?.removeEventListener("compositionend", E),
            d.current?.removeEventListener("input", A),
            d.current?.removeEventListener("keydown", v),
            d.current?.removeEventListener("paste", b);
        }
      );
    }, [e, f]);
    let y = () => {
      (d.current.value = ""), f();
    };
    return C("div", {
      className: "translate-area",
      children: [
        C(KL, {
          ctx: e,
          popPosition: "left",
          serviceItems: r,
          checkedServices: l.current,
          groupServiceItems: n,
          onChange: (b) => {
            (l.current = b), g();
          },
        }),
        C("div", {
          className: "translate-main",
          children: [
            C("header", {
              children: [
                C(G9, {
                  ctx: e,
                  popPosition: "left",
                  className: "source-language",
                  checkedLangs: [u.current],
                  multiple: !1,
                  autoDetectLang: c.current,
                  onChange: (b) => {
                    b.length &&
                      ((u.current = b[0]),
                      u.current != "auto" ? (c.current = "auto") : f(),
                      g());
                  },
                }),
                C("div", {
                  className: "convert-icon",
                  onClick: x,
                  children: C(jk, {}),
                }),
                C(G9, {
                  ctx: e,
                  popPosition: "right",
                  className: "target-languages",
                  checkedLangs: s.current,
                  multiple: !0,
                  onChange: (b) => {
                    (s.current = b), g();
                  },
                }),
              ],
            }),
            C("textarea", {
              ref: d,
              rows: 5,
              placeholder: o("pasteTextPlaceholder"),
            }),
            C("footer", {
              children: [
                C("div", {
                  className: "icon delete-button",
                  onClick: y,
                  children: C(Ok, {}),
                }),
                C("div", { style: { flex: 1 } }),
                C("button", {
                  className: "translate-button",
                  onClick: h,
                  children: [o("translate"), C(_k, {})],
                }),
              ],
            }),
          ],
        }),
      ],
    });
  }
  async function YL(e) {
    let t = await te.storage.local.get("lastTranslate"),
      { lastTranslate: n } = t || {};
    return pi(e.config, n?.services || [e.translationService]);
  }
  async function ZL() {
    let e = await te.storage.local.get("lastTranslate"),
      { lastTranslate: t } = e || {};
    return t;
  }
  var JL = ({ refreshKey: e, ctx: t }) => {
    let [n, r] = V(null),
      [a, i] = V(!1),
      [o, s] = V(null);
    return (
      Q(() => {
        i(!0),
          s(null),
          H4()
            .then(r)
            .catch(s)
            .finally(() => {
              i(!1);
            });
      }, [e, t]),
      { rewardData: n, isLoading: a, error: o }
    );
  };
  function V9(e, t = !1) {
    if (!t) {
      try {
        if (!J(!1, !0) && $n()) {
          te.tabs.create({ url: `${e}` });
          return;
        }
        globalThis.open(`${e}`, "_blank");
      } catch {
        globalThis.open(`${e}`, "_blank");
      }
      return;
    }
    try {
      if (!J(!1, !0) && $n()) {
        te.tabs.create({
          url: `${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,
        });
        return;
      }
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,
        "_blank"
      );
    } catch {
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=reward_center`,
        "_blank"
      );
    }
  }
  async function HX() {
    let { userValue: e, localValue: t } = await Qs("rewardCenterOpenTime");
    e || t || (await qu("rewardCenterOpenTime", Date.now().toString()));
  }
  function W9({
    visible: e,
    onClose: t,
    ctx: n,
    refreshKey: r = 0,
    setSettings: a,
  }) {
    HX();
    let [i, o] = V(!1),
      [s, l] = V(!1),
      [u, c] = V({}),
      [d, m] = V(0),
      { t: p } = ae(),
      [g, h] = V(""),
      x = `${r}_${d}`,
      { rewardData: f, isLoading: y, error: b } = JL({ refreshKey: x, ctx: n }),
      v = () => {
        m((B) => B + 1);
      };
    Q(() => {
      if (e) {
        o(!0), l(!1);
        let B = setTimeout(() => {
          l(!0);
        }, 10);
        return m((j) => j + 1), () => clearTimeout(B);
      } else {
        l(!1);
        let B = setTimeout(() => {
          o(!1);
        }, 400);
        return () => clearTimeout(B);
      }
    }, [e]);
    let S = (B) => {
        B.target === B.currentTarget && t();
      },
      T = () => {
        e || o(!1);
      };
    function E(B) {
      let j = Le();
      return (
        {
          translate_line_1: `${Pd}reward-task/?showUserGuide=true&guideType=selection-translate`,
          translate_web_1: `${Pd}reward-task/?showUserGuide=true&guideType=float-ball`,
          translate_web_2: `${Pd}reward-task/?showUserGuide=true&guideType=translate-service`,
          translate_pdf_1: `${j.TRANSLATE_FILE_URL}`,
          translate_pdf_2: `${j.PDF_PRO_URL}`,
          translate_video_1: `${zf}`,
          translate_video_2: `${zf}`,
        }[B] || ""
      );
    }
    let A = async (B) => {
        if (
          (I.debug("Task start:", B.taskConfig.taskKey),
          J(!1, !0) && B.taskConfig.taskKey === "translate_line_1")
        )
          return;
        let j = await Z8(),
          R = E(B.taskConfig.taskKey);
        if (!j) {
          V9(`${el}&return_url=${encodeURIComponent(R)}`, !1);
          return;
        }
        if (B.enabled && !B.completed) {
          let M = B.taskConfig.taskKey;
          Te({
            key: "reward_center_start_task",
            ctx: n,
            params: { trigger: B.taskConfig.taskKey },
          }),
            M === "translate_line_1" &&
              (n.config.generalRule.selectionTranslation?.enable ||
                a((w) => ({
                  ...w,
                  generalRule: {
                    ...w.generalRule,
                    "selectionTranslation.add": {
                      ...w.generalRule?.["selectionTranslation.add"],
                      enable: !0,
                    },
                  },
                }))),
            M === "translate_video_1" && jb({ type: "video-subtitle" }),
            M === "translate_video_2" && jb({ type: "ai-subtitle" }),
            V9(R);
          return;
        }
        if (B.completed && !B.taken) {
          if (g) return;
          h(B.taskConfig.taskKey),
            Te({
              key: "reward_center_claim",
              ctx: n,
              params: { trigger: B.taskConfig.taskKey },
            });
          try {
            await Y8(B.taskConfig.taskKey), v(), J8(), h("");
          } catch (M) {
            I.error("takeReward error", M);
          } finally {
            h("");
          }
        }
      },
      D = (B, j = !1) => {
        switch (B) {
          case "ai_token":
            return j
              ? p("rewardCenter.reward.ai_token") +
                  p("rewardCenter.reward.description")
              : p("rewardCenter.reward.ai_token");
          case "pdf_token":
            return p("rewardCenter.reward.pdf_token");
          case "video_token":
            return p("rewardCenter.reward.video_token");
          default:
            return B;
        }
      },
      F = (B, j, R = !0) => {
        let M = B.toLocaleString(),
          P = R ? p("rewardCenter.reward.unit_pdf_token") : "",
          w = R ? p("rewardCenter.reward.unit_video_token") : "";
        return j === "ai_token"
          ? M
          : j === "video_token"
          ? `${M}${w}`
          : j === "pdf_token"
          ? `${M}${P}`
          : M;
      },
      O = (B) => {
        let { taskConfig: j, taken: R, completed: M, enabled: P } = B,
          w = j.rewards[0],
          L = j.groupName === "\u9AD8\u7EA7\u4EFB\u52A1",
          U = M && R,
          z = !P && !M && !R,
          G = p("rewardCenter.task.start"),
          q = "reward-task-button",
          W = !1;
        return (
          M && !R
            ? ((G = p("rewardCenter.task.claim")), (q += " claim"))
            : M && R
            ? ((G = p("rewardCenter.task.claimed")), (q += " completed"))
            : !P && !M && !R
            ? ((G = p("rewardCenter.task.start")), (q += " disabled"))
            : L &&
              P &&
              !M &&
              !R &&
              ($8(B, f) || ((G = p("rewardCenter.task.start")), (W = !0))),
          C(
            "div",
            {
              className: `reward-task-item ${z ? "unavailable" : ""} ${
                U ? "completed" : ""
              }`,
              children: [
                C("div", {
                  className: "reward-task-content",
                  children: [
                    C("div", {
                      className: "reward-task-title",
                      children: p(`rewardCenter.task.${j.taskKey}`),
                    }),
                    w &&
                      C("div", {
                        className: "reward-task-reward",
                        children: [
                          p("rewardCenter.reward.get"),
                          "  ",
                          C("span", {
                            className: `${
                              L ? "reward-amount-advanced" : "reward-amount"
                            }`,
                            children: F(w.rewardAmount, w.rewardType),
                          }),
                          " ",
                          D(w.rewardType, !0),
                        ],
                      }),
                  ],
                }),
                U
                  ? C(sM, {})
                  : J(!1, !0) && j.taskKey === "translate_line_1"
                  ? C(Tt, {
                      text: p("rewardCenter.task.translate_line_1.warning", {
                        1: {
                          tag: "a",
                          style: "color: #EA4C89;",
                          href: Ff + "?utm_campaign=reward_center",
                          target: "_blank",
                        },
                      }),
                      position: "left",
                      tipStyle: {
                        width: "150px",
                        maxWidth: "150px",
                        whiteSpace: "normal",
                        wordBreak: "break-word",
                      },
                      children: C("button", {
                        className: `${q} disabled`,
                        disabled: !0,
                        children: G,
                      }),
                    })
                  : W
                  ? C(Tt, {
                      text: p("rewardCenter.task.unclaimedWarning"),
                      position: "left",
                      tipStyle: {
                        width: "150px",
                        maxWidth: "150px",
                        whiteSpace: "normal",
                        wordBreak: "break-word",
                      },
                      children: C("button", {
                        className: q,
                        onClick: () => A(B),
                        disabled: z,
                        children: [G, g === j.taskKey ? C(XL, {}) : null],
                      }),
                    })
                  : C("button", {
                      className: q,
                      onClick: () => A(B),
                      disabled: z,
                      children: [G, g === j.taskKey ? C(XL, {}) : null],
                    }),
              ],
            },
            j.taskKey
          )
        );
      },
      _ = (B) => {
        let j = B.groupName === "\u9AD8\u7EA7\u4EFB\u52A1",
          R = p(j ? "rewardCenter.task.level2" : "rewardCenter.task.level1"),
          M = B.taskItems.filter((w) => !(w.completed && w.taken)),
          P = B.taskItems.filter((w) => w.completed && w.taken);
        return C(
          "div",
          {
            className: "reward-center-card",
            children: [
              C("div", {
                className: "reward-card-title",
                children: [
                  j ? C(oM, {}) : C(iM, {}),
                  R,
                  j &&
                    C("span", {
                      className: "reward-task-subtitle",
                      children: p("rewardCenter.task.level2.description"),
                    }),
                ],
              }),
              C("div", {
                className: "reward-card-content",
                children: [
                  M.map((w) => O(w)),
                  P.length > 0 &&
                    C(it, {
                      children: [
                        C("div", {
                          className: "completed-tasks-divider clickable",
                          onClick: () =>
                            c((w) => ({
                              ...w,
                              [B.groupName]: !w[B.groupName],
                            })),
                          children: [
                            C("span", {
                              className: "completed-tasks-text",
                              children: p("rewardCenter.task.completed"),
                            }),
                            C("span", {
                              className: `completed-tasks-arrow ${
                                u[B.groupName] ? "expanded" : ""
                              }`,
                              children: C(ei, {}),
                            }),
                          ],
                        }),
                        u[B.groupName] && P.map((w) => O(w)),
                      ],
                    }),
                ],
              }),
            ],
          },
          B.groupName
        );
      };
    return !e && !i
      ? null
      : C(it, {
          children: C("div", {
            className: `reward-center-overlay ${s ? "visible" : ""}`,
            onClick: S,
            children: C("div", {
              className: `reward-center-drawer ${s ? "visible" : ""}`,
              onTransitionEnd: T,
              children: [
                C("div", {
                  className: "reward-center-fixed-header",
                  children: [
                    C("div", {
                      className: "reward-center-header",
                      children: [
                        C("div", {
                          className: "reward-center-header-left",
                          children: [
                            C(Du, {}),
                            C("div", {
                              className: "reward-center-title",
                              children: p("rewardCenter.title"),
                            }),
                          ],
                        }),
                        C(Us, {
                          class: "reward-center-close-icon",
                          onClick: t,
                        }),
                      ],
                    }),
                    C("div", {
                      className: "reward-center-description",
                      children: p("rewardCenter.description"),
                    }),
                  ],
                }),
                C("div", {
                  className: "reward-center-scrollable-content",
                  children: [
                    !f && y && C("div", { className: "reward-center-loading" }),
                    b &&
                      C("div", {
                        className: "reward-center-error",
                        children: b.message,
                      }),
                    !b &&
                      f &&
                      C(it, {
                        children: [
                          C("div", {
                            className:
                              "reward-center-card reward-progress-container",
                            children: [
                              C("div", {
                                className: "reward-card-title",
                                children: p("rewardCenter.progress"),
                              }),
                              C("div", {
                                className: "reward-card-content",
                                children: f?.rewardViews.map((B) => {
                                  let j =
                                    B.rewardTotalAmount > 0
                                      ? (B.completedAmount /
                                          B.rewardTotalAmount) *
                                        100
                                      : 0;
                                  return C(
                                    "div",
                                    {
                                      className: "reward-progress-item",
                                      children: C("div", {
                                        className: "reward-progress-info",
                                        children: [
                                          C("div", {
                                            className: "reward-progress-label",
                                            children: D(B.rewardType),
                                          }),
                                          C("div", {
                                            className: "reward-progress-right",
                                            children: [
                                              C("div", {
                                                className:
                                                  "reward-progress-value",
                                                children: [
                                                  F(
                                                    B.completedAmount,
                                                    B.rewardType,
                                                    !1
                                                  ),
                                                  "/",
                                                  F(
                                                    B.rewardTotalAmount,
                                                    B.rewardType,
                                                    !1
                                                  ),
                                                ],
                                              }),
                                              C("div", {
                                                className:
                                                  "reward-progress-bar",
                                                children: C("div", {
                                                  className:
                                                    "reward-progress-fill",
                                                  style: { width: `${j}%` },
                                                }),
                                              }),
                                            ],
                                          }),
                                        ],
                                      }),
                                    },
                                    B.rewardType
                                  );
                                }),
                              }),
                            ],
                          }),
                          f?.groupViews.map((B) => _(B)),
                        ],
                      }),
                    C("div", {
                      className: "reward-center-footer",
                      children: [
                        C($g, {}),
                        C("div", {
                          className: "reward-center-footer-text",
                          onClick: () => V9(Yd, !0),
                          children: p("rewardCenter.help"),
                        }),
                      ],
                    }),
                  ],
                }),
              ],
            }),
          }),
        });
  }
  var XL = () => C("div", { className: "reward-task-loading" });
  var ef = null;
  function K9() {
    let {
        config: e,
        ctx: t,
        ctxRef: n,
        settingsHookValue: r,
        refreshCtx: a,
      } = uP(),
      [i, o, s, l, u] = r,
      [c, d] = V("auto"),
      [m, p] = V([]),
      [g, h] = V([]),
      { expand: x, setExpand: f } = WL(),
      [y, b] = V(!1),
      [v, S] = V(0),
      T = () => {
        y ||
          Te({
            key: "reward_center_open",
            ctx: t,
            params: { trigger: "side_panel" },
          }),
          b(!y);
      },
      { t: E } = ae(),
      A = e && cn(e.rtlLanguages ?? [], e.interfaceLanguage),
      [D, F] = V([]);
    Q(() => {
      async function q() {
        if (!t) return;
        let W = await YL(t),
          Z = (await tp(t, "translationService", "zh-CN", W)) || [];
        F(Z);
      }
      q();
    }, [t]);
    let O = le(() => (t ? D.filter((q) => g.includes(q.id)) : []), [t, D, g]),
      _ = le(() => np(D, E), [D]),
      [B, j] = V([]),
      R = ge([]);
    R.current = B;
    let M = Me(
        async (q, W, Z, ne) => {
          t &&
            (d(W),
            p(Z),
            h(q),
            j([]),
            H9({
              ctx: t,
              sourceLang: W,
              text: ne,
              services: q,
              targetLangs: Z,
              callback: (fe) => {
                let Ee = R.current.find(
                  ($) =>
                    $.service === fe.service && $.targetLang === fe.targetLang
                );
                Ee
                  ? ((Ee.loading = !1),
                    (Ee.translateText = fe.translateText),
                    (Ee.error = fe.error),
                    j([...R.current]))
                  : (R.current.push(fe), j([...R.current]));
              },
            }));
        },
        [t, R]
      ),
      P = Me(
        (q, W) => {
          let Z = R.current.find(
            (ne) => ne.service === q && ne.targetLang === W
          );
          !t ||
            !Z ||
            H9({
              ctx: t,
              sourceLang: c,
              text: Z.text,
              services: [q],
              targetLangs: [W],
              callback: (ne) => {
                (Z.translateText = ne.translateText),
                  (Z.loading = ne.loading),
                  (Z.error = ne.error),
                  j([...R.current]);
              },
            });
        },
        [t, R, c]
      ),
      w = Me(
        (q) => {
          h(g.filter((W) => W !== q));
        },
        [g]
      ),
      [L, U] = V(!1);
    Q(() => {
      document.body.style.marginRight && U(!0);
    }, []);
    let z = Me(
      (q) => {
        let W;
        typeof q.detail == "string"
          ? (W = JSON.parse(q.detail))
          : (W = q.detail);
        let { payload: Z } = W,
          { method: ne, data: fe } = Z;
        switch ((I.debug("side panel received message", ne, fe || " "), ne)) {
          case "openRewardCenter":
            y
              ? (b(!1), kw())
              : (b(!0),
                Te({
                  key: "reward_center_open",
                  ctx: n.current,
                  params: { trigger: fe?.trigger || "float_ball" },
                }));
            break;
          case "refreshRewardCenter":
            b(!0), S((Ee) => Ee + 1);
            break;
          case "updateContext":
            a();
            break;
        }
      },
      [y, a]
    );
    function G() {
      ef && globalThis.document.removeEventListener(rc, ef),
        (ef = z),
        globalThis.document.addEventListener(rc, ef);
    }
    return (
      Q(() => {
        location.href.includes("openRewardCenter=true") &&
          (b(!0),
          setTimeout(() => {
            Te({
              key: "reward_center_open",
              ctx: n.current,
              params: { trigger: "popup" },
            });
          }, 1e3)),
          G();
      }, []),
      t
        ? C("div", {
            className: "notranslate imt-side-panel",
            dir: A ? "rtl" : "ltr",
            children: [
              L &&
                C(Us, {
                  class: "panel-close-icon",
                  onClick: () => {
                    Eo(t, "panel");
                  },
                }),
              C(QL, {
                ctx: t,
                selectServices: g,
                serviceItems: D,
                groupServiceItems: _,
                resultsRef: R,
                onTranslate: M,
              }),
              C(GX, {
                ctx: t,
                config: e,
                serviceItems: O,
                langs: m,
                results: B,
                onDeleteService: w,
                onRefresh: P,
              }),
              C("div", { style: { height: x ? 160 : 50 } }),
              C(qX, { ctx: t, expand: x, setExpand: f, toggleRewardCenter: T }),
              C(W9, {
                visible: y,
                onClose: T,
                ctx: t,
                refreshKey: v,
                setSettings: o,
              }),
            ],
          })
        : null
    );
  }
  function qX({ ctx: e, expand: t, setExpand: n, toggleRewardCenter: r }) {
    let { t: a } = ae(),
      [i, o] = V(!1),
      [s, l] = V(!1),
      u = Me(() => {
        o(!0),
          setTimeout(() => {
            o(!1);
          }, 1200);
      }, []);
    Q(() => {
      (async () => {
        let { userValue: b, localValue: v } = await Qs("rewardCenterClicked"),
          S = b || v;
        l(!!S), S || u();
      })();
    }, []),
      Q(() => {
        let b = setInterval(() => {
          s || $m(e) ? clearInterval(b) : u();
        }, 2e3);
        return () => clearTimeout(b);
      }, [u, e, s]);
    let c = Me(() => {
        o(!1), qu("rewardCenterClicked", !0), l(!0);
      }, []),
      [d, m] = q9(),
      p = e.config?.sidePanel || {},
      g = $m(e),
      h = async () => {
        let y = await ZL();
        if (y) {
          let b = y.langs[0],
            v = encodeURIComponent(y.text.trim()),
            S = `${Yo}text#${y.sourceLang}/${b}/${v}`;
          Xu(S, "translate_text");
        } else Xu(Yo + "text", "translate_text");
      },
      x = (p.i18n?.[e.config.interfaceLanguage] || p.i18n?.en)?.upgradeTitle,
      f = p.upgradeUrl;
    return C("div", {
      className: "side-footer",
      children: [
        C("div", {
          className: "footer-expand",
          style: { marginBottom: t ? 4 : -12 },
          onClick: () => n(!t),
          children: t ? C(ei, {}) : C(dM, {}),
        }),
        C("div", {
          className: "footer-area",
          style: { display: t ? "flex" : "none" },
          children: [
            C("div", { className: "divider" }),
            C("span", {
              className: "footer-area-title",
              children: a("alsoTranslate"),
            }),
            C("div", {
              className: "footer-area-buttons-wrapper",
              children: [
                C("div", {
                  className: "footer-area-button",
                  onClick: () => Xu(Yo, "translate_file"),
                  children: [C(mM, {}), a("translateFile")],
                }),
                C("div", {
                  className: "footer-area-button",
                  onClick: () => Xu(Yo + "image", "translate_image"),
                  children: [C(pM, {}), a("translateImage")],
                }),
                C("div", {
                  className: "footer-area-button",
                  onClick: h,
                  children: [C(gM, {}), a("translateText")],
                }),
              ],
            }),
          ],
        }),
        C("div", {
          className: "side-footer-preview",
          children: [
            e.isPro
              ? C("div", { className: "empty-space" })
              : C("a", {
                  className: "upgrade",
                  target: "_blank",
                  href: f,
                  onClick: () => m(!0),
                  children: [C(lM, {}), C("span", { children: x })],
                }),
            C("div", {
              className: "action-buttons",
              children: [
                !g &&
                  C("div", {
                    className: "action-icon-wrapper",
                    onClick: () => {
                      r(), c();
                    },
                    children: [
                      C("div", {
                        className: `action-icon${i ? " bounce-animate" : ""}`,
                        children: C(Du, {}),
                      }),
                      C("span", {
                        className: "reward-center-text",
                        children: a("rewardCenter.title"),
                      }),
                    ],
                  }),
                C("div", {
                  className: "action-icon",
                  onClick: () => Xu(Yd, "help_center"),
                  children: C($g, {}),
                }),
                C("div", {
                  className: "action-icon",
                  onClick: () => gr(!1, "", !1),
                  children: C(Xg, {
                    style: { width: 18, height: 18 },
                    fillColor: "#999",
                  }),
                }),
              ],
            }),
          ],
        }),
      ],
    });
  }
  function Xu(e, t) {
    try {
      if (!J(!1, !0) && $n()) {
        te.tabs.create({
          url: `${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,
        });
        return;
      }
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,
        "_blank"
      );
    } catch {
      globalThis.open(
        `${e}?utm_source=extension&utm_medium=extension&utm_campaign=side_footer_${t}`,
        "_blank"
      );
    }
  }
  function GX({
    ctx: e,
    config: t,
    serviceItems: n,
    langs: r,
    results: a,
    onDeleteService: i,
    onRefresh: o,
  }) {
    let { t: s } = ae(),
      [l, u] = V(!1),
      c = (m) => {
        Gh(m),
          u(!0),
          setTimeout(() => {
            u(!1);
          }, 2e3);
      },
      d = (m, p) => {
        let g = new AbortController();
        Kh({ text: p, lang: m, provider: "WebSpeech", signal: g.signal });
      };
    return t
      ? C("div", {
          className: "results-container",
          children:
            a.length > 0 &&
            n
              .map((m, p) => {
                let g = r.map((h) => {
                  let x = a.find(
                      (v) => v.service === m.id && v.targetLang === h
                    ),
                    f = cn(e.config.rtlLanguages, h),
                    y = x?.translateText,
                    b = x?.loading || (!x?.translateText && !x?.error);
                  return C("div", {
                    className: "result-container",
                    children: [
                      y &&
                        !x.error &&
                        C("pre", {
                          dir: f ? "rtl" : "ltr",
                          children: y.trim(),
                        }),
                      b &&
                        C("div", {
                          className: "skeleton-loader",
                          children: [
                            C("div", { className: "skeleton-text-line short" }),
                            C("div", {
                              className: "skeleton-text-line medium",
                            }),
                          ],
                        }),
                      C(VX, {
                        ctx: e,
                        error: x?.error,
                        onRefresh: () => o(m.id, h),
                      }),
                      C("div", {
                        className: "result-lang",
                        children: [
                          C(Tt, {
                            text: s(
                              l
                                ? "selectionTranslationCopySuccess"
                                : "selectionTranslationCopy"
                            ),
                            children: C("div", {
                              className: "icon",
                              onClick: () => c(y ?? ""),
                              children: C(Zg, {}),
                            }),
                          }),
                          C("div", {
                            className: "icon",
                            onClick: () => d(h, y),
                            children: C(Mu, {}),
                          }),
                          C("div", { style: { flex: 1 } }),
                          Pr(h, t?.interfaceLanguage, !1, !0),
                        ],
                      }),
                    ],
                  });
                });
                return C("div", {
                  className: "service-wrapper-container",
                  children: [
                    C("div", {
                      className: "service-wrapper",
                      children: [
                        C("img", { src: m.icon, className: "icon" }),
                        C("span", { children: m.name }),
                        C("div", {
                          className: "close-icon",
                          onClick: () => i(m.id),
                          children: C(Us, {}),
                        }),
                      ],
                    }),
                    g,
                    C("div", {
                      className: "divider",
                      hidden: p === n.length - 1,
                    }),
                  ],
                });
              })
              .flat(),
        })
      : null;
  }
  function VX({ ctx: e, error: t, onRefresh: n }) {
    let { t: r } = ae(),
      [a, i] = q9();
    if (!e || !t) return null;
    let o = t.uiConfig(e);
    return ["upgrade", "login"].includes(o.action || "")
      ? C("div", {
          className: "error-result error-info",
          children: [
            C("div", {
              className: "error-message",
              dangerouslySetInnerHTML: { __html: o.errMsg ?? "" },
            }),
            C("div", {
              className: "upgrade-button",
              onClick: () => {
                Xu(hn, "upgrade"), i(!0);
              },
              children: [
                C("svg", {
                  width: "20",
                  height: "21",
                  viewBox: "0 0 20 21",
                  fill: "none",
                  xmlns: "http://www.w3.org/2000/svg",
                  children: C("path", {
                    d: "M6.7482 17.3325C7.46127 17.5702 7.69894 16.9165 7.40183 16.6788C6.45109 16.0252 5.97572 14.8368 6.33226 13.7672C6.68879 12.5788 7.63953 12.1034 7.63953 10.3208C7.63953 10.3208 8.82795 11.2121 8.59027 12.5788C9.77869 11.2121 9.2439 9.42947 9.00623 8.59756C12.0367 10.2019 14.6512 13.7078 11.6208 16.6788C11.2642 16.9759 11.6208 17.5107 12.1556 17.3325C20.4151 12.6382 14.1759 5.62648 13.1063 4.79459C13.4628 5.62648 13.5223 6.93375 12.8092 7.5874C11.5613 2.89312 8.53088 1.94238 8.53088 1.94238C8.8874 4.37865 7.1642 6.9932 5.5598 9.01352C5.50039 8.00336 5.44098 7.34973 4.96559 6.4584C4.84676 8.18162 3.48006 9.60772 3.12355 11.3904C2.64818 13.7672 3.48008 15.4904 6.74826 17.3325L6.7482 17.3325Z",
                    fill: "#FFC736",
                  }),
                }),
                r("upgradeToPro"),
              ],
            }),
          ],
        })
      : C("div", {
          className: "error-result error-warning",
          children: [
            C("div", {
              className: "error-message",
              dangerouslySetInnerHTML: { __html: o.errMsg ?? "" },
            }),
            C("div", {
              className: "retry-button",
              onClick: n,
              children: C(Nk, {}),
            }),
          ],
        });
  }
  var nf = !1,
    tf = !1,
    rf = 150,
    Q9 = 362,
    Y9 = "mock-side-panel-width",
    Z9 = "cursor-mock-side-panel-host";
  function WX() {
    return nf;
  }
  async function KX(e) {
    if (globalThis.top !== window || tf) return;
    let n = `${await JX()}px`,
      r = "6px",
      a = document.getElementById(Z9),
      i = Ae.IMMERSIVE_TRANSLATE_SIDE_PANEL_CSS,
      o = Ae.IMMERSIVE_TRANSLATE_REWARD_CENTER_CSS;
    if (a) a.remove(), $L(), (nf = !1);
    else {
      (nf = !0),
        (tf = !0),
        (a = document.createElement("div")),
        (a.id = Z9),
        a.setAttribute(
          "style",
          `
      all: initial;
      position: fixed;
      top: 0px;
      right: 0px;
      width: ${n};
      height: 100%;
      z-index: ${Ia};
      background-color: white;
      display: flex;
      transform: translateX(100%);
      transition: transform ${rf}ms ease-out;
      `
        ),
        document.documentElement.appendChild(a);
      let s = a.attachShadow({ mode: "open" }),
        l = document.createElement("div");
      Object.assign(l.style, {
        width: r,
        height: "100%",
        cursor: "col-resize",
        backgroundColor: "transparent",
        borderRight: "1px solid #d1d1d1",
        borderLeft: "1px solid #d1d1d1",
        boxSizing: "border-box",
      });
      let u = document.createElement("div");
      (u.id = "mount"),
        Object.assign(u.style, {
          flexGrow: "1",
          height: "100%",
          overflow: "auto",
          backgroundColor: "white",
        });
      let c = document.createElement("style");
      c.textContent = i;
      let d = document.createElement("style");
      (d.textContent = o),
        Na(
          C(it, {
            children: C(Po, { lang: e.interfaceLanguage, children: C(K9, {}) }),
          }),
          u
        ),
        s.appendChild(c),
        s.appendChild(d),
        s.appendChild(l),
        s.appendChild(u),
        requestAnimationFrame(() => {
          a &&
            ((a.style.transform = "translateX(0)"),
            ZX(n),
            a.addEventListener(
              "transitionend",
              () => {
                tf = !1;
              },
              { once: !0 }
            ));
        });
      let m = !1,
        p = 0,
        g = 0;
      l.addEventListener("mousedown", (f) => {
        (m = !0),
          (p = f.clientX),
          (g = a.offsetWidth),
          (u.style.pointerEvents = "none"),
          (a.style.transition = "none"),
          f.preventDefault(),
          document.addEventListener("mousemove", h),
          document.addEventListener("mouseup", x);
      });
      let h = (f) => {
          if (!m || !a) return;
          let y = f.clientX - p,
            b = g - y,
            v = globalThis.innerWidth * (2 / 3);
          b > v ? (b = v) : b < Q9 && (b = Q9),
            XX(b),
            (a.style.width = `${b}px`),
            YX(`${b}px`);
        },
        x = () => {
          m &&
            ((m = !1),
            (u.style.pointerEvents = "auto"),
            a && (a.style.transition = `transform ${rf}ms ease-out`),
            document.removeEventListener("mousemove", h),
            document.removeEventListener("mouseup", x));
        };
    }
  }
  async function QX() {
    let e = document.getElementById(Z9);
    e && (e.remove(), $L(), (nf = !1));
  }
  va.toggleMockSidePanel = KX;
  va.getIsOpenSidePanel = WX;
  va.closeMockSidePanel = QX;
  function YX(e) {
    let t = document.body;
    t.style.setProperty("margin-right", e, "important"),
      t.style.setProperty("width", "unset", "important");
  }
  function ZX(e) {
    let t = document.body;
    t.style.setProperty(
      "transition",
      `margin-right ${rf}ms ease-out`,
      "important"
    ),
      t.style.setProperty("margin-right", e, "important"),
      t.style.setProperty("width", "unset", "important"),
      setTimeout(() => {
        t.style.removeProperty("transition"), (tf = !1);
      }, rf);
  }
  function $L() {
    let e = document.body;
    e.style.removeProperty("margin-right"),
      e.style.removeProperty("width"),
      e.style.removeProperty("transition");
  }
  async function JX() {
    let t = (await te.storage.local.get(Y9))[Y9] || Q9;
    return parseInt(t);
  }
  async function XX(e) {
    await te.storage.local.set({ [Y9]: e.toString() });
  }
  var be = null,
    af = {},
    $u,
    eR = !1;
  async function zh() {
    Xl.clearStrictTicks();
    let e = await $e(Ie(), {}),
      t = await or();
    I.debug("init page ctx", e);
    let n = await h$(e);
    if ((n && (e = n), vk(e), O9(e), e.rule.pageType == "subtitleBuilder")) {
      R9();
      return;
    }
    if (e.rule.pageType == "ebookBuilder") {
      wL();
      return;
    }
    if (
      ($u || ($u = e.state.translationTheme),
      e.rule.urlChangeDelay && (await We(e.rule.urlChangeDelay)),
      e.rule.waitForSelectors &&
        e.rule.waitForSelectors.length > 0 &&
        (await d$(e.rule.waitForSelectors, e.rule.waitForSelectorsTimeout)),
      e.rule.isInjectOptionsUrl)
    ) {
      let o = Li(e.config.useOnlineOptions),
        s = document.createElement("meta");
      (s.name = N + "-options-url"), (s.content = o);
      try {
        document.head?.appendChild?.(s);
      } catch (l) {
        I.warn("inject options url failed", l);
      }
    }
    if (
      (e.rule.globalMeta &&
        Object.keys(e.rule.globalMeta).forEach((s) => {
          let l = document.createElement("meta");
          (l.name = s),
            (l.content = e.rule.globalMeta[s]),
            document.head?.appendChild?.(l);
        }),
      e.rule.initialGlobalAttributes &&
        IE(document.body, e.rule.initialGlobalAttributes),
      b$(e),
      e.rule.isOnBoardingPage)
    ) {
      document.dispatchEvent(
        new CustomEvent(Jt, {
          detail: JSON.stringify({
            type: "currentConfig",
            payload: {
              enableDefaultAlwaysTranslatedUrls:
                !!e.config.enableDefaultAlwaysTranslatedUrls,
            },
          }),
        })
      );
      let o = document.querySelector(
        "#immersiveTranslateEnableDefaultAlwaysTranslatedUrlsValue"
      );
      o &&
        ((o.value = "helloworld"),
        (o.value = String(!!e.config.enableDefaultAlwaysTranslatedUrls)),
        o.dispatchEvent(new Event("change")));
    }
    eR || ((eR = !0), tt() && e.rule.useIframePostMessage && (await Vw()));
    let r = e.sourceLanguage;
    r === "auto" ? (r = await nR()) : Mr(r);
    let a = US(e, r);
    if (
      (a && (e = await $e(Ie(), { translationMode: a })), e.rule.isInjectMeta)
    )
      try {
        let o = await jl(r, e.targetLanguage),
          s = document.createElement("meta");
        (s.name = N + "-meta"),
          (s.content = fc(JSON.stringify(o))),
          document.head?.appendChild?.(s);
      } catch (o) {
        I.warn("inject meta failed", o);
      }
    await S$(e), await f$(e);
    let i = p$(e);
    if (
      (!i &&
        !e.isTranslateExcludeUrl &&
        (I.debug(`detect page language: ${e.url} ${r}`),
        mo(r, e.targetLanguage, {
          ignoreZhCNandZhTW: e.rule.ignoreZhCNandZhTW,
        }) ||
          r === "auto" ||
          (Kw(r, e.config.translationLanguagePattern) &&
            ((i = !0),
            I.debug(`match language pattern ${r}, auto translate`)))),
      (await LT(e)) && ((i = !0), I.debug("auto translate by referrer")),
      e.rule.pageType == "ebookBuilder" && (i = !1),
      e.rule.pageType !== "pdfReader" && cl(t) && Vy(e),
      i)
    )
      (be.state.isAutoTranslate = !0),
        Ul(document.documentElement) && globalThis.parent != globalThis.self
          ? kn("Translated")
          : yr(be);
    else if (
      (I.debug("do not auto translate", e),
      e.rule.initTranslationServiceAsSoonAsPossible &&
        e.translationService === "deepl")
    ) {
      if (
        mo(r, e.targetLanguage, {
          ignoreZhCNandZhTW: e.rule.ignoreZhCNandZhTW,
        }) ||
        r === "auto"
      )
        return;
      typeof e.config?.translationServices?.deepl?.authKey == "string" &&
        e.config.translationServices.deepl.authKey.startsWith("immersive_") &&
        (af[e.translationService] ||
          ((af[e.translationService] = !0),
          tt() ||
            D2(e).catch((o) => {
              I.warn("init translation engine error", o);
            })));
    }
    if (e.rule.immediatelyInjectedCss)
      try {
        An(
          document,
          e.rule.immediatelyInjectedCss.join(`
`),
          N + "-immediately-injected-css"
        );
      } catch (o) {
        I.warn("inject immediately css error", o);
      }
  }
  var tR = 0;
  async function r9(e) {
    if (Date.now() - tR < 100) return;
    tR = Date.now();
    let t = e?.currentPageStatus || Ye();
    if (t === "Original") {
      let n = {};
      $u && (n.translationTheme = $u), be && (n = { ...be.state, ...n });
      let r = await $e(Ie(), n);
      if (e?.trigger == "right_menu" && Ms(r?.rule)) {
        let a = Ds(r.rule);
        if (a) {
          Zl(!0, a || Ie());
          return;
        }
      }
      await yr({ ...be, sourceProgram: "html" }, e);
    } else ["Translated", "Error"].includes(t) && Ea();
  }
  async function $X() {
    let e = Ql();
    be || (be = await $e(Ie(), {})),
      e == "Original" ? lI(be) : (dI(), Qr("Original"));
  }
  function e$() {
    mT("enableEditTranslation", !be?.config.enableEditTranslation);
    let e = wp("enableEditTranslation");
    be && (be.config.enableEditTranslation = e);
    let t = W0();
    t && (t.ctx.config.enableEditTranslation = e),
      document.querySelectorAll(`font.notranslate.${vt}`).forEach((n) => {
        e
          ? n.setAttribute("contenteditable", "true")
          : n.removeAttribute("contenteditable");
      });
  }
  async function t$() {
    if (((be = await $e(Ie(), {})), be.rule.pageType == "ebookBuilder"))
      return EL(be);
    if (be.rule.pageType == "subtitleBuilder") return SL(be);
    Lk();
  }
  async function a9(e) {
    if (Ye() === "Original") {
      (be = await $e(Ie(), {})), $u || ($u = be.state.translationTheme);
      let t = "mask";
      be.state.translationTheme === "opacity" && (t = "opacity"),
        (be = await $e(Ie(), { translationTheme: t })),
        await yr(be, e);
    } else if (Ye() === "Translated") {
      let t = "mask";
      be?.state?.translationTheme === "opacity" && (t = "opacity");
      let n = gb()
          .filter((i) => i.contentDocument?.body)
          .map((i) => i.contentDocument.body),
        r = [be.mainFrame, ...n],
        a = be?.state.translationTheme;
      for (let i of r) {
        let o = q8(i, il, !0);
        a === "mask" || a === "opacity"
          ? o !== "none"
            ? Lt(i, il, "none", !0)
            : Lt(i, il, t, !0)
          : o !== "mask" && o !== "opacity"
          ? Lt(i, il, t, !0)
          : Lt(i, il, "none", !0);
      }
    }
  }
  async function nR() {
    let e = await $e(Ie(), {});
    return e.rule.pageType == "subtitleBuilder" ? R9() : Pk(e);
  }
  function n$(e) {
    Ye() === "Original" ? CL(e) : F9();
  }
  function r$(e) {
    Ye() === "Original" ? Yg(e) : Oo();
  }
  async function Ea() {
    f0(""),
      document.dispatchEvent(
        new CustomEvent(Jt, {
          detail: JSON.stringify({ type: "restorePage", payload: {} }),
        })
      );
    let e = await $e(Ie(), {});
    if (e.rule.pageType == "subtitleBuilder") {
      F9();
      return;
    } else e.rule.pageType == "ebookBuilder" && AL(e);
    Oo();
  }
  function a$(e) {
    if (
      e.config.sameLangCheck &&
      !(e.rule.detectParagraphLanguage || e.state.isDetectParagraphLanguage) &&
      rn() == e.targetLanguage
    ) {
      if (
        document.body?.innerText.length < 100 &&
        globalThis.self !== globalThis.top
      )
        return;
      document.dispatchEvent(
        new CustomEvent(Rd, { detail: { type: "sameLang" } })
      );
    }
  }
  async function yr(e, t) {
    if (
      globalThis.self !== globalThis.top &&
      globalThis.self.innerWidth === 0 &&
      globalThis.self.innerHeight === 0
    )
      return;
    if (
      (Di("initial"),
      a$(e),
      Ye() !== "Original" && (await Ea()),
      document.dispatchEvent(
        new CustomEvent(Jt, {
          detail: JSON.stringify({ type: "translateStart", payload: {} }),
        })
      ),
      document.dispatchEvent(
        new CustomEvent(Jt, {
          detail: JSON.stringify({
            type: "targetLanguage",
            payload: { targetLanguage: e.targetLanguage },
          }),
        })
      ),
      af[e.translationService] ||
        ((af[e.translationService] = !0),
        tt() ||
          D2(e).catch((r) => {
            I.warn("init translation engine error", r);
          })),
      (e.specialAiAssistant = Fi(e, e.translationService, !0)),
      m$(e, t),
      e.rule.pageType == "subtitleBuilder")
    ) {
      n$(e), f0("yes");
      return;
    }
    if (e.rule.pageType == "ebookBuilder") {
      kL(e), f0("yes");
      return;
    }
    r$(e), f0("yes");
  }
  async function i$(e, t) {
    let n = be?.translationService;
    if (Ye() === "Original" || n != e) {
      be = await $e(Ie(), { translationService: e });
      let r = Pa(be.config, e),
        a = ye(be.config.interfaceLanguage, "tempChangeTransToService", {
          service: r,
        });
      Ed({ text: a }), await yr(be, t);
    } else {
      Ea();
      let r = await Wn(),
        a = { url: Ie(), config: r, state: {} },
        i = await Fn(a);
      be = await $e(Ie(), { translationService: i.translationService });
    }
  }
  async function o$(e) {
    Ye() === "Original"
      ? await J9(e)
      : (Ye() === "Translated" || Ye() === "Error") &&
        ((be = await $e(Ie(), {})),
        be.state.translationArea !== "main" ? await J9(e) : Ea());
  }
  async function J9(e) {
    (be = await $e(Ie(), { translationArea: "main" })), await yr(be, e);
  }
  async function rR(e) {
    (be = await $e(Ie(), { translationArea: "body" })), await yr(be, e);
  }
  async function fL(e) {
    let t = await je({ text: e, ignorePageLang: !0 });
    return Mr(t), t;
  }
  async function i9(e) {
    let t = await $e(Ie(), {}),
      n = t.state.translationMode == "dual" ? "translation" : "dual";
    if (((t = await $e(Ie(), { translationMode: n })), Ye() === "Original")) {
      yr(t, e);
      return;
    }
    window.immersiveTranslateSwitchTranslateState &&
      window.immersiveTranslateSwitchTranslateState(n);
  }
  async function I9(e) {
    if (
      ((be = await $e(Ie(), { translationMode: e })),
      Te({
        key: "switch_translation_mode",
        params: { mode: e },
        ctx: { ...be, sourceLanguage: "unknown" },
      }),
      Ye() === "Original")
    ) {
      yr(be);
      return;
    }
    globalThis.immersiveTranslateSwitchTranslateState &&
      globalThis.immersiveTranslateSwitchTranslateState(e);
  }
  async function s$(e) {
    if (Ye() === "Original") await rR(e);
    else if (Ye() === "Translated" || Ye() === "Error") {
      let t = {};
      be && be.state && (t = be.state),
        (be = await $e(Ie(), t)),
        be.state.translationArea !== "body"
          ? ((be.state.translationArea = "body"),
            (be = await $e(Ie(), be.state)),
            await yr(be, e))
          : Ea();
    }
  }
  async function aR(e) {
    (be = await $e(Ie(), { translationStartMode: "immediate" })),
      await yr(be, e);
  }
  async function l$(e) {
    Ye() === "Original"
      ? await aR(e)
      : (Ye() === "Translated" || Ye() === "Error") && Ea();
  }
  async function iR() {
    let e = await $e(Ie(), {});
    if (!nt(e.url, e.config.inputStyleBlockUrls)) {
      let n = Le().IMMERSIVE_TRANSLATE_INPUT_INJECTED_CSS;
      An(document, n, zd);
    }
    (e.rule.pageType && e.rule.pageType !== "html") ||
      (e.config.enableInputTranslation && Ky(e));
  }
  function id() {
    return be;
  }
  async function $e(e, t, n) {
    let r = Object.keys(t);
    if (!be || n) {
      let a = await Wn(),
        i = t;
      r.length === 0 && (i = void 0),
        (be = await Fn({ url: e, config: a, state: i }));
    } else {
      let a = { url: e, config: be.config, state: { ...be.state, ...t } };
      be = await Fn(a);
    }
    return (
      be.state?.translationService &&
        (be.translationService = be.state.translationService),
      be.state?.subtitleTranslateService &&
        (be.subtitleTranslateService = be.state.subtitleTranslateService),
      be.state?.targetLanguage && (be.targetLanguage = be.state.targetLanguage),
      be
    );
  }
  async function u$() {
    let e = (await jt()) || {},
      t = e.generalRule || {},
      n = t["subtitleRule.add"] || {},
      r = !n.preTranslation;
    await Yt({
      ...e,
      generalRule: { ...t, "subtitleRule.add": { ...n, preTranslation: r } },
    }),
      r
        ? Ed({
            text: ye(
              be.config.interfaceLanguage,
              "videoSubtitlePreTranslationOn"
            ),
          })
        : Ed({
            text: ye(
              be.config.interfaceLanguage,
              "videoSubtitlePreTranslationOff"
            ),
          }),
      setTimeout(() => {
        window.location.reload();
      }, 1e3);
  }
  async function Vi() {
    let e = await Wn();
    wp("enableEditTranslation") != null &&
      (e.enableEditTranslation = wp("enableEditTranslation"));
    let t;
    be && be.state && (t = be.state);
    let n = { url: Ie(), config: e, state: t },
      r = await Fn(n);
    return (
      r.state &&
        r.state.translationService &&
        (r.translationService = r.state.translationService),
      (be = r),
      Ub(be),
      await c$(be),
      r
    );
  }
  async function c$(e) {
    let t = await or();
    yd(e, window),
      cl(t) && Vy(e),
      e.config.enableInputTranslation && Ky(e),
      O9(e);
  }
  function d$(e, t = 3e3) {
    return new Promise((n, r) => {
      let a = t
          ? setTimeout(() => {
              n(new Error("timeout"));
            }, t)
          : void 0,
        i = setInterval(() => {
          e.every((s) => document.querySelector(s) !== null) &&
            (clearInterval(i), a && clearTimeout(a), n(null));
        }, 50);
    });
  }
  async function bL(e) {
    let t = e.detail;
    et.set(gt, t);
    let n = await et.get(Rf, !1);
    et.set(Rf, !1);
    let r = await jt();
    xt(t) && (await gP(t.token, r, Yt)),
      document.dispatchEvent(
        new CustomEvent(ie + "DocumentMessageUserResult", {
          detail: n ? "close" : "success",
        })
      ),
      o0(),
      s0(),
      xs({ method: "updateContext" });
  }
  function yL(e) {
    I.debug("update user info", e);
    let t = e.detail;
    et.set(gt, t), o0(), s0(), Vi(), xs({ method: "updateContext" });
  }
  async function vL(e) {
    let t = await jt();
    if (t.translationService) return;
    let n = e.config.translationService,
      r = await rd(e, n);
    if (e.translationService === r) return;
    if (!r) {
      Te({ key: "no_avaliable_translation_service", ctx: e });
      return;
    }
    let a = Ss({
      state: t,
      config: e.config,
      serviceType: "translationService",
      serviceId: r,
    });
    Yt(a),
      (e.translationService = r),
      Te({
        key: "change_default_translation_service",
        params: { translation_service: r },
        ctx: e,
      });
  }
  async function xL(e, t) {
    let n = t.detail;
    if (!e.rule.allowInnerInvoke) return;
    let r = n.translateService;
    if (!r) return;
    let [a] = pi(e.config, [r]) || [],
      i = await jt(),
      o = Ss({
        state: i,
        config: e.config,
        serviceType: "translationService",
        serviceId: a,
      });
    Yt(o);
  }
  async function rd(e, t, n = !0, r) {
    let a = r ||
      e.rule.detectionServiceOrder || ["google", "bing", "transmart", "yandex"];
    return (
      (a = um(e, a)),
      a.sort((i, o) => (i === t ? (n ? -1 : 1) : o === t ? (n ? 1 : -1) : 0)),
      await pg(a, e)
    );
  }
  async function aL(e, t) {
    let n = await jt(),
      r = Ss({
        state: n,
        config: e,
        serviceType: "translationService",
        serviceId: t,
      });
    await Yt(r);
    let a = await oR({ translationService: t });
    await yr(a);
  }
  var X9 = {
    interfaceLanguage: "",
    targetLanguage: "",
    translationService: "",
    aiAssistantIds: [],
    subtitleTranslateService: "",
    inputTranslationService: "",
    mouseHoverTranslationService: "",
  };
  async function P9(e, t) {
    let n = await jt(),
      r = !1,
      a = !1;
    return (
      Object.keys(X9).forEach((i) => {
        let o = t[i];
        if (o != null) {
          if (
            ((i === "targetLanguage" || i === "interfaceLanguage") &&
              ((o = oe(o)), o !== n[i] && (a = !0)),
            i.endsWith("Service"))
          ) {
            let [s] = pi(e.config, [o]) || [];
            (n = Ss({
              state: n,
              config: e.config,
              serviceType: i,
              serviceId: s,
            })),
              (r = !0);
            return;
          }
          (n[i] = o), (r = !0);
        }
      }),
      r && (qo(n), await Yt(n), (e = await Vi())),
      a && Te({ key: "init_page_daily", ctx: e, forceDaily: !0 }),
      L9(e)
    );
  }
  function L9(e) {
    let t = e.config,
      n = {};
    return (
      Object.keys(X9).forEach((r) => {
        t[r] != null && (n[r] = t[r]);
      }),
      n
    );
  }
  async function oR(e) {
    let t = await $e(Ie(), e);
    return (
      Ku("syncContextState", e),
      e?.targetLanguage && Ps({ targetLanguage: e.targetLanguage }),
      t
    );
  }
  function m$(e, t) {
    if (tt()) return;
    let n = {};
    t && t.trigger && (n.trigger = t.trigger),
      Te({ key: "translage_page_daily", params: n, ctx: e }),
      Te({
        key: "translate_page",
        params: n,
        ctx: { ...e, sourceLanguage: rn() },
      }),
      kr("translate_web_1", e);
    let r = e.config.translationServices?.[e.translationService];
    r.assistantId != "common" &&
      Te({
        key: "assistant_translate_page",
        params: {
          ...n,
          ai_assistant: r.assistantId,
          ai_assistant_use: e.specialAiAssistant?.id || "common",
        },
        ctx: { ...e, sourceLanguage: rn() },
      });
  }
  function p$(e) {
    let t = new URL(e.url).searchParams;
    return (
      !!e.config.autoTranslateParams?.find((r) => {
        let a = t.get(r) || "";
        if (r == "crosearch_translate") {
          let i = t.get("output_locale");
          i && ((be.state.targetLanguage = oe(i)), (be.targetLanguage = oe(i)));
        }
        return ["1", "true"].includes(a);
      }) ||
      e.state.isAutoTranslate ||
      e.isTranslateUrl
    );
  }
  var g$ = [
    "translationMode",
    "translationArea",
    "isAutoTranslate",
    "translationService",
    "targetLanguage",
    "subtitleTranslateService",
  ];
  async function h$(e) {
    let t = Qo + "_set_state_";
    try {
      let n = [];
      new URL(KT() || location.href).searchParams.forEach((i, o) => {
        o.startsWith(t) && n.push([o, i]);
      });
      let a = {};
      for (let [i, o] of n) {
        let s = i.replace(t, "");
        if (!g$.includes(s)) return;
        a[s] = o;
      }
      return Object.keys(a).length ? await $e(Ie(), a) : void 0;
    } catch (n) {
      I.error(n);
    }
  }
  async function f$(e) {
    let t = Qo + "_set_";
    try {
      let n = [];
      new URL(location.href).searchParams.forEach((i, o) => {
        o.startsWith(t) && n.push([o, i]);
      });
      let a = {};
      for (let [i, o] of n) {
        let s = i.replace(t, "");
        if (X9[s] == null) return;
        a[s] = o;
      }
      if (!Object.keys(a).length) return;
      await P9(e, a);
    } catch (n) {
      I.error(n);
    }
  }
  async function b$(e) {
    let t = e.config.arxivRule;
    if (!t?.matches || !nt(e.url, t.matches)) return;
    let r = document.querySelector(t.injectContainerSelector);
    if (!r) return;
    let o = new URL(e.url).pathname.split("/").pop();
    for (let s of t.detectOrders)
      try {
        await Se({ url: `${s}/` + o, responseType: "HEAD", method: "HEAD" });
        let l = document.createElement("li");
        (l.innerHTML = `<a target="_blank" href="${s}/${o}?_immersive_translate_auto_translate=1" aria-describedby="download-button-info" accesskey="f" class="abs-button download-pdf">${ye(
          e.config.interfaceLanguage,
          "viewWithImmersiveTranslate"
        )}</a>`),
          r.appendChild(l);
        break;
      } catch {}
  }
  function y$(e, { text: t, trigger: n }) {
    let r = document.activeElement;
    r &&
      ((r.tagName !== "INPUT" &&
        r.tagName !== "TEXTAREA" &&
        !r.isContentEditable) ||
        $y(e, window, t, n));
  }
  function v$(e, { text: t, trigger: n }) {
    let r = globalThis.getSelection();
    if (!t || !r || r.isCollapsed) return;
    let a = r.rangeCount > 0 ? r.getRangeAt(0) : null;
    if (!a) return;
    let i = j9(r);
    U9({
      selection: {
        text: t,
        range: a,
        rect: a.getBoundingClientRect(),
        contextText: i,
      },
      ctx: e,
      trigger: n,
    });
  }
  function x$(e, { translation_service: t }) {
    Te({
      key: "change_translate_service",
      params: { translation_service: t },
      ctx: e,
    });
  }
  function C$(e, { key: t, events: n, forceDaily: r, trigger: a }) {
    a &&
      n.forEach((i) => {
        i.params?.trigger || (i.params = { ...i.params, trigger: a });
      }),
      Xp(t, n, e, r);
  }
  async function S$(e) {
    let n = new URL(e.url).searchParams.get("imt_refresh_rule");
    ["1", "true"].includes(n || "") && (await hd());
  }
  rw({
    detectCurrentPageLanguage: nR,
    ensureSwitchTranslationMode: i9,
    restorePage: Ea,
    retryFailedParagraphs: t$,
    switchTranslationMode: I9,
    toggleTranslateManga: $X,
    toggleTranslatePage: r9,
    toggleTranslateTheMainPage: o$,
    toggleTranslateTheWholePage: s$,
    toggleTranslationMask: a9,
    toggleEnableEditTranslation: e$,
    toggleVideoSubtitlePreTranslation: u$,
    translatePage: yr,
    translatePageWithTranslationService: i$,
    translateTheMainPage: J9,
    translateTheWholePage: rR,
    translateToThePageEndImmediately: aR,
    toggleTranslateToThePageEndImmediately: l$,
    updateGlobalContext: Vi,
    getPureGlobalContext: id,
    getIsDulSubtitle: fA,
    updateContextState: oR,
    inputSelectedTextTranslate: y$,
    reportTranslateService: x$,
    popupEventReport: C$,
    updateFloatBallEnable: qP,
    translateSelectImage: JP,
    selectionTranslate: v$,
  });
  async function sR(e) {
    if (!e) {
      let n = await Wn();
      e = await Fn({ config: n, url: Ie() });
    }
    if (
      (e.config.debug ? I.setLevel("debug") : I.setLevel("info"),
      !e.isTranslateExcludeUrl)
    ) {
      if (
        e.rule.pageType == "ebookReader" ||
        e.rule.pageType == "ebookBuilder"
      ) {
        if (e.rule.isInjectMeta) {
          try {
            let n = await jl("auto", e.targetLanguage),
              r = document.createElement("meta");
            (r.name = N + "-meta"),
              (r.content = fc(JSON.stringify(n))),
              document.head?.appendChild?.(r);
          } catch (n) {
            I.warn("inject meta failed", n);
          }
          if (e.rule.isInjectOptionsUrl) {
            let n = Li(e.config.useOnlineOptions),
              r = document.createElement("meta");
            (r.name = N + "-options-url"), (r.content = n);
            try {
              document.head?.appendChild?.(r);
            } catch (a) {
              I.warn("inject options url failed", a);
            }
          }
          if (e.rule.isInjectVersion) {
            let n = Pt(),
              r = document.createElement("meta");
            (r.name = N + "-version"), (r.content = n);
            try {
              document.head?.appendChild?.(r);
            } catch (a) {
              I.warn("inject version failed", a);
            }
          }
        }
      } else FT(e), zh();
      GP(e).catch((n) => {
        I.error(`init popup page error: ${n}`);
      }),
        tt() || lP(e),
        Sh(e);
    }
    await iR(), await iP(e);
  }
  var xd = { capture: !0, once: !0, passive: !0 },
    cR = () =>
      document.readyState === "interactive" ||
      document.readyState === "complete",
    T$ = (e) => document.readyState === e,
    Cd = (e, t) => (T$(e) || cR() ? (t(e), !0) : !1),
    w$ = () =>
      new Promise((e) => {
        Cd("loading", e) ||
          document.addEventListener(
            "readystatechange",
            () => {
              document.readyState === "loading" && e("loading");
            },
            xd
          );
      }),
    E$ = () =>
      new Promise((e) => {
        Cd("interactive", e) ||
          document.addEventListener(
            "readystatechange",
            () => {
              document.readyState === "interactive" && e("interactive");
            },
            xd
          );
      }),
    A$ = () =>
      new Promise((e) => {
        Cd("complete", e) ||
          document.addEventListener(
            "readystatechange",
            () => {
              document.readyState === "complete" && e("complete");
            },
            xd
          );
      }),
    lR = () =>
      new Promise((e) => {
        Cd("domready", e) ||
          document.addEventListener(
            "DOMContentLoaded",
            () => {
              e("domready");
            },
            xd
          );
      }),
    uR = () =>
      new Promise((e) => {
        Cd("load", e) ||
          window.addEventListener(
            "load",
            () => {
              e("load");
            },
            xd
          );
      }),
    dR = {};
  Object.defineProperties(dR, {
    state: {
      get: function () {
        return document.readyState;
      },
    },
    loading: {
      get: function () {
        return w$();
      },
    },
    interactive: {
      get: function () {
        return E$();
      },
    },
    complete: {
      get: function () {
        return A$();
      },
    },
    window: {
      get: function () {
        return uR();
      },
    },
    load: {
      get: function () {
        return uR();
      },
    },
    domready: {
      get: function () {
        return lR();
      },
    },
    dom: {
      get: function () {
        return lR();
      },
    },
    ready: {
      get: function () {
        return cR();
      },
    },
  });
  var mR = dR;
  var pR = {
    NOT_FUNCTION:
      "Your executor is not a function. functions and promises are valid.",
    FAILED_TO_WAIT: "Failed to wait",
  };
  function k$(e) {
    return () =>
      Promise.resolve()
        .then(() => e())
        .catch((t) => {
          throw t;
        });
  }
  function M$(e) {
    if (typeof e != "function") throw new Error(pR.NOT_FUNCTION);
  }
  var $9 = class {
      _interval;
      _timeout;
      _stopOnFailure;
      _backoffFactor;
      _backoffMaxInterval;
      _Console;
      originalStacktraceError;
      _userMessage;
      _verbose;
      _isWaiting;
      _isResolved;
      _executeFn;
      start;
      promise;
      resolve;
      reject;
      _lastError;
      constructor({
        interval: t = 100,
        timeout: n = 1e3,
        stopOnFailure: r = !1,
        verbose: a = !1,
        backoffFactor: i = 1,
        backoffMaxInterval: o,
        message: s = "",
      } = {}) {
        (this._interval = t),
          (this._timeout = n),
          (this._stopOnFailure = r),
          (this._isWaiting = !1),
          (this._isResolved = !1),
          (this._verbose = a),
          (this._userMessage = s),
          (this.originalStacktraceError = new Error()),
          (this._Console = console),
          (this._backoffFactor = i),
          (this._backoffMaxInterval = o || n),
          (this.start = +Date.now());
      }
      tryEvery(t) {
        return (this._interval = t), this;
      }
      stopAfter(t) {
        return (this._timeout = t), this;
      }
      execute(t) {
        return (
          this._applyPromiseHandlers(),
          M$(t),
          (this._executeFn = k$(t)),
          (this.start = Date.now()),
          (this._isWaiting = !0),
          this._log("starting to execute"),
          this._runFunction(),
          this.promise
        );
      }
      getPromise() {
        return this.promise;
      }
      isResolved() {
        return this._isResolved;
      }
      isWaiting() {
        return this._isWaiting;
      }
      stopOnFailure(t) {
        return (this._stopOnFailure = t), this;
      }
      _applyPromiseHandlers() {
        this.promise = new Promise((t, n) => {
          (this.resolve = t), (this.reject = n);
        });
      }
      _timeFromStart() {
        return Date.now() - this.start;
      }
      _shouldStopTrying() {
        return this._timeFromStart() > this._timeout;
      }
      _executeAgain() {
        this._log("executing again");
        let t = this._interval,
          n = t * this._backoffFactor;
        (this._interval =
          n > this._backoffMaxInterval ? this._backoffMaxInterval : n),
          setTimeout(this._runFunction.bind(this), t);
      }
      _failedToWait() {
        let t = `${pR.FAILED_TO_WAIT} after ${this._timeFromStart()}ms`;
        if (
          (this._userMessage && (t = `${t}: ${this._userMessage}`),
          this._lastError)
        ) {
          this._lastError.message = `${t}
${this._lastError.message}`;
          let n = this.originalStacktraceError.stack;
          n &&
            (this._lastError.stack += n.substring(
              n.indexOf(`
`) + 1
            ));
        } else
          (this._lastError = this.originalStacktraceError),
            (this._lastError.message = t);
        return this._log(this._lastError), this._lastError;
      }
      _runFunction() {
        if (this._shouldStopTrying()) {
          (this._isWaiting = !1), this.reject?.(this._failedToWait());
          return;
        }
        this._executeFn()
          .then((t) => {
            if (t === !1) {
              this._log(`then execute again with result: ${t}`),
                this._executeAgain();
              return;
            }
            this.resolve?.(t),
              (this._isWaiting = !1),
              (this._isResolved = !0),
              this._log(`then done waiting with result: ${t}`);
          })
          .catch((t) =>
            this._stopOnFailure
              ? (this._log(`stopped on failure with err: ${t}`),
                this.reject?.(t))
              : ((this._lastError = t),
                this._log(`catch with err: ${t}`),
                this._executeAgain())
          );
      }
      _log(t) {
        this._verbose &&
          this._Console &&
          this._Console.log &&
          this._Console.log(t);
      }
    },
    gR = (e, t) => new $9(t).execute(e);
  async function hR(e) {
    try {
      let t = e.config.domReadyDetectTimeout;
      return (
        await gR(
          () => {
            let r = (e.mainFrame || document.body)?.innerText || "";
            if (
              Rl(r, e.rule.mainFrameMinTextCount, e.rule.mainFrameMinWordCount)
            )
              return !0;
            throw new Error("there is no main text");
          },
          { timeout: t !== void 0 ? t : 3e3 }
        ),
        !0
      );
    } catch (t) {
      if (tt()) throw t;
      return I.debug("check dom element ready failed:", t, e), !0;
    }
  }
  function fR() {
    let e = document.querySelector(`meta[name=${N}-options]`);
    return !!(e && e.getAttribute("content") === "true");
  }
  async function bR() {
    if (!document.getElementById(N + "-status")) {
      I.error("Could not find status element");
      return;
    }
    await e6("local"), await e6("sync"), I$();
    let t = document.getElementById(N + "-page-ready");
    t &&
      setTimeout(() => {
        (t.value = "true"), t.dispatchEvent(new Event("change"));
      }, 100);
  }
  async function D$(e, t) {
    let n;
    try {
      n = JSON.parse(e.detail);
    } catch (a) {
      I.error("parse detail failed", a);
      return;
    }
    let r = n.id || "default";
    try {
      let a = n.data || {},
        i = await t(a),
        o = { id: r, ok: !0, data: i };
      document.dispatchEvent(
        new CustomEvent(Sf, {
          detail: JSON.stringify({ ...o, type: "answer" }),
        })
      );
    } catch (a) {
      let i = {
        ok: !1,
        errorName: a.name,
        errorMessage: a.message,
        errorDetails: a.details || a.detail,
      };
      document.dispatchEvent(
        new CustomEvent(Sf, {
          detail: JSON.stringify({ ...i, id: r, type: "answer" }),
        })
      );
    }
  }
  function I$() {
    document.addEventListener(R6, (r) => {
      let a = r;
      if ((I.debug("document message", a), a && a.detail)) {
        let i;
        try {
          i = JSON.parse(a.detail);
        } catch (o) {
          I.error("parse detail failed", o);
          return;
        }
        i.type === "ask"
          ? i.method === "request" && D$(a, Se)
          : i.type === "tell" && i.method === "updateCommands" && Vp(i.data);
      }
    });
    let e = document.getElementById(N + "-manifest");
    if (!e) {
      I.error("Could not find manifest element");
      return;
    }
    let t = te.runtime.getManifest();
    pt() && (t._isSafari = !0),
      (e.value = JSON.stringify(t)),
      e.dispatchEvent(new Event("change")),
      document
        .getElementById(`${N}-message`)
        .addEventListener("change", (r) => {
          try {
            let a = JSON.parse(r.target.value);
            a &&
              a.method === "removeStorageKey" &&
              a.data &&
              a.data.area &&
              a.data.keys &&
              (te.storage[a.data.area].remove(a.data.keys), e6(a.data.area));
          } catch (a) {
            I.error("parse message error", a);
          }
        });
  }
  async function e6(e) {
    let t = document.getElementById(N + "-status"),
      n = document.getElementById(`${N}-${e}-storage`);
    if (n) {
      I.debug("init storage");
      let r = await te.storage[e].get(null);
      (n.value = JSON.stringify(r)),
        n.dispatchEvent(new Event("change")),
        n.addEventListener("change", (a) => {
          try {
            let i = JSON.parse(a.target.value);
            te.storage[e].set(i);
          } catch (i) {
            I.error("save to storage error", i);
          }
        }),
        n.addEventListener("refresh-" + e, async (a) => {
          let i = await te.storage[e].get(null);
          (n.value = JSON.stringify(i)), I.debug("refresh ", e, "storage");
        });
    } else {
      I.error(`Could not find storage ${e} element`),
        (t.innerText = "Could not find storage local input element");
      return;
    }
  }
  function P$() {
    try {
      document.dispatchEvent(new Event(_6));
    } catch {}
  }
  function yR() {
    na() || P$();
  }
  var vR = [
    "textarea",
    "input",
    "button",
    "select",
    "option",
    "iframe",
    "strong",
    "form",
    "body",
  ];
  async function L$() {
    tt() || WT(Ie()), yR(), await u5();
    let e = await tn();
    e.excludeTranslationHtmlTags && (vR = e.excludeTranslationHtmlTags),
      Ps({}),
      st.addHook("beforeSanitizeElements", function (r, a, i) {
        let o = (r.nodeName || "").toLowerCase();
        if (vR.includes(o)) {
          let l = `<${o}>${r.textContent || ""}</${o}>`,
            u = document.createTextNode(l);
          return r.replaceWith(u), r;
        }
        return r;
      }),
      st.addHook("uponSanitizeElement", function (r, a) {
        let i = r.nodeName || "";
        /\d+$/.test(i) && (a.allowedTags[a.tagName] = !0),
          Yc(r.tagName) && (a.allowedTags[r.tagName.toLowerCase()] = !0);
      }),
      st.addHook("uponSanitizeAttribute", function (r, a) {
        Yc(r.tagName) && (a.allowedAttributes[a.attrName.toLowerCase()] = !0);
      });
    let t = Ie(),
      n = await Fn({ config: e, url: t });
    Te({ key: "init_page_daily", ctx: n }),
      mR.domready
        .then(() => {
          if (fR()) I.debug("detect web options page"), yd(n, window), bR();
          else {
            if (!n.config.enabled) return;
            if (n.rule.isInjectVersion) {
              let a = Pt(),
                i = document.createElement("meta");
              (i.name = N + "-version"), (i.content = a);
              try {
                document.head?.appendChild?.(i);
              } catch (o) {
                I.warn("inject version failed", o);
              }
            }
            if (nt(n.url, n.config.blockUrls)) return;
            yd(n, window),
              hR(n)
                .then(() => {
                  sR(n).catch((a) => {
                    a && I.debug("translate page error", a.name, a.message, a);
                  });
                })
                .catch((a) => {
                  I.debug("can not detect a valid body: ", a);
                });
          }
        })
        .catch((r) => {
          r && I.debug("translate dom ready detect error", r);
        });
  }
  L$().catch((e) => {
    I.debug("init error", e);
  });
})();
/*!
 * Toastify js 1.12.0
 * https://github.com/apvarun/toastify-js
 * @license MIT licensed
 *
 * Copyright (C) 2018 Varun A P
 */
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
/*! Bundled license information:

lottie-web/build/player/lottie.js:
  (*!
   Transformation Matrix v2.0
   (c) Epistemex 2014-2015
   www.epistemex.com
   By Ken Fyrstenberg
   Contributions by leeoniya.
   License: MIT, header required.
   *)
*/
/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/
