(() => {
  function ro(e) {
    let t = A.runtime.getURL(wl),
      a = new URL(t);
    return (
      e && (e.startsWith("http") || !qa()) && a.searchParams.set("file", e),
      a.href
    );
  }
  function Xp() {
    return ue().PDF_VIEWER_URL;
  }
  function $p(e) {
    return new URL(e)?.pathname.toLowerCase().endsWith(".pdf");
  }
  var io = class {
    accessToken;
    constructor(t) {
      this.accessToken = t;
    }
    async listAll() {
      let t = [],
        a = "";
      do {
        let { nextPageToken: n, files: r } = await this.list(a).catch((i) => {
          throw i;
        });
        t.push(...r), (a = n || "");
      } while (a);
      return t;
    }
    async getConfig(t) {
      try {
        return await (
          await fetch(
            `https://www.googleapis.com/drive/v3/files/${t}?alt=media`,
            { headers: { Authorization: `Bearer ${this.accessToken}` } }
          )
        ).json();
      } catch (a) {
        return I.error("get config error, use default", a), {};
      }
    }
    async delete(t) {
      await fetch(`https://www.googleapis.com/drive/v3/files/${t}`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${this.accessToken}` },
      });
    }
    findByName(t) {
      return this.list(void 0, `name = '${t}'`);
    }
    uploadConfig(t, a = ai) {
      let n = new Blob([JSON.stringify(t, null, 2)], {
        type: "application/json",
      });
      return this.upload(
        { name: a, parents: ["appDataFolder"], mimeType: "application/json" },
        n
      );
    }
    updateConfig(t, a) {
      let n = new Blob([JSON.stringify(a, null, 2)], {
        type: "application/json",
      });
      return this.updateContent(t, n);
    }
    async upload(t, a) {
      let n = new FormData();
      n.append(
        "metadata",
        new Blob([JSON.stringify(t)], {
          type: "application/json; charset=UTF-8",
        })
      ),
        n.append("file", a);
      let r = await fetch(
        "https://www.googleapis.com/upload/drive/v3/files?uploadType=multipart",
        {
          method: "POST",
          headers: { Authorization: `Bearer ${this.accessToken}` },
          body: n,
        }
      );
      return r.ok ? await r.json() : Promise.reject(r.text());
    }
    async list(t, a) {
      let n = new URL("https://www.googleapis.com/drive/v3/files");
      t && n.searchParams.append("pageToken", t),
        a && n.searchParams.append("q", a),
        n.searchParams.append("spaces", "appDataFolder"),
        n.searchParams.append(
          "fields",
          "files(id,name,createdTime,modifiedTime,size)"
        ),
        n.searchParams.append("pageSize", "100"),
        n.searchParams.append("orderBy", "createdTime desc");
      try {
        return (
          I.debug("list api:", n.toString(), this.accessToken),
          await (
            await fetch(n.toString(), {
              headers: { Authorization: `Bearer ${this.accessToken}` },
            })
          ).json()
        );
      } catch (r) {
        throw (I.error("fetch google ip error", r), r);
      }
    }
    async updateContent(t, a) {
      return await (
        await fetch(
          `https://www.googleapis.com/upload/drive/v3/files/${t}?uploadType=media`,
          {
            method: "PATCH",
            headers: { Authorization: `Bearer ${this.accessToken}` },
            body: a,
          }
        )
      ).text();
    }
  };
  function eg(e, t) {
    let a = ["https://www.googleapis.com/auth/drive.appdata"];
    return `https://accounts.google.com/o/oauth2/v2/auth?client_id=${ti}&response_type=token&redirect_uri=${encodeURIComponent(
      t
    )}&scope=${encodeURIComponent(a.join(" "))}&state=${encodeURIComponent(
      JSON.stringify(e)
    )}`;
  }
  function tg(e) {
    let t = e.match(/[#?](.*)/);
    return !t || t.length < 1
      ? null
      : {
          access_token: new URLSearchParams(t[1].split("#")[0]).get(
            "access_token"
          ),
        };
  }
  async function ag(e, t, a, n, r, i, o) {
    if ((I.debug(`autoSyncStrategy accessToken: ${e}`), t === null)) {
      I.debug("autoSyncStrategy settings is null");
      return;
    }
    let s = new io(e);
    try {
      let u = (await s.findByName(ai)).files;
      I.debug("files", u);
      let c = u[0]?.id,
        l = null;
      if (
        (c &&
          (l = await s.getConfig(c).then((d) => ({ fileId: c, config: d }))),
        l)
      ) {
        let { config: d, fileId: m } = l,
          p = d.updatedAt ? new Date(d.updatedAt) : new Date(0),
          v = t.updatedAt ? new Date(t.updatedAt) : new Date(0);
        if ((I.debug("remoteUpdatedAt", p, "localUpdatedAt", v), p > v))
          I.debug("remote is newer, update local config", d), a(d), i && i(!0);
        else if (p.getTime() === v.getTime())
          I.debug("remote and local are the same, do nothing"), i && i(!1);
        else if (p < v) {
          let w = new Date().getTime(),
            S = await Ne("installedAt", ""),
            g = w - new Date(S).getTime() < 60 * 60 * 1e3 * 24,
            M = Object.keys(d || {}).length,
            f = Object.keys(t || {}).length;
          g && M > f
            ? (I.debug(
                "It's very recent install, and remote config has more fields, use remote config",
                d
              ),
              a(d))
            : (I.debug("local is newer, update remote config", t),
              await s.updateConfig(m, t)),
            i && i(!0);
        } else {
          o && o(": unknown error");
          return;
        }
        n(new Date().toISOString());
      } else if (l === null)
        if (t) {
          if (!t.updatedAt) {
            let d = new Date().toISOString();
            r(d), (t.updatedAt = d);
          }
          await s.uploadConfig(t), n(new Date().toISOString()), i && i(!0);
        } else o && o(": Local Config is empty");
      else o && o(": latestConfig is " + l);
    } catch (u) {
      I.error("syncLatestWithDrive error", u), o && o(": " + u.message);
    }
  }
  var Lx = "https://oauth2.googleapis.com/revoke",
    oo = class e {
      CLASSNAME = "GoogleAuth";
      _state;
      _redirectUrl;
      constructor(t, a) {
        (this._state = t), (this._redirectUrl = a);
      }
      static revoke(t) {
        let a = `${Lx}?token=${t}`;
        return fetch(a, {
          method: "POST",
          headers: { "Content-Type": "application/x-www-form-urlencoded" },
        }).then(
          async (n) => (
            await e.removeAuthInfo(), Eo() && globalThis.location.reload(), n
          )
        );
      }
      static async getAuthInfo() {
        let t = await A.storage.local.get(pn);
        if (t[pn]) return t[pn];
      }
      static async removeAuthInfo() {
        return await A.storage.local.remove(pn);
      }
      static setAuthInfo(t) {
        return A.storage.local.set({ [pn]: t });
      }
      async auth(t = !1) {
        let a = await e.getAuthInfo();
        if (
          (I.debug(this.CLASSNAME, "token from cache:", a),
          a &&
            a.access_token &&
            (await ng(a.access_token)
              .then((i) => !0)
              .catch((i) => !1)))
        )
          return Promise.resolve(a);
        let n = eg(this._state, this._redirectUrl);
        return (
          I.debug(this.CLASSNAME, "auth url: " + n),
          Eo()
            ? this.userscriptAuthWorkflow(n, t)
            : this.extensionAuthWorkflow(n).then((r) => (e.setAuthInfo(r), r))
        );
      }
      async userscriptAuthWorkflow(t, a) {
        return (
          a && (await A.storage.local.set({ [Sl]: !0 })),
          globalThis.open(t, "_self"),
          Promise.resolve({})
        );
      }
      extensionAuthWorkflow(t) {
        let a = typeof A < "u" && !!A?.runtime?.id,
          n = a && typeof A.windows?.create == "function",
          r,
          i = !1;
        return new Promise((o, s) => {
          let u = () => {
              a &&
                (A.tabs.onUpdated.removeListener(c),
                A.tabs.onRemoved.removeListener(l));
            },
            c = (p, v, w) => {
              if (
                (I.debug(this.CLASSNAME, "create tab onUpdated: " + w.url),
                r === p)
              ) {
                let S = new URL(w.url || ""),
                  g = tg(w.url);
                S.pathname.startsWith("/auth-done") &&
                  g?.access_token &&
                  (I.debug(this.CLASSNAME, "auth done: " + w.url),
                  o({ access_token: g.access_token }),
                  (i = !0),
                  A.tabs.remove(p),
                  u());
              }
            },
            l = (p) => {
              I.debug(this.CLASSNAME, "create tab onRemoved: " + p),
                (p === r || !i) && (u(), s(new Error("auth failed")));
            },
            d = () => {
              let p = globalThis.open(
                t,
                "google_oauth",
                `width=500,height=650,left=${screen.width / 2 - 250},top=${
                  screen.height / 2 - 325
                }`
              );
              if (!p)
                throw new Error(
                  "Popup blocked. Please allow popups for this site."
                );
              return new Promise((v, w) => {
                let S = setInterval(() => {
                  if (p.location.href.includes("google-auth-success")) {
                    p.close(), clearInterval(S);
                    try {
                      let g =
                          localStorage.getItem("immersiveTranslateAuthState") ||
                          "",
                        M = JSON.parse(g);
                      o(M);
                    } catch (g) {
                      w(g);
                    }
                    return;
                  }
                  p.closed &&
                    (clearInterval(S), w(new Error("User closed the window")));
                }, 100);
              });
            },
            m = () => {
              if (n && !kt().any) {
                let p = globalThis.screen,
                  v = p.availLeft ?? p.left,
                  w = p.availTop ?? p.top,
                  S = p.availWidth,
                  g = p.availHeight,
                  M = Math.max(400, Math.min(500, S)),
                  f = Math.max(500, Math.min(650, g)),
                  T = Math.max(
                    v,
                    Math.min(Math.round(v + (S - M) / 2), v + S - M)
                  ),
                  L = Math.max(
                    w,
                    Math.min(Math.round(w + (g - f) / 2), w + g - f)
                  );
                return A.windows
                  .create({
                    url: t,
                    type: "popup",
                    width: M,
                    height: f,
                    left: T,
                    top: L,
                  })
                  .then((E) => {
                    r = E.tabs?.[0]?.id;
                  });
              } else
                return A.tabs.create({ url: t }).then((p) => {
                  r = p.id;
                });
            };
          try {
            (a ? m() : d())
              .then(() => {
                a &&
                  (A.tabs.onUpdated.addListener(c),
                  A.tabs.onRemoved.addListener(l));
              })
              .catch(s);
          } catch (p) {
            s(p);
          }
        });
      }
    };
  var Nx = "https://www.googleapis.com/oauth2/v3/tokeninfo",
    Ox = ti,
    zx = ue().REDIRECT_URL;
  function ng(e) {
    if (!e) throw "Authorization failure";
    let t = `${Nx}?access_token=${e}`,
      a = new Request(t, { method: "GET" });
    function n(r) {
      return new Promise((i, o) => {
        r.status != 200 && o("Token validation error"),
          r.json().then((s) => {
            s.aud && s.aud === Ox ? i(e) : o("Token validation error");
          });
      });
    }
    return fetch(a).then(n);
  }
  function rg(e, t = !1) {
    let a = zx;
    if (typeof window < "u" && window.location.protocol.startsWith("http")) {
      let r = window.location.hostname,
        i = window.location.port;
      a = `${window.location.protocol}//${r}${i ? `:${i}` : ""}/auth-done/`;
    }
    return new oo(e, a).auth(t);
  }
  async function ig() {}
  function og(e, t) {
    let a = new Map();
    for (let i of t) a.set(i.header.toLowerCase(), i);
    let n = [],
      r = e.filter((i) => {
        let o = a.get(i.name.toLowerCase());
        if (o) {
          if (o.operation === "remove") return !1;
          if (o.operation === "set") return !1;
        }
        return !0;
      });
    for (let i of t)
      i.operation === "set" && n.push({ name: i.header, value: i.value || "" });
    return r.concat(n);
  }
  var so = [
    {
      id: 1,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "Referer",
            operation: "set",
            value: "https://httpstat.us/429",
          },
          {
            header: "origin",
            operation: "set",
            value: "https://httpstat.us/429",
          },
          { header: "DNT", operation: "set", value: "1" },
        ],
      },
      condition: {
        urlFilter: "https://httpstat.us/429",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 2,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "Referer",
            operation: "set",
            value: "https://www.deepl.com/",
          },
          {
            header: "origin",
            operation: "set",
            value: "https://www.deepl.com",
          },
          { header: "DNT", operation: "set", value: "1" },
          { header: "cookie", operation: "remove" },
        ],
      },
      condition: {
        urlFilter: "https://www2.deepl.com/jsonrpc*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 200,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "Referer",
            operation: "set",
            value: "https://www.deepl.com/",
          },
          {
            header: "origin",
            operation: "set",
            value: "chrome-extension://cofdbpoegempjloogbagkncekinflcnj",
          },
          { header: "DNT", operation: "set", value: "1" },
        ],
      },
      condition: {
        urlFilter: "https://api.deepl.com/jsonrpc*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 201,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "Referer",
            operation: "set",
            value: "https://www.deepl.com/",
          },
          {
            header: "origin",
            operation: "set",
            value: "chrome-extension://cofdbpoegempjloogbagkncekinflcnj",
          },
        ],
      },
      condition: {
        urlFilter: "https://w.deepl.com/oidc/token",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 3,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "origin",
            operation: "set",
            value: "chrome-extension://lkjkfecdnfjopaeaibboihfkmhdjmanm",
          },
        ],
      },
      condition: {
        urlFilter: "https://transmart.qq.com/api/imt",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 4,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "origin",
            operation: "set",
            value: "chrome-extension://lkjkfecdnfjopaeaibboihfkmhdjmanm",
          },
        ],
      },
      condition: {
        urlFilter: "https://translate.volcengine.com/crx/translate/v1/",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 5,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "User-Agent",
            operation: "set",
            value:
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
          },
        ],
      },
      condition: {
        urlFilter: "https://edge.microsoft.com/translate/auth",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 6,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "User-Agent",
            operation: "set",
            value:
              "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
          },
        ],
      },
      condition: {
        urlFilter:
          "https://api-edge.cognitive.microsofttranslator.com/translate",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 301,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://www.pixiv.net/",
          },
        ],
      },
      condition: {
        urlFilter: "https://i.pximg.net/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 302,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://newtoki341.com/",
          },
        ],
      },
      condition: {
        urlFilter: "https://img1.newtoki21*.org/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 303,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://newtoki341.com/",
          },
        ],
      },
      condition: {
        urlFilter: "https://img1.newtoki21.org/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 304,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://2.bp.blogspot.com",
          },
        ],
      },
      condition: {
        urlFilter: "https://2.bp.blogspot.com/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 305,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://japanreader.com",
          },
        ],
      },
      condition: {
        urlFilter: "https://japanreader.com/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 307,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://sl.mangafuna.xyz/",
          },
        ],
      },
      condition: {
        urlFilter: "https://sl.mangafuna.xyz/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 308,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          { header: "referer", operation: "set", value: "https://toonily.me" },
        ],
      },
      condition: {
        urlFilter: "https://s*.toonilycdnv2.xyz/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 309,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://readcomiconline.li",
          },
        ],
      },
      condition: {
        urlFilter: "https://*.whatsnew*.net/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 310,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://yymanhua.com",
          },
        ],
      },
      condition: {
        urlFilter: "https://image.yymanhua.com/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 311,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          { header: "referer", operation: "set", value: "https://klz9.com" },
        ],
      },
      condition: {
        urlFilter: "https://*.(klimv1|jfimv2).xyz/images*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 312,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://manhwato.com",
          },
        ],
      },
      condition: {
        urlFilter: "https://stcdn.manhwato.com/images/manga/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 313,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://www.comemh8.com",
          },
        ],
      },
      condition: {
        urlFilter: "https://*.kingwar.cn/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 314,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          { header: "referer", operation: "set", value: "https://weibo.com/" },
        ],
      },
      condition: {
        urlFilter: "https://*.sinaimg.cn/",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 315,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "origin",
            operation: "set",
            value: "http://127.0.0.1:11434",
          },
        ],
      },
      condition: {
        urlFilter: "http://*:11434",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 316,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "origin",
            operation: "set",
            value: "http://127.0.0.1:1234",
          },
        ],
      },
      condition: {
        urlFilter: "http://*:1234",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 317,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          { header: "referer", operation: "set", value: "https://jestful.net" },
        ],
      },
      condition: {
        urlFilter: "https://*.jfimv2.xyz/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 318,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://viewer.championcross.jp",
          },
        ],
      },
      condition: {
        urlFilter: "https://viewer.championcross.jp",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
    {
      id: 319,
      priority: 1,
      action: {
        type: "modifyHeaders",
        requestHeaders: [
          {
            header: "referer",
            operation: "set",
            value: "https://viewer.comic-growl.com/",
          },
        ],
      },
      condition: {
        urlFilter: "https://viewer.comic-growl.com/*",
        resourceTypes: ["xmlhttprequest"],
        domainType: "thirdParty",
        initiatorDomains: [
          "cfhamdkdjgoelclgllcoikbckcfpaklj",
          "bpoadfkcbjbfhfodiogcnhhhpibjhbnh",
          "amkbmndfnliijdhojkpoglbnaaahippg",
        ],
      },
    },
  ];
  var qx = "https://www.google-analytics.com/mp/collect",
    Gx = 30,
    Hx = 100;
  async function Kx() {
    let { sessionData: e } = await A.storage.session.get("sessionData"),
      t = Date.now();
    return (
      e &&
        e.timestamp &&
        ((t - e.timestamp) / 6e4 > Gx
          ? (e = null)
          : ((e.timestamp = t),
            await A.storage.session.set({ sessionData: e }))),
      e ||
        ((e = { session_id: t.toString(), timestamp: t.toString() }),
        await A.storage.session.set({ sessionData: e })),
      e.session_id
    );
  }
  function sg(e) {
    Nl().forEach(({ measurementId: t, apiSecret: a }) => {
      ug({
        measurement_id: t,
        api_secret: a,
        page_title: e.pageTitle,
        page_location: e.pageLocation,
        time: e.time,
      });
    });
  }
  function Hn(e, t, a) {
    return ug({
      measurement_id: jl,
      api_secret: Ll,
      page_title: e,
      page_location: t,
      time: a,
    });
  }
  async function ug(e) {
    try {
      let t = ue(),
        a = Wa(),
        n = t.INSTALL_FROM === "firefox_store";
      if ((a?.name?.startsWith("ImtFx") && (n = !1), n)) return;
      let { fakeUserId: r } = await oa(),
        i = `${qx}?measurement_id=${e.measurement_id}&api_secret=${e.api_secret}`,
        o = {
          method: "POST",
          body: JSON.stringify({
            client_id: r,
            events: [
              {
                name: "page_view",
                params: {
                  session_id: await Kx(),
                  engagement_time_msec: e.time || Hx,
                  page_title: e.page_title || document.title,
                  page_location: e.page_location || document.location.href,
                },
              },
            ],
          }),
        };
      fetch(i, o);
    } catch (t) {
      I.error(t);
    }
  }
  var fa = new Map();
  function lg(e) {
    if (!e) return null;
    try {
      return new URL(e).hostname;
    } catch {
      return null;
    }
  }
  async function lo(e) {
    let t = await dg(),
      a = Date.now();
    if (t) {
      if (fa.has(e)) return;
      let n = await A.tabs.get(e),
        r = lg(n.url);
      if (!r) return;
      fa.set(e, { duration: 0, lastActiveTime: a, hostname: r });
      return;
    }
    Yu(e);
  }
  async function cg(e) {
    let t = await dg(),
      a = Date.now();
    for (let [n, r] of fa)
      !(await A.tabs.get(n)).active &&
        r.lastActiveTime &&
        ((r.duration += a - r.lastActiveTime), (r.lastActiveTime = null));
    if (t) {
      if (fa.has(e)) {
        let i = fa.get(e);
        i.lastActiveTime = a;
        return;
      }
      let n = await A.tabs.get(e),
        r = lg(n.url);
      if (!r) return;
      fa.set(e, { duration: 0, lastActiveTime: a, hostname: r });
      return;
    }
  }
  function Yu(e) {
    if (fa.has(e)) {
      let t = fa.get(e),
        a = t.duration;
      t.lastActiveTime && (a += Date.now() - t.lastActiveTime),
        fa.delete(e),
        Vx(t.hostname, a);
    }
  }
  async function Wx(e) {
    let a = (await A.tabs.query({ currentWindow: !0, active: !0 }))[0].id;
    return _a().sendMessage(`content_script:main:${a}`, e);
  }
  function Vx(e, t) {
    Hn(`${e}(video)`, e, t), Qx(t);
  }
  var uo = "videoWatchTimeDay";
  async function Qx(e) {
    try {
      let t = new Date(),
        n = new Date(t.getFullYear(), t.getMonth(), t.getDate()).getTime(),
        r = await Ne(uo, null);
      if (!r) {
        await Se(uo, { day: n, time: e });
        return;
      }
      if (r.day !== n) {
        Hn("day-video." + De, "day-video." + De, r.time),
          await Se(uo, { day: n, time: e });
        return;
      }
      let i = { day: n, time: r.time + e };
      await Se(uo, i);
    } catch (t) {
      I.error(t);
    }
  }
  async function dg() {
    try {
      return await Wx({
        method: "getIsDulSubtitle",
        data: { trigger: "shortcut" },
      });
    } catch {
      return null;
    }
  }
  var Ju = (e, t, a, n) => {
    let r = Hl[e] || e,
      i = Gl[e] || e,
      o = Kl[e],
      s = Wl[e],
      u = { "zh-CN": o, "zh-TW": s, en: i };
    if (a) return u[e] ? u[e] : i;
    let c = e !== t && r !== "All Languages";
    if (u[t]) {
      let l = u[t];
      return n || e === "auto" || e === "placeholder"
        ? l
        : c
        ? `${l} (${r})`
        : `${l}`;
    } else return c ? `${i} (${r})` : i;
  };
  function Yx(e, t, a) {
    let n = e.generalRule.imageRule,
      r = Dm(a),
      o = n.supportPlatform?.[r] || n.enableImageTranslation,
      s = e.beta;
    return o == "all" ||
      (o == "beta" && s) ||
      (o == "pro" && t) ||
      (o == "pro_beta" && s && t)
      ? !0
      : o
      ? !1
      : s && !!t;
  }
  async function Zu() {
    if (fe()) return !1;
    let e = await Ue(),
      t = await cc();
    return Yx(e, t);
  }
  function Jx() {
    return na() && !Ze();
  }
  var Or = Jx() ? ["action"] : ["browser_action", "page_action"],
    zr = "",
    mg = [
      {
        id: "toggleTranslatePage",
        contexts: ["page", "frame", "selection", ...Or],
      },
      { id: Oa, contexts: ["image"] },
      { id: Mo, contexts: Or },
      { id: Po, contexts: Or },
      { id: Fo, contexts: Or },
      { id: Io, contexts: Or },
    ];
  async function Xu(e) {
    I.debug("createContextMenu", e.isShowContextMenu, mg);
    for (let t of mg) {
      let a = !0;
      try {
        let n = Re(e.interfaceLanguage, `browser.${t.id}`);
        if ((t.id === Oa && (a = await Zu()), t.id === "toggleTranslatePage")) {
          n = Re(e.interfaceLanguage, "browser.toggleTranslatePage", {
            targetLanguage: Ju(e.targetLanguage, e.interfaceLanguage, !1, !0),
          });
          let r = Vu(e.shortcuts.toggleTranslatePage);
          r && (n += `(${r})`);
        }
        A.contextMenus.create(
          {
            id: t.id,
            title: n,
            contexts: t.contexts,
            visible: a && e.isShowContextMenu,
          },
          () => A.runtime.lastError
        );
      } catch (n) {
        I.debug("create context menu error, it's ok!!", n, `menu id: ${t.id}`);
      }
    }
  }
  async function Ur({ targetLanguage: e, text: t }) {
    let a = await Ue(),
      n = "toggleTranslatePage",
      r;
    if (t)
      (zr = t),
        (r = Re(a.interfaceLanguage, "browser.translateText", { text: t }));
    else {
      let i = await co(),
        o = i && i !== "Original";
      (r = Re(a.interfaceLanguage, "browser.toggleTranslatePage", {
        targetLanguage: Ju(e ?? a.targetLanguage, a.interfaceLanguage, !1, !0),
      })),
        o && (r = `${Re(a.interfaceLanguage, "show-original")}`);
      let s = Vu(a.shortcuts.toggleTranslatePage);
      s && (r += `(${s})`), (zr = "");
    }
    A.contextMenus.update(n, { title: r, visible: a.isShowContextMenu });
  }
  function pg() {
    A.contextMenus.onClicked.addListener(async (e) => {
      if (e.menuItemId === Mo) A.runtime.openOptionsPage();
      else if (e.menuItemId === Io) {
        let t = Xp();
        A.tabs.create({ url: t });
      } else if (e.menuItemId === Fo) {
        let a = ue().EBOOK_BUILDER_URL;
        A.tabs.create({ url: a });
      } else if (e.menuItemId === Po) {
        let a = ue().EBOOK_VIEWER_URL;
        A.tabs.create({ url: a });
      } else if (e.menuItemId === Ml) {
        let a = ue().SUBTITLE_BUILDER_URL;
        A.tabs.create({ url: a });
      } else if (e.menuItemId === Oa)
        ln({
          method: e.menuItemId,
          data: { srcUrl: e.srcUrl, trigger: "right_menu" },
        });
      else {
        if (e.selectionText && e.editable) {
          ln({
            method: "inputSelectedTextTranslate",
            data: { text: zr, trigger: "right_menu" },
          });
          return;
        }
        if (e.selectionText && zr) {
          ln({
            method: "selectionTranslate",
            data: { text: zr, trigger: "right_menu" },
          });
          return;
        }
        ln({ method: e.menuItemId, data: { trigger: "right_menu" } });
      }
    });
  }
  async function gg() {
    try {
      let e = await Ue(),
        t = await Zu();
      A.contextMenus.update(Oa, { visible: t && e.isShowContextMenu });
    } catch {}
  }
  function hg() {
    A.tabs.onActivated.addListener(function (e) {
      Ur({}), Zx(e.tabId), cg(e.tabId);
    }),
      A.tabs.onRemoved.addListener(function (e) {
        bg(e), Yu(e);
      }),
      A.tabs.onUpdated.addListener(function (e) {
        $u(e), lo(e);
      });
  }
  var ba = new Map();
  async function $u(e) {
    let t = await co(),
      a = Date.now();
    if (t == "Translated") {
      if (ba.has(e)) return;
      let n = await A.tabs.get(e),
        r = fg(n.url);
      if (!r) return;
      ba.set(e, { duration: 0, lastActiveTime: a, hostname: r });
      return;
    }
    bg(e);
  }
  function fg(e) {
    if (!e) return null;
    try {
      return new URL(e).hostname;
    } catch {
      return null;
    }
  }
  async function Zx(e) {
    let t = await co(),
      a = Date.now();
    for (let [n, r] of ba)
      !(await A.tabs.get(n)).active &&
        r.lastActiveTime &&
        ((r.duration += a - r.lastActiveTime), (r.lastActiveTime = null));
    if (t == "Translated") {
      if (ba.has(e)) {
        let i = ba.get(e);
        i.lastActiveTime = a;
        return;
      }
      let n = await A.tabs.get(e),
        r = fg(n.url);
      if (!r) return;
      ba.set(e, { duration: 0, lastActiveTime: a, hostname: r });
      return;
    }
  }
  function bg(e) {
    if (ba.has(e)) {
      let t = ba.get(e),
        a = t.duration;
      t.lastActiveTime && (a += Date.now() - t.lastActiveTime),
        ba.delete(e),
        $x(t.hostname, a);
    }
  }
  async function Xx(e) {
    let a = (await A.tabs.query({ currentWindow: !0, active: !0 }))[0].id;
    return _a().sendMessage(`content_script:main:${a}`, e);
  }
  function $x(e, t) {
    Hn(e, e, t), ew(t);
  }
  async function co() {
    try {
      return await Xx({
        method: "getPageStatus",
        data: { trigger: "shortcut" },
      });
    } catch {
      return null;
    }
  }
  var mo = "readTimeDay";
  async function ew(e) {
    try {
      let t = new Date(),
        n = new Date(t.getFullYear(), t.getMonth(), t.getDate()).getTime(),
        r = await Ne(mo, null);
      if (!r) {
        await Se(mo, { day: n, time: e });
        return;
      }
      if (r.day !== n) {
        Hn("day." + De, "day." + De, r.time), await Se(mo, { day: n, time: e });
        return;
      }
      let i = { day: n, time: r.time + e };
      await Se(mo, i);
    } catch (t) {
      I.error(t);
    }
  }
  async function yg(e, t, a, n) {
    if (!chrome?.offscreen) return !1;
    try {
      (await chrome.runtime.getContexts({})).find(
        (o) => o.contextType === "OFFSCREEN_DOCUMENT"
      ) ||
        (await chrome.offscreen.createDocument({
          url: "offscreen.html",
          reasons: ["WORKERS"],
          justification: "ocr translate img",
        })),
        chrome.runtime.sendMessage({
          target: "offscreen",
          data: {
            context: e,
            type: "trigger",
            urlHash: t,
            mimeType: a,
            imgBuffer: n,
          },
        });
    } catch {
      return !1;
    }
    return !0;
  }
  function po() {
    Ue()
      .then((e) => {
        el(e), tl(e);
      })
      .catch((e) => {
        I.error("create menu error", e);
      });
  }
  async function el(e) {
    try {
      let t = await tw(e);
      I.debug(`updateUninstallUrl: ${t}`),
        t && A.runtime.setUninstallURL && A.runtime.setUninstallURL(t);
    } catch (t) {
      I.error("setUninstallUrl error", t);
    }
  }
  async function tw(e) {
    try {
      if ((e || (e = await Ue()), !e.uninstallUrl)) return;
      let t = await Ut.get(bt, null),
        { installedAt: a, fakeUserId: n } = await oa(),
        r = new URL(e.uninstallUrl);
      e.interfaceLanguage &&
        r.searchParams.set("interface_language", e.interfaceLanguage),
        e.targetLanguage &&
          r.searchParams.set("target_language", e.targetLanguage);
      let i = ka.parse(globalThis.navigator.userAgent),
        o = Hm(a);
      r.searchParams.set("is_new_user_today", o ? "1" : "0"),
        r.searchParams.set("install_day", _i(Bn(new Date(a)))),
        r.searchParams.set("version", rt()),
        i.browser &&
          (r.searchParams.set("browser_name", i.browser.name || "unknown"),
          r.searchParams.set(
            "browser_version",
            i.browser.version || "unknown"
          ));
      let s = li(t);
      r.searchParams.set("user_type", s?.user_type || "anonymous"),
        r.searchParams.set("device_id", n || "unknown");
      let u = await Gt();
      r.searchParams.set("t", u), r.searchParams.set("ab_tag", u);
      let c = await Qa();
      return r.searchParams.set("campaign", c || "none"), r.toString();
    } catch (t) {
      return I.error("getUninstallUrl error", t), e?.uninstallUrl;
    }
  }
  function tl(e) {
    e
      ? Xu(e)
      : Ue()
          .then((t) => {
            Xu(t);
          })
          .catch((t) => {
            I.error("create menu error", t);
          });
  }
  function vg() {
    A.runtime.onInstalled.addListener((e) => {
      I.debug(`onInstalled reason: ${e.reason}`), I.debug(e);
      let t = A.runtime.getManifest().version;
      e.reason == "install"
        ? (async () => {
            let a = Wn,
              r = A.runtime.getURL("").startsWith("safari"),
              i = !1,
              o = !1;
            try {
              let d = await A.runtime.getPlatformInfo();
              if (r) d.os === "mac" ? (i = !0) : d.os === "ios" && (o = !0);
              else if (d.os === "android") {
                if ($n()) {
                  let m = er();
                  if (m && Ht(m, "1.0.2")) {
                    po();
                    return;
                  }
                }
                a = Wn + "mobile/";
              }
            } catch {}
            i ? (a = Wn + "safari/step-1/") : o && (a = Wn + "ios/step-2/");
            let s = await Ne("onboardingDisplayTime", ""),
              u = await Gt(),
              c = await rt(),
              l = new URL(a);
            l.searchParams.set("t", u),
              l.searchParams.set("v", c),
              r
                ? s ||
                  (await Se("onboardingDisplayTime", new Date().toISOString()),
                  A.tabs.create({ url: l.toString() }))
                : A.tabs.create({ url: l.toString() }),
              po();
          })()
        : (e.reason == "update" && e.previousVersion && t != e.previousVersion,
          po());
    });
  }
  var go,
    aw = async function (e, t) {
      let { method: a, data: n } = e;
      if ((I.debug("background received message", a, n || " "), a === "mock"))
        await xa(150);
      else {
        if (a === "queryParagraphCache") return zs(n);
        if (a === "setParagraphCache") return Os(n);
        if (a === "queryAllTermsMeta") return Bi(!1);
        if (a === "queryTerms") return hr(n, !1);
        if (a === "saveTerms") return Pn(n, !1);
        if (a === "deleteTerms") return gr(n, !1);
        if (a === "calculateSize") return Fm();
        if (a === "fetch") return Ca(n);
        if (a === "getConfig") return n?.userAgent && Dc(n.userAgent), Ue();
        if (a === "getLocalConfig") return Ja();
        if (a === "openOptionsPage") {
          let r = "";
          n && n.pageRoute && (r = n.pageRoute);
          let i = A.runtime.getURL("options.html");
          A.tabs.create({ url: i + r });
        } else if (a === "openAboutPage")
          A.tabs.create({ url: A.runtime.getURL("options.html#about") });
        else if (a === "openInTab") n && A.tabs.create({ url: n });
        else if (a === "openEbookViewerPage") {
          let i = ue().EBOOK_VIEWER_URL;
          A.tabs.create({ url: i });
        } else if (a === "openSubtitleBuilderPage") {
          let i = ue().SUBTITLE_BUILDER_URL;
          A.tabs.create({ url: i });
        } else if (a === "openEbookBuilderPage") {
          let i = ue().EBOOK_BUILDER_URL;
          A.tabs.create({ url: i });
        } else if (a === "openHtmlViewerPage") {
          let i = ue().HTML_VIEWER_URL;
          A.tabs.create({ url: i });
        } else if (a === "openPdfViewerPage") {
          let r = n?.url,
            i = ro(r);
          r || (i = ue().PDF_VIEWER_URL), A.tabs.create({ url: i });
        } else {
          if (a === "setLocalConfig") return Ma(n);
          if (a == "getUserConfig") return ua();
          if (a == "setUserConfig") return Ft(n);
          if (a === "detectLanguage") {
            let { text: r } = n;
            if (A.i18n && A.i18n.detectLanguage)
              try {
                let i = await xg(A.i18n.detectLanguage(r), 1500, {
                  isReliable: !1,
                  languages: [],
                });
                return i.languages.length > 0
                  ? Ua(i.languages[0].language)
                  : "auto";
              } catch (i) {
                return I.debug("detect language error", i), "auto";
              }
            else return "auto";
          } else if (a === "detectTabLanguage")
            try {
              let r = await xg(A.tabs.detectLanguage(t.id), 1500, "auto");
              return Ua(r);
            } catch (r) {
              return I.debug("detect tab language error, use auto ", r), "auto";
            }
          else if (a === "autoSyncLatestConfig") {
            try {
              await ig();
            } catch (r) {
              I.debug("auto sync latest config error", r);
            }
            return "";
          } else if (a !== "updateCommands")
            if (a === "setBadge") {
              let r = t.id,
                i = n && n.text ? n.text : "";
              r && (Ur({}), nw(r, i)), r && $u(r);
            } else if (a == "getDelay") {
              let { key: r, options: i } = n || {};
              return yr.getDelay(r, i, !0);
            } else if (a === "getIsSupportIsOnToolbar")
              try {
                return A?.action?.getUserSettings
                  ? (await A.action.getUserSettings(), !0)
                  : !1;
              } catch {
                return !1;
              }
            else if (a === "getIsOnToolbar")
              try {
                return (await A.action.getUserSettings()).isOnToolbar;
              } catch {
                return !1;
              }
            else if (a == "reportOptionsPageView") sg(n);
            else if (a === "updateVideoSubtitleStatus") {
              let r = t.id;
              r && lo(r);
            } else if (a === "updateToggleTranslateContextMenu") Ur(n);
            else if (a === "updateImageMenu") gg();
            else if (a == "triggerClientTranslateImage") {
              let { mimeType: r, imgBuffer: i, urlHash: o, context: s } = n;
              return yg(s, o, r, i);
            } else if (a === "updateUninstallUrl") el();
            else if (a === "toggleTranslatePage") {
              let { trigger: r, currentPageStatus: i } = n;
              await iw({
                method: "toggleTranslatePage",
                data: { trigger: r, currentPageStatus: i },
              });
            } else {
              if (a == "isOpenSidePanel") return $s();
              if (a === "toggleSidePanel") {
                let r = t.id;
                zi({ isOpen: n?.isOpen, tabId: r });
              } else if (a === "queryInDB")
                try {
                  return Us(n.dbName, n.key);
                } catch {
                  return null;
                }
              else if (a === "addInDB") return qs(n.dbName, n.value);
            }
        }
      }
    };
  function wg() {
    _a();
    let e = A.runtime.getManifest();
    if (
      (e.manifest_version > 2,
      e.manifest_version === 2 &&
        A.webRequest &&
        A.webRequest.onBeforeSendHeaders)
    ) {
      let t = so.map((n) => n.condition.urlFilter),
        a = so.reduce(
          (n, r) => (
            r.condition.resourceTypes.forEach((i) => {
              n.includes(i) || n.push(i);
            }),
            n
          ),
          []
        );
      A.webRequest.onBeforeSendHeaders.addListener(
        function (n) {
          if (
            !(n.originUrl && n.originUrl.startsWith("http")) &&
            n.originUrl &&
            n.requestHeaders
          )
            for (let r = 0; r < t.length; r++) {
              let i = so[r];
              if (i.condition.urlFilter && Sn(n.url, i.condition.urlFilter))
                return {
                  requestHeaders: og(n.requestHeaders, i.action.requestHeaders),
                };
            }
        },
        { urls: t, types: a },
        ["blocking", "requestHeaders"]
      );
    }
  }
  function _a() {
    return (
      go || ((go = new sa("background", !1).getConnection("main", aw)), go)
    );
  }
  function ho(e, t) {
    return t === "dark"
      ? {
          32: `${e}/dark-32.png`,
          48: `${e}/dark-48.png`,
          64: `${e}/dark-64.png`,
          128: `${e}/dark-128.png`,
          256: `${e}/dark-256.png`,
        }
      : {
          32: `${e}/32.png`,
          48: `${e}/48.png`,
          64: `${e}/64.png`,
          128: `${e}/128.png`,
          256: `${e}/256.png`,
        };
  }
  async function nw(e, t) {
    if (Ze()) return;
    let a = await rw();
    a !== null &&
      (t
        ? A.browserAction && A.browserAction.setIcon
          ? A.browserAction.setIcon({ tabId: e, path: ho("badge-icons", a) })
          : A.action &&
            A.action.setIcon &&
            A.action.setIcon({ tabId: e, path: ho("badge-icons", a) })
        : A.browserAction && A.browserAction.setIcon
        ? A.browserAction.setIcon({ tabId: e, path: ho("icons", a) })
        : A.action &&
          A.action.setIcon &&
          A.action.setIcon({ tabId: e, path: ho("icons", a) }));
  }
  async function rw() {
    if (A.theme && A.theme.getCurrent) {
      let e = await A.theme.getCurrent();
      if (e.properties && e.properties.color_scheme)
        return e.properties.color_scheme;
      if (e.properties && e.properties.color_scheme === null) return null;
    }
    return "light";
  }
  function xg(e, t, a) {
    return new Promise((n, r) => {
      let i = setTimeout(() => {
        n(a);
      }, t);
      e.then((o) => {
        clearTimeout(i), n(o);
      }).catch((o) => {
        clearTimeout(i), r(o);
      });
    });
  }
  async function iw(e) {
    let a = (await A.tabs.query({ currentWindow: !0, active: !0 }))[0].id;
    _a()
      .sendMessage(`content_script:main:${a}`, e)
      .catch((r) => {
        I.error("send content message request failed", e, r);
      });
  }
  function Ag() {
    typeof A.commands < "u" &&
      A.commands.onCommand.addListener(async (e, t) => {
        if (
          (I.debug(`received command: ${e}`),
          ["toggleTranslatePage"].includes(e))
        ) {
          let a = await A.tabs.query({ active: !0, currentWindow: !0 });
          if (a.length === 0 || typeof a[0].id > "u") return;
          let r = a[0].url;
          if ($p(r)) {
            A.tabs.create({ url: ro(r) });
            return;
          }
        }
        if (["toggleSidePanel"].includes(e)) {
          let a = e0();
          a || zi({ isOpen: !1, tabId: t?.id }),
            await ln({
              method: "toggleSidePanel",
              data: { trigger: "shortcut", isOpen: a },
            });
          return;
        }
        await ln({ method: e, data: { trigger: "shortcut" } });
      });
  }
  async function ln(e) {
    let a = (await A.tabs.query({ currentWindow: !0, active: !0 }))[0].id;
    _a()
      .sendMessage(`content_script:main:${a}`, e)
      .catch((r) => {
        I.error("send content message request failed", e, r);
      });
  }
  async function ow(e, t, a) {
    let n = (s) => {
        e.postMessage(JSON.stringify({ data: s }));
      },
      r = (s) => {
        e.postMessage(
          JSON.stringify({
            error: {
              name: s.name,
              status: s.status,
              message: s.message,
              stack: s.stack,
            },
          })
        ),
          e.disconnect();
      },
      o = {
        ...t,
        onMessage: n,
        onError: r,
        onFinish: (s) => {
          e.postMessage(JSON.stringify({ finish: !0, reason: s })),
            e.disconnect();
        },
        signal: a,
      };
    try {
      await Ca(o);
    } catch (s) {
      if (s?.message.includes("aborted")) return;
      r(s);
    }
  }
  function kg() {
    A.runtime.onConnect.addListener((e) => {
      switch ((I.debug(`received connect: ${e.name}`), e.name)) {
        case "fetchStream":
          let t = new AbortController(),
            { signal: a } = t;
          e.onMessage.addListener(function (n) {
            let { type: r, options: i } = JSON.parse(n);
            switch (r) {
              case "abort":
                t.abort();
                break;
              case "start":
                ow(e, i, a);
                break;
            }
          });
          return;
      }
    });
  }
  var sw = As(null);
  async function lw(e, t, a) {
    a = a || (await tu());
    let n = a.aiAssistants || [],
      r = !1;
    if (e == "edit" && Eg(t))
      for (let o = n.length - 1; o >= 0; o--)
        n[o].id === t.id && ((n[o] = t), (r = !0));
    else if (e === "add" && Eg(t)) {
      for (let o = n.length - 1; o >= 0; o--)
        n[o].id === t.id && n.splice(o, 1);
      n.push(t), (r = !0);
    } else {
      for (let o = n.length - 1; o >= 0; o--)
        n[o].id === t.id && n.splice(o, 1);
      r = !0;
    }
    a.aiAssistants = n.sort((o, s) => o.priority - s.priority);
    let i = await ua();
    i.aiAssistantIds = [...new Set(n.map((o) => o.id))];
    try {
      await a0(a), await Ft(i);
    } catch {
      return !1;
    }
    return r;
  }
  async function cw(e, t) {
    (
      await Promise.allSettled(
        e.map((n) => wt({ url: `${To}api/plugins/${n}.json` }))
      )
    ).forEach((n) => {
      if (n.status === "fulfilled") {
        let r = n.value;
        r && lw("add", r, t);
      }
    });
  }
  async function dw(e, t, a = !0) {
    e || (e = await Ue()), t || (t = await tu());
    let n = t.aiAssistants || [],
      r = (e.aiAssistantIds || []).filter((s) => !n.find((u) => u.id === s)),
      i = [];
    if (a) {
      let s = await hw();
      i = await gw(t, s);
    }
    let o = [...new Set([...r, ...i])].filter((s) => !s.startsWith("custom"));
    o.length !== 0 && cw(o, t);
  }
  var mw = ar(dw, 1500);
  function Eg(e) {
    return Ht(Xn(), e.extensionVersion);
  }
  function pw(e, t) {
    return t ? !Ht(e.version, t) : !1;
  }
  function gw(e, t) {
    let a = [];
    return (
      (e.aiAssistants || []).forEach((n) => {
        pw(n, t[n.id]?.version) && a.push(n.id);
      }),
      [...new Set(a)]
    );
  }
  async function hw() {
    return (await wt({ url: `${To}api/plugins/meta.json` }))?.plugins || {};
  }
  var fw = 1e3 * 3600 * 24;
  async function Sg(e) {
    try {
      let t = e?.cacheCleanIntervalDay ?? 1,
        a = e?.cacheMaxAgeDay ?? 30,
        n = new Date(),
        r = await A.storage.local.get(null),
        i = r[ni],
        o = r[Bo];
      if (
        (o || ((o = n.getTime()), await A.storage.local.set({ [Bo]: o })),
        i || ((i = n.getTime()), await A.storage.local.set({ [ni]: i })),
        n.getTime() - i >= t * fw)
      ) {
        let s = await Bm(a, o);
        await A.storage.local.set({ [ni]: n.getTime() });
      }
    } catch {}
  }
  async function Tg() {
    try {
      if (fe()) return;
      let e = A.runtime.getURL("locales.json"),
        a = await (await fetch(e)).json();
      Object.assign(Ia, a);
    } catch {}
  }
  wg();
  vg();
  Ag();
  kg();
  A.contextMenus && pg();
  async function bw() {
    await Tg(), A.contextMenus && tl();
    let e = await Ue();
    if ((Sg(e), e.joinJobs.length)) {
      let t = Ul.replace(
        "{jobs}",
        e.joinJobs.map((a) => `    \u2022 ${a}`).join(`
`)
      );
    }
    hg(), e.debug && I.setLevel("debug");
  }
  bw().catch((e) => {});
})();
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
