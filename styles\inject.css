:root {
  --immersive-translate-theme-underline-borderColor: #72ece9;
  --immersive-translate-theme-nativeUnderline-borderColor: #72ece9;
  --immersive-translate-theme-nativeDashed-borderColor: #72ece9;
  --immersive-translate-theme-nativeDotted-borderColor: #72ece9;
  --immersive-translate-theme-highlight-backgroundColor: #ffff00;
  --immersive-translate-theme-dashed-borderColor: #59c1bd;
  --immersive-translate-theme-blockquote-borderColor: #cc3355;
  --immersive-translate-theme-thinDashed-borderColor: #ff374f;
  --immersive-translate-theme-dashedBorder-borderColor: #94a3b8;
  --immersive-translate-theme-dashedBorder-borderRadius: 0;
  --immersive-translate-theme-solidBorder-borderColor: #94a3b8;
  --immersive-translate-theme-solidBorder-borderRadius: 0;
  --immersive-translate-theme-dotted-borderColor: #94a3b8;
  --immersive-translate-theme-wavy-borderColor: #72ece9;
  --immersive-translate-theme-dividingLine-borderColor: #94a3b8;
  --immersive-translate-theme-grey-textColor: #2f4f4f;
  --immersive-translate-theme-marker-backgroundColor: #fbda41;
  --immersive-translate-theme-marker-backgroundColor-rgb: 251, 218, 65;
  --immersive-translate-theme-marker2-backgroundColor: #ffff00;
  --immersive-translate-theme-background-backgroundColor: #dbafaf;
  --immersive-translate-theme-background-backgroundColor-rgb: 219, 175, 175;
  --immersive-translate-theme-background-backgroundOpacity: 12;
  --immersive-translate-theme-opacity-opacity: 10;
}

[imt-state="dual"] .immersive-translate-target-translation-pre-whitespace {
  white-space: pre-wrap !important;
}

[imt-state="dual"] .immersive-translate-pdf-target-container {
  position: absolute;
  background-color: #fff;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica,
    sans-serif;
  top: 0;
  width: 600px;
  height: 100%;
  z-index: 2;
  line-height: 1.3;
  font-size: 16px;
}
[imt-state="dual"] .immersive-translate-target-wrapper[dir="rtl"] {
  text-align: right;
}

[imt-state="dual"]
  .immersive-translate-pdf-target-container
  .immersive-translate-target-wrapper {
  color: rgb(0, 0, 0);
  white-space: normal;
  position: absolute;
}

[imt-state="dual"]
  .immersive-translate-pdf-target-container
  .immersive-translate-target-wrapper
  font {
  color: inherit;
  white-space: inherit;
  position: unset;
}

[imt-state="translation"] .immersive-translate-target-wrapper > br {
  display: none;
}

[imt-state="translation"]
  .immersive-translate-target-translation-block-wrapper {
  margin: 0 !important;
}

[imt-state="dual"] .immersive-translate-target-translation-block-wrapper {
  margin: 8px 0 !important;
  display: inline-block;
}

[imt-trans-position="before"]
  .immersive-translate-target-translation-block-wrapper {
  display: block;
}

[imt-trans-position="before"]
  .immersive-translate-target-translation-block-wrapper {
  margin-top: 0 !important;
}

[imt-state="dual"] .immersive-translate-target-translation-pdf-block-wrapper {
  margin: 0 !important;
  display: inline-block;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-grey-inner {
  color: var(--immersive-translate-theme-grey-textColor);
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-underline-inner {
  border-bottom: 1px solid
    var(--immersive-translate-theme-underline-borderColor) !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-nativeUnderline-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-nativeUnderline-borderColor
  ) !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-dashedBorder-borderRadius
  ) !important;
  padding: 6px;
  margin-top: 2px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-inline-wrapper-theme-dashedBorder {
  border: 1px dashed var(--immersive-translate-theme-dashedBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-dashedBorder-borderRadius
  ) !important;
  padding: 2px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-solidBorder-borderRadius
  ) !important;
  padding: 6px;
  margin-top: 2px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-inline-wrapper-theme-solidBorder {
  border: 1px solid var(--immersive-translate-theme-solidBorder-borderColor) !important;
  border-radius: var(
    --immersive-translate-theme-solidBorder-borderRadius
  ) !important;
  padding: 2px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-nativeDashed-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-nativeDashed-borderColor
  ) !important;
  text-decoration-style: dashed !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-thinDashed-inner {
  border-bottom: 1px dashed
    var(--immersive-translate-theme-thinDashed-borderColor) !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-dotted-inner {
  background-image: linear-gradient(
    to right,
    var(--immersive-translate-theme-dotted-borderColor) 30%,
    rgba(255, 255, 255, 0) 0%
  );
  background-position: bottom;
  background-size: 5px 1px;
  background-repeat: repeat-x;
  padding-bottom: 3px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-nativeDotted-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-nativeDotted-borderColor
  ) !important;
  text-decoration-style: dotted !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-wavy-inner {
  text-decoration: underline !important;
  text-decoration-color: var(
    --immersive-translate-theme-wavy-borderColor
  ) !important;
  text-decoration-style: wavy !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-dashed-inner {
  background: linear-gradient(
      to right,
      var(--immersive-translate-theme-dashed-borderColor) 0%,
      var(--immersive-translate-theme-dashed-borderColor) 50%,
      transparent 50%,
      transparent 100%
    )
    repeat-x left bottom;
  background-size: 8px 2px;
  padding-bottom: 2px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-dividingLine::before {
  content: "";
  display: block;
  max-width: 80px;
  width: 10%;
  border-top: 1px dashed
    var(--immersive-translate-theme-dividingLine-borderColor);
  padding-top: 8px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-inline-wrapper-theme-dividingLine::before {
  content: "";
  border-left: 1px dashed
    var(--immersive-translate-theme-dividingLine-borderColor);
  max-height: 16px;
  height: 16px;
  padding-left: 8px;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-highlight-inner {
  background: var(--immersive-translate-theme-highlight-backgroundColor);
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-marker {
  line-height: 1.5em;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-marker2-inner {
  font-weight: bold;
  text-shadow: 10px 0px 3px
      var(--immersive-translate-theme-marker2-backgroundColor),
    16px 3px 9px var(--immersive-translate-theme-marker2-backgroundColor),
    2px 0px 6px var(--immersive-translate-theme-marker2-backgroundColor),
    -12px 0px 12px var(--immersive-translate-theme-marker2-backgroundColor) !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-marker-inner {
  /* TODO: add more texture */
  background: linear-gradient(
    to right,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.1),
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 3%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 35%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.9) 70%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.8) 95%,
    rgba(var(--immersive-translate-theme-marker-backgroundColor-rgb), 0.3)
  );
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-weakening {
  opacity: 0.618 !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-italic {
  font-style: italic !important;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-bold {
  font-weight: bold !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-paper {
  margin: 8px 0;
  box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
  padding: 16px 32px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-blockquote {
  border-left: 4px solid var(--immersive-translate-theme-blockquote-borderColor) !important;
  padding-left: 12px !important;
  margin-top: 4px;
  margin-bottom: 4px;
  padding-top: 4px;
  padding-bottom: 4px;
  display: inline-block;
}

[imt-state="dual"] .immersive-translate-target-translation-theme-mask-inner {
  filter: blur(5px) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

[data-immersive-translate-root-translation-theme="none"]
  .immersive-translate-target-translation-theme-mask-inner {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="mask"]
  .immersive-translate-target-inner {
  filter: blur(5px) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

/* opacity theme start */

[imt-state="dual"] .immersive-translate-target-translation-theme-opacity-inner {
  filter: opacity(
    calc(var(--immersive-translate-theme-opacity-opacity) * 1%)
  ) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

[data-immersive-translate-root-translation-theme="none"]
  .immersive-translate-target-translation-theme-opacity-inner {
  filter: none !important;
}
[data-immersive-translate-root-translation-theme="opacity"]
  .immersive-translate-target-inner,
[imt-state="dual"]
  .immersive-translate-target-translation-theme-opacity-inner:hover {
  filter: opacity(
    calc(var(--immersive-translate-theme-opacity-opacity) * 1%)
  ) !important;
  transition: filter 0.3s ease !important;
  border-radius: 10px;
  display: inline-block;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-opacity-inner:hover {
  filter: none !important;
}

[imt-state="dual"]
  .immersive-translate-target-translation-theme-mask-inner:hover {
  filter: none !important;
}
[data-immersive-translate-root-translation-theme="opacity"]
  .immersive-translate-target-inner:hover {
  filter: none !important;
}

[data-immersive-translate-root-translation-theme="mask"]
  .immersive-translate-target-inner:hover {
  filter: none !important;
}

/* opacity theme end */

/* background theme start */
[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper-theme-background {
  margin: 8px 0;
  background: rgba(
    var(--immersive-translate-theme-background-backgroundColor-rgb),
    calc(var(--immersive-translate-theme-background-backgroundOpacity) * 1%)
  );
  border-radius: 4px;
  box-shadow: unset !important;
  padding: 12px;
  display: inline-block;
}
[imt-state="dual"]
  .immersive-translate-target-translation-theme-background-inner {
  background: rgba(
    var(--immersive-translate-theme-background-backgroundColor-rgb),
    calc(var(--immersive-translate-theme-background-backgroundOpacity) * 1%)
  );
  padding-left: 6px;
  padding-right: 6px;
  box-decoration-break: clone;
  -webkit-box-decoration-break: clone;
}
[imt-state="dual"]
  .immersive-translate-target-translation-block-wrapper
  .immersive-translate-target-translation-theme-background-inner {
  background: unset;
  padding-left: unset;
  padding-right: unset;
}
/* background theme end */

/* vertical css , please remain it in the last one. */
.immersive-translate-target-translation-vertical-block-wrapper {
  margin: 0px 8px !important;
}

.immersive-translate-text {
  font-size: 15px !important;
}

.immersive-translate-error-toast {
  position: fixed;
  top: 5%;
  z-index: 99999999;
  left: 0;
  right: 0;
  margin: auto;
  max-width: 300px;
  padding: 16px;
  border-radius: 12px;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

@media all and (min-width: 750px) {
  .immersive-translate-error-toast {
    max-width: 400px;
  }
}

.immersive-translate-clickable-button {
  cursor: pointer;
}

.immersive-translate-help-button {
  cursor: pointer;
}

.immersive-translate-loading-text:before {
  content: "...";
}

/* dark mode for loading */

@media only screen and (prefers-color-scheme: dark) {
  .immersive-translate-loading {
    border: 2px rgba(255, 255, 255, 0.25) solid !important;
    border-top: 2px rgba(255, 255, 255, 1) solid !important;
  }
}

.immersive-translate-error-wrapper {
  position: relative;
  display: inline-flex;
  padding: 6px;
  margin: 0 12px;
  white-space: nowrap;
  font-size: 0.9em;
}
[lang="zh-CN"] .immersive-translate-error-wrapper {
  font-size: 0.75em;
}
[lang="zh-TW"] .immersive-translate-error-wrapper {
  font-size: 0.75em;
}

.immersive-translate-tooltip {
  position: relative;
  display: inline-flex;
  /* little indicater to indicate it's hoverable */
}

.immersive-translate-tooltip-content {
  /* here's the magic */
  position: absolute;
  z-index: 100000000000;

  left: 50%;
  bottom: 0;
  transform: translate(-50%, 110%);
  line-height: 1;
  /* and add a small left margin */

  /* basic styles */
  width: max-content;
  max-width: 250px;
  word-wrap: break-word;
  white-space: pre-line;
  padding: 10px;
  border-radius: 10px;
  background: #000c;
  color: #fff;
  text-align: center;
  font-size: 14px;
  display: none;
  /* hide by default */
}

.immersive-translate-tooltip:hover .immersive-translate-tooltip-content {
  display: inline-block;
}

.immersive-translate-tooltip:hover + .immersive-translate-tooltip-content {
  display: inline-block;
}

.immersive-translate-tooltip-content-table {
  left: unset !important;
  bottom: unset !important;
  transform: translate(-10%, 50%) !important;
}

.immersive-translate-tooltip:hover:before {
  display: inline-block;
}

.immersive-translate-loading-spinner {
  vertical-align: middle !important;
  width: 10px !important;
  height: 10px !important;
  display: inline-block !important;
  margin: 0 4px !important;
  border: 2px rgba(221, 244, 255, 0.6) solid !important;
  border-top: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-left: 2px rgba(0, 0, 0, 0.375) solid !important;
  border-radius: 50% !important;
  padding: 0 !important;
  -webkit-animation: immersive-translate-loading-animation 0.6s infinite linear !important;
  animation: immersive-translate-loading-animation 0.6s infinite linear !important;
}

@-webkit-keyframes immersive-translate-loading-animation {
  from {
    -webkit-transform: rotate(0deg);
  }

  to {
    -webkit-transform: rotate(359deg);
  }
}

@keyframes immersive-translate-loading-animation {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(359deg);
  }
}

.imt-image-status {
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--bg-2, #fff);
  font-size: 14px;
}
