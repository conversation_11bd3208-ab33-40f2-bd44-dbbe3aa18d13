(() => {
  var k1 = {
    manifest_version: 3,
    name: "__MSG_brandName__",
    description: "__MSG_brandDescription__",
    version: "1.19.6",
    default_locale: "en",
    background: { service_worker: "background.js" },
    web_accessible_resources: [
      "styles/inject.css",
      "pdf/index.html",
      "video-subtitle/inject.js",
      "image/inject.js",
      "tesseract/worker.min.js",
      "tesseract/tesseract-core-simd-lstm.wasm.js",
      "browser-bridge/inject.js",
      "side-panel.html",
    ],
    content_security_policy: {
      extension_pages:
        "script-src 'self' 'wasm-unsafe-eval'; object-src 'self'",
    },
    content_scripts: [
      {
        matches: ["<all_urls>", "file:///*", "*://*/*"],
        js: ["content_script.js"],
        run_at: "document_start",
        all_frames: !0,
      },
      {
        matches: ["<all_urls>", "file:///*", "*://*/*"],
        js: ["content_start.js"],
        run_at: "document_start",
        all_frames: !0,
      },
    ],
    commands: {
      toggleTranslatePage: {
        suggested_key: { default: "Alt+A" },
        description: "__MSG_toggleTranslatePageOfficialPage__",
      },
      toggleTranslateTheWholePage: {
        suggested_key: { default: "Alt+W" },
        description: "__MSG_toggleTranslateTheWholePage__",
      },
      toggleOnlyTransation: { description: "__MSG_toggleOnlyTransation__" },
      toggleTranslateToThePageEndImmediately: {
        description: "__MSG_toggleTranslateToThePageEndImmediately__",
      },
      toggleTranslateTheMainPage: {
        description: "__MSG_toggleTranslateTheMainPage__",
      },
      toggleTranslationMask: { description: "__MSG_toggleTranslationMask__" },
      shareToDraft: { description: "__MSG_shareToDraft__" },
      toggleMouseHoverTranslateDirectly: {
        description: "__MSG_toggleMouseHoverTranslateDirectly__",
      },
      translateWithOpenAI: { description: "__MSG_translateWithOpenAI__" },
      translateWithDeepL: { description: "__MSG_translateWithDeepL__" },
      translateWithGoogle: { description: "__MSG_translateWithGoogle__" },
      translateWithTransmart: { description: "__MSG_translateWithTransmart__" },
      translateWithGemini: { description: "__MSG_translateWithGemini__" },
      translateWithBing: { description: "__MSG_translateWithBing__" },
      translateWithClaude: { description: "__MSG_translateWithClaude__" },
      translateInputBox: { description: "__MSG_translateInputBox__" },
      toggleSidePanel: {
        suggested_key: { default: "Alt+S" },
        description: "__MSG_toggleSidePanel__",
      },
      translateWithCustom1: { description: "__MSG_translateWithCustom1__" },
      translateWithCustom2: { description: "__MSG_translateWithCustom2__" },
      translateWithCustom3: { description: "__MSG_translateWithCustom3__" },
      toggleVideoSubtitlePreTranslation: {
        description: "__MSG_toggleVideoSubtitlePreTranslation__",
      },
    },
    options_page: "options.html",
    options_ui: { page: "options.html", open_in_tab: !0, browser_style: !1 },
    permissions: [
      "storage",
      "activeTab",
      "contextMenus",
      "webRequest",
      "webRequestBlocking",
      "declarativeNetRequestWithHostAccess",
      "declarativeNetRequestFeedback",
      "declarativeNetRequest",
      "offscreen",
      "sidePanel",
    ],
    host_permissions: ["<all_urls>"],
    declarative_net_request: {
      rule_resources: [
        {
          id: "ruleset_1",
          enabled: !0,
          path: "rules/request_modifier_rule.json",
        },
      ],
    },
    action: {
      default_popup: "popup.html",
      default_icon: {
        32: "icons/32.png",
        48: "icons/48.png",
        64: "icons/64.png",
        128: "icons/128.png",
        256: "icons/256.png",
      },
    },
    browser_action: {
      default_icon: "icons/32.png",
      default_popup: "popup.html",
      theme_icons: [
        { dark: "icons/32.png", light: "icons/dark-32.png", size: 32 },
        { dark: "icons/48.png", light: "icons/dark-48.png", size: 48 },
        { dark: "icons/64.png", light: "icons/dark-64.png", size: 64 },
        { dark: "icons/128.png", light: "icons/dark-128.png", size: 128 },
        { dark: "icons/256.png", light: "icons/dark-256.png", size: 256 },
      ],
    },
    icons: {
      32: "icons/32.png",
      48: "icons/48.png",
      64: "icons/64.png",
      128: "icons/128.png",
      256: "icons/256.png",
    },
    browser_specific_settings: {
      gecko: {
        id: "{5efceaa7-f3a2-4e59-a54b-85319448e305}",
        strict_min_version: "63.0",
      },
      gecko_android: { strict_min_version: "113.0" },
    },
    key: "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA7JPn78UfqI3xIIOPPLPS74UTzLfJL1gQM8hlk/deKWvFP/WqUBnPJPdhQeF45sFpI1OjO70nFqdATT4/RwYAiZK7G/E6m27MDVnhHjszfzReOuoAEn9J3RnE2xEx5pFhRFcelhnwTTLrrn90aaPcaMtNsgXtZA1Ggz/SnX9I4ZygqpJYjx3Ql2t6SyNK222oRQiKMT93Rrjgyc8RFA7FKXsWglG0TvseRjbmG5Jk5gDx+2/YTcWGqCDotQnWnkPj/dBO23UAX7IpyJK3FGYdkvWFih6OVClHIIWY8mfCjjwSGbXNQNesaa9F2hrzBZ5MRTj4m7yj76mGxuPHPIE8mwIDAQAB",
  };
  var a_ = "";
  function An() {
    return a_ || k1.version;
  }
  function t0() {
    return Se.runtime.getManifest().version;
  }
  function Wo(e, t) {
    let n = di(e),
      r = di(t);
    return n >= r;
  }
  function di(e) {
    let t = e.split(".").reverse(),
      n = 0,
      r = 1;
    for (let i = 0; i < 3; i++) (n += r * Number(t[i] || "0")), (r *= 100);
    return n;
  }
  function S1() {
    let e = t0();
    return Wo(e, "1.18.2");
  }
  var aY = 1e3 * 3600 * 24;
  async function D1(e, t) {
    return await new Promise((n, r) => {
      let i = e,
        o = 1,
        l = indexedDB.open(i, o);
      (l.onsuccess = (s) => {
        n(l.result);
      }),
        (l.onerror = (s) => {
          r();
        }),
        (l.onupgradeneeded = (s) => {
          let u = l.result;
          (t || ["cache"]).forEach((d) => {
            u.objectStoreNames.contains(d) ||
              u.createObjectStore(d, { keyPath: "key" });
          });
        });
    });
  }
  async function C1(e, t) {
    let n = await D1("onnx", ["models"]);
    return new Promise((r, i) => {
      let l = n.transaction(["models"], "readwrite").objectStore("models"),
        s = { key: e, data: t },
        u = l.put(s);
      (u.onsuccess = function () {
        r("Model stored successfully");
      }),
        (u.onerror = function (a) {
          r("Error storing model:" + a.target.errorCode);
        });
    });
  }
  async function I1(e) {
    let t = await D1("onnx", ["models"]);
    return new Promise((n, r) => {
      let l = t
        .transaction(["models"], "readonly")
        .objectStore("models")
        .get(e);
      (l.onsuccess = function (s) {
        s.target.result ? n(s.target.result.data) : n(null);
      }),
        (l.onerror = function (s) {
          n(null);
        });
    });
  }
  var { InferenceSession: _1 } = Na;
  Na.env.wasm.wasmPaths =
    "https://cdn.jsdelivr.net/npm/onnxruntime-web@1.20.1/dist/";
  var i_ = [1, 3, 640, 640],
    o_ = 0.2,
    n0;
  async function M1(e, t, n) {
    try {
      let r = await s_(e, t);
      if (!n0) {
        ss;
        let l = await P1("preprocess-yolo", n),
          s = await P1("best", n),
          u = await _1.create(s),
          a = await _1.create(l);
        n?.("detecting"), (n0 = { net: u, prep: a }), ss;
      }
      ss;
      let i = document.createElement("canvas");
      n?.("recognizing");
      let o = await Py(r, i, n0, o_, i_);
      return n?.("saved"), o;
    } catch (r) {
      throw r;
    }
  }
  function s_(e, t) {
    return new Promise((n, r) => {
      let i = new Blob([t], { type: e }),
        o = URL.createObjectURL(i),
        l = new Image();
      (l.src = o),
        (l.onload = function () {
          let s = document.createElement("canvas"),
            u = s.getContext("2d");
          (s.width = l.width),
            (s.height = l.height),
            u.drawImage(l, 0, 0),
            u_(s),
            s.toBlob((a) => {
              let d = URL.createObjectURL(a);
              n(d), URL.revokeObjectURL(o);
            }, e);
        }),
        (l.onerror = function () {
          r(new Error("Failed to load image"));
        });
    });
  }
  function u_(e) {
    let t = e.getContext("2d"),
      r = t.getImageData(0, 0, e.width, e.height).data,
      i = e.width,
      o = e.height,
      s = Math.floor(3 / 2),
      u = t.createImageData(i, o),
      a = u.data;
    for (let d = 0; d < o; d++)
      for (let c = 0; c < i; c++) {
        let p = 0,
          m = 0,
          g = 0,
          f = 0;
        for (let b = -s; b <= s; b++)
          for (let x = -s; x <= s; x++) {
            let y = d + b,
              S = c + x;
            if (S >= 0 && S < i && y >= 0 && y < o) {
              let B = (y * i + S) * 4;
              (p += r[B]), (m += r[B + 1]), (g += r[B + 2]), f++;
            }
          }
        let v = (d * i + c) * 4;
        (a[v] = p / f),
          (a[v + 1] = m / f),
          (a[v + 2] = g / f),
          (a[v + 3] = r[v + 3]);
      }
    t.putImageData(u, 0, 0);
  }
  var l_ = "https://s.immersivetranslate.com/assets/r2-uploads/models/";
  async function P1(e, t) {
    let n = await I1(e);
    return (
      n ||
        (t?.("model_downloading"),
        (n = await _y(`${l_}${e}.onnx`, [
          `Loading ${e}`,
          ({ text: r, progress: i }) => {},
        ])),
        n && (await C1(e, n))),
      n
    );
  }
  var cn = {};
  chrome.runtime.onMessage.addListener((e, t, n) => {
    if (e.target === "offscreen")
      switch (e.data.type) {
        case "trigger":
          return d_(e.data), !1;
        case "state":
          return c_(n, e.data), !0;
        default:
          throw new Error("Unrecognized message:", e.type);
      }
  });
  function c_(e, t) {
    let { urlHash: n } = t;
    cn[n] || (cn[n] = { state: "extension_uploading" }),
      e({ state: cn[n].state, errorMsg: cn[n].errorMsg, result: cn[n].result });
  }
  async function d_(e) {
    let { mimeType: t, imgBuffer: n, urlHash: r } = e;
    (!cn[r] || cn[r].state == "error") &&
      (cn[r] = { ...cn[r], state: "extension_uploading", errorMsg: "" });
    let i = Object.values(n),
      l = new Uint8Array(i).buffer;
    try {
      let s = await M1(t, l, (u) => {
        (cn[r].state = u), Ce.debug("imgState", u);
      });
      cn[r].result = s;
    } catch (s) {
      Ce.error(s), (cn[r].state = "error"), (cn[r].errorMsg = s.message);
    }
  }
})();
/*! Bundled license information:

onnxruntime-web/dist/ort.bundle.min.mjs:
  (*!
   * ONNX Runtime Web v1.20.1
   * Copyright (c) Microsoft Corporation. All rights reserved.
   * Licensed under the MIT License.
   *)

onnxruntime-web/dist/ort.bundle.min.mjs:
  (*! Bundled license information:
  
  long/index.js:
    (**
     * @license
     * Copyright 2009 The Closure Library Authors
     * Copyright 2020 Daniel Wirtz / The long.js Authors.
     *
     * Licensed under the Apache License, Version 2.0 (the "License");
     * you may not use this file except in compliance with the License.
     * You may obtain a copy of the License at
     *
     *     http://www.apache.org/licenses/LICENSE-2.0
     *
     * Unless required by applicable law or agreed to in writing, software
     * distributed under the License is distributed on an "AS IS" BASIS,
     * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
     * See the License for the specific language governing permissions and
     * limitations under the License.
     *
     * SPDX-License-Identifier: Apache-2.0
     *)
  *)
*/
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
