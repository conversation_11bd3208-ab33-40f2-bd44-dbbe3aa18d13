(() => {
  var tf = class extends Yo {
    constructor(t) {
      super(t), (this.state = { hasError: !1 });
    }
    error;
    info;
    static getDerivedStateFromError(t) {
      return { hasError: !0 };
    }
    componentDidCatch(t, n) {
      (this.error = t), (this.info = n);
    }
    render() {
      return this.state.hasError
        ? (I.error("ErrorBoundary", this.error, this.info),
          C(b$, { error: this.error, info: this.info }))
        : this.props.children;
    }
  };
  function b$({ error: e, info: t }) {
    let { t: n } = re(),
      r =
        location.protocol.includes("extension") &&
        location.href.includes("options.html");
    return C("div", {
      class: "error-boundary",
      children: [
        e?.message,
        " ",
        JSON.stringify(t?.componentStack),
        C("div", {
          style: { marginTop: 10 },
          children: [
            n("error.configError"),
            C("a", {
              style: { marginLeft: 6 },
              href: r
                ? location.href.replace(/#.+/, "#import_export")
                : "https://dash.immersivetranslate.com/#import_export",
              target: r ? "" : "_blank",
              children: n("error.goReset"),
            }),
          ],
        }),
      ],
    });
  }
  function $9() {
    let [e, t] = V("Original"),
      n = de(""),
      { t: r } = re(),
      [a, i, o, s] = Ta(() => {
        setTimeout(() => {
          On("updateGlobalContext", !1)();
        }, 250);
      });
    Du(a, i);
    let [l, u] = V(null),
      [c, d] = V(null),
      [m, p] = V(null),
      [g, h] = V("auto"),
      [x, f] = V(null),
      [y, b] = V({ visible: !1, preview: !1 }),
      v = de(!1),
      S = de(),
      [T, E] = V({}),
      A = de(!1),
      [{ errorMsg: D, disableButton: F, noPermissionPDF: N }, B] = V({
        errorMsg: "",
        disableButton: !1,
        noPermissionPDF: !1,
      });
    K(() => {
      Re().any && E({ width: "100%" });
    }, []),
      K(() => {
        Ot().then((U) => u(U));
      }, [a]),
      K(() => {
        if (!(!c || !l)) {
          if (v.current && S.current) {
            S.current.getAsyncContextString().then((U) => {
              if (U) {
                let W = JSON.parse(U);
                f(W);
              }
            });
            return;
          }
          gr({ url: c, config: l }).then((U) => {
            v.current || f(U);
          });
        }
      }, [c, l, v, S]),
      K(
        () => (
          O(),
          () => {
            globalThis.document.removeEventListener(La, P);
          }
        ),
        []
      );
    let _ = ke((U, W) => {
      On("change_translate_service", !1, {
        translation_service: `${U}_${W}`,
      })();
    }, []);
    if (
      (K(() => {
        !x ||
          A.current ||
          ((A.current = !0),
          On("popupEventReport", !1, {
            key: "show_popup",
            events: [{ name: "show_popup", params: {} }],
          })());
      }, [x]),
      !l || !x)
    )
      return null;
    return C(tf, {
      children: C(G0, {
        type: "popup",
        style: T,
        aiContextInfo: y,
        onClose: rR,
        onToggleTranslate: On("toggleTranslatePage", !0, {
          currentPageStatus: e,
        }),
        onToggleEnabled: L,
        openOptionsPage: z,
        openAboutPage: G,
        openSharePage: C$,
        openRewardCenter: async () => {
          try {
            await cS({ forceOpen: !0, openRewardCenter: !0 });
          } catch {
            S.current?.openRewardCenter();
          }
          globalThis.close();
        },
        onTranslatePdf: () => {
          w(n.current || c || "");
        },
        onChangeService: _,
        onTranslateTheMainPage: On("translateTheMainPage"),
        onTranslateTheWholePage: On("translateTheWholePage"),
        onTranslateToThePageEndImmediately: On(
          "translateToThePageEndImmediately"
        ),
        onSwitchTranslationMode: (U) => {
          On("switchTranslationMode", !0, { mode: U })();
        },
        onTranslatePage: On("translatePage"),
        onRestorePage: On("restorePage", !1),
        onWebReport: On("webReport"),
        onSetPageLanguage: H,
        setSettings: i,
        config: l,
        pageStatus: e,
        ctx: x,
        currentUrl: c,
        currentLang: g,
        onSetLocalConfig: zt,
        onSetBuildinConfig: Mp,
        request: qa,
        errorMsg: D,
        disabledButton: F,
        noPermissionPDF: N,
        onAutoEnableSubtitleChanged: On("autoEnableSubtitleChanged"),
        onToggleEnableEditTranslation: On("toggleEnableEditTranslation"),
        updateContextState: (U) => On("updateContextState", !1, U)(),
        onOpenUrl: S$,
        onUpdateFloatBallEnable: On("updateFloatBallEnable"),
        onReport: (U) => {
          On("popupEventReport", !1, U)();
        },
      }),
    });
    async function O() {
      let U = h0(),
        W = await $.tabs.query({ currentWindow: !0, active: !0 }),
        Q = W[0].id;
      p(Q);
      let J = nR(Q, U);
      S.current = J;
      let se = W[0].url;
      if ((R(Q), globalThis.document.addEventListener(La, P), !se)) {
        d("about:newtab");
        return;
      }
      d(se),
        Xp(se)
          ? (M(J, Q, se),
            aR(Q, async () => {
              let ye = await J.getContextString(),
                ee = await J.getPageStatus(),
                te = await Ot(),
                me = await J.getCurrentPageLanguage(),
                we = await S.current?.getAIContextInfo();
              if (
                (b({
                  visible: !!we?.used,
                  preview: we?.sourceProgram == "html",
                }),
                t(ee),
                u(te),
                h(me),
                ye)
              ) {
                let ue = JSON.parse(ye);
                if (ue) {
                  if ((f(ue), (v.current = !0), Mi(ue.rule))) {
                    let Y = await J.getPdfWebUrl();
                    n.current = Y;
                  }
                } else I.error("contextStr is empty", ye);
              }
              me === "auto" &&
                setTimeout(async () => {
                  let ue = await J.getCurrentPageLanguage();
                  h(ue);
                }, 500);
            }))
          : P3(se || "")
          ? v$(se || "") &&
            y$(se || "").then((ye) => {
              ye ||
                B({
                  disableButton: !1,
                  noPermissionPDF: !0,
                  errorMsg: r("noLocalFilePermissionForPDF"),
                });
            })
          : B({
              disableButton: !0,
              errorMsg: r("noPermissionForThisPage"),
              noPermissionPDF: !1,
            });
    }
    function R(U) {
      $.tabs.onUpdated.addListener((W, Q, J) => {
        U === W && J.url && d(J.url);
      });
    }
    function M(U, W, Q) {
      U.ping()
        .then((J) => {
          B({ disableButton: !1, errorMsg: "", noPermissionPDF: !1 }),
            (X9[W] = !0),
            iR(W);
        })
        .catch((J) => {
          let se =
            "Could not establish connection. Receiving end does not exist.";
          sn() && J.message === se
            ? B({
                disableButton: !0,
                errorMsg: r("noPermissionForThisPage"),
                noPermissionPDF: !0,
              })
            : jw(Q)
            ? B({
                disableButton: !0,
                errorMsg: r("noPermissionForLocalFile"),
                noPermissionPDF: !1,
              })
            : Ow(Q) ||
              B({
                disableButton: !0,
                errorMsg: r("reloadCurrentPage"),
                noPermissionPDF: !1,
              }),
            I.debug(
              "ping failed, but it is ok. cause maybe content is not injected",
              J
            );
        });
    }
    function P(U) {
      let { tabId: W, payload: Q } = U.detail,
        { method: J, data: se } = Q;
      I.debug("popup received message", J, se || " "),
        J === "setPageStatus"
          ? W && t(se)
          : J === "ready" &&
            W &&
            (B({ disableButton: !1, errorMsg: "", noPermissionPDF: !1 }),
            (X9[W] = !0),
            iR(W));
    }
    function w(U) {
      U && ($.tabs.create({ url: l5(U) }), globalThis.close());
    }
    function L() {
      i((U) => ({ ...U, enabled: !U.enabled })),
        setTimeout(() => {
          rR();
        }, 50);
    }
    function H(U) {
      let W = h0();
      if ((h(U), m && c)) {
        let Q = Dd(c, U, l.sourceLanguageUrlPattern);
        i((J) => ({ ...J, sourceLanguageUrlPattern: Q })),
          aR(m, () => {
            nR(m, W).setCurrentPageLanguageByClient(U);
          });
      }
    }
    function z(U = !1, W = "") {
      if (l?.useOnlineOptions)
        return globalThis.open(Te.OPTIONS_URL + W), Promise.resolve();
      let Q = $.runtime.getURL("options.html");
      $.tabs.create({ url: Q + W }),
        setTimeout(() => {
          globalThis.close();
        }, 50);
    }
    function G() {
      if (l?.useOnlineOptions)
        return globalThis.open(Te.OPTIONS_URL + "#about"), Promise.resolve();
      $.tabs.create({ url: $.runtime.getURL("options.html#about") }),
        setTimeout(() => {
          globalThis.close();
        }, 50);
    }
  }
  function On(e, t = !0, n = {}) {
    return async () => {
      let r = h0(),
        i = (await $.tabs.query({ currentWindow: !0, active: !0 }))[0].id,
        o = { trigger: "popup", ...n };
      r.sendMessage(`content_script:main:${i}`, { method: e, data: o }),
        t &&
          setTimeout(() => {
            globalThis.close();
          }, 10);
    };
  }
  async function y$(e) {
    try {
      let t = new URL(e),
        n = await fetch(t.href);
      return !0;
    } catch {
      return !1;
    }
  }
  function v$(e) {
    try {
      return new URL(e).protocol === "file:";
    } catch {
      return !1;
    }
  }
  var x$ = {
    setCurrentPageLanguageByClient: (e) => Promise.resolve(),
    ping: (e) => Promise.resolve(),
    getPageStatus: (e) => Promise.resolve(),
    getCurrentPageLanguage: (e) => Promise.resolve(),
    getContextString: (e) => Promise.resolve(),
    getAsyncContextString: (e) => Promise.resolve(),
    getPdfWebUrl: () => Promise.resolve(""),
    getAIContextInfo: () => Promise.resolve({}),
    toggleSidePanel: () => Promise.resolve(),
    openRewardCenter: () => Promise.resolve(),
  };
  function nR(e, t) {
    function n(a, i) {
      let o = `content_script:main_sync:${e}`;
      return (
        a.includes("Async") && (o = `content_script:main:${e}`),
        t.sendMessage(o, { method: a, data: i })
      );
    }
    let r = { ...x$ };
    return (
      Object.keys(r).forEach((a) => {
        r[a] = n.bind(null, a);
      }),
      r
    );
  }
  var C$ = () => {
    On("shareToDraft")();
  };
  function S$(e, t) {
    let n = e;
    t &&
      (n = `${e}?utm_source=extension&utm_medium=extension&utm_campaign=${t}`),
      $.tabs.create({ url: n }),
      setTimeout(() => {
        globalThis.close();
      }, 50);
  }
  function rR() {
    globalThis.close();
  }
  var el = {},
    X9 = {};
  function aR(e, t) {
    if (X9[e])
      try {
        t();
      } catch (n) {
        I.error("run callback failed", n);
      }
    else el[e] || (el[e] = []), el[e].push(t);
  }
  function iR(e) {
    if (el[e] && el[e].length) {
      let t = [...el[e]];
      (el[e] = []), t.forEach((n) => n());
    }
  }
  async function oR() {
    try {
      if (X()) return;
      let e = $.runtime.getURL("locales.json"),
        n = await (await fetch(e)).json();
      Object.assign(To, n);
    } catch {}
  }
  var sR = document.getElementById("mount");
  Uw();
  sR &&
    (async () => {
      let e = await Ot();
      await oR(),
        e.debug && I.setLevel("debug"),
        _r(C(Qa, { lang: e.interfaceLanguage, children: C($9, {}) }), sR);
    })();
})();
/*! Bundled license information:

bowser/src/bowser.js:
  (*!
   * Bowser - a browser detector
   * https://github.com/lancedikson/bowser
   * MIT License | (c) Dustin Diaz 2012-2015
   * MIT License | (c) Denis Demchenko 2015-2019
   *)
*/
/*! Bundled license information:

dompurify/dist/purify.es.js:
  (*! @license DOMPurify 3.0.3 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.0.3/LICENSE *)
*/
/*! Bundled license information:

js-yaml/dist/js-yaml.mjs:
  (*! js-yaml 4.1.0 https://github.com/nodeca/js-yaml @license MIT *)
*/
/*!
 * Toastify js 1.12.0
 * https://github.com/apvarun/toastify-js
 * @license MIT licensed
 *
 * Copyright (C) 2018 Varun A P
 */
/*! Bundled license information:

lottie-web/build/player/lottie.js:
  (*!
   Transformation Matrix v2.0
   (c) Epistemex 2014-2015
   www.epistemex.com
   By Ken Fyrstenberg
   Contributions by leeoniya.
   License: MIT, header required.
   *)
*/
